//
//  ContentView.swift
//  PartTimeJob
//
//  Created by K4 on 2025/8/29.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var userPreferences = UserPreferences.shared
    @StateObject private var notificationManager = NotificationManager.shared

    var body: some View {
        MainTabView()
            .environmentObject(userPreferences)
            .environmentObject(notificationManager)
            .preferredColorScheme(userPreferences.appTheme.colorScheme)
            .accentColor(userPreferences.themeColor)
    }
}

#Preview {
    ContentView()
        .environmentObject(DataManager.shared)
}
