//
//  CacheService.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation

/// 数据缓存服务 - 提高应用性能
@MainActor
class CacheService: ObservableObject {
    static let shared = CacheService()
    
    // MARK: - 缓存存储
    private var workRecordsCache: [String: [WorkRecord]] = [:]
    private var workTypesCache: [WorkType]?
    private var statisticsCache: [String: Any] = [:]
    
    // MARK: - 缓存时间戳
    private var workRecordsCacheTime: [String: Date] = [:]
    private var workTypesCacheTime: Date?
    private var statisticsCacheTime: [String: Date] = [:]
    
    // MARK: - 缓存配置
    private let cacheExpireTime: TimeInterval = 300 // 5分钟
    private let maxCacheSize = 100 // 最大缓存条目数
    
    private init() {}
    
    // MARK: - 工作记录缓存
    
    /// 获取缓存的工作记录
    func getCachedWorkRecords(key: String) -> [WorkRecord]? {
        guard let cacheTime = workRecordsCacheTime[key],
              Date().timeIntervalSince(cacheTime) < cacheExpireTime,
              let records = workRecordsCache[key] else {
            return nil
        }
        return records
    }
    
    /// 缓存工作记录
    func cacheWorkRecords(_ records: [WorkRecord], key: String) {
        // 清理过期缓存
        cleanExpiredCache()
        
        // 限制缓存大小
        if workRecordsCache.count >= maxCacheSize {
            removeOldestCache()
        }
        
        workRecordsCache[key] = records
        workRecordsCacheTime[key] = Date()
    }
    
    /// 生成工作记录缓存键
    func generateWorkRecordsCacheKey(
        startDate: Date? = nil,
        endDate: Date? = nil,
        workTypeId: UUID? = nil
    ) -> String {
        var components: [String] = ["work_records"]
        
        if let startDate = startDate {
            components.append("start_\(Int(startDate.timeIntervalSince1970))")
        }
        
        if let endDate = endDate {
            components.append("end_\(Int(endDate.timeIntervalSince1970))")
        }
        
        if let workTypeId = workTypeId {
            components.append("type_\(workTypeId.uuidString)")
        }
        
        return components.joined(separator: "_")
    }
    
    // MARK: - 工作类型缓存
    
    /// 获取缓存的工作类型
    func getCachedWorkTypes() -> [WorkType]? {
        guard let cacheTime = workTypesCacheTime,
              Date().timeIntervalSince(cacheTime) < cacheExpireTime,
              let workTypes = workTypesCache else {
            return nil
        }
        return workTypes
    }
    
    /// 缓存工作类型
    func cacheWorkTypes(_ workTypes: [WorkType]) {
        workTypesCache = workTypes
        workTypesCacheTime = Date()
    }
    
    // MARK: - 统计数据缓存
    
    /// 获取缓存的统计数据
    func getCachedStatistics<T>(key: String, type: T.Type) -> T? {
        guard let cacheTime = statisticsCacheTime[key],
              Date().timeIntervalSince(cacheTime) < cacheExpireTime,
              let data = statisticsCache[key] as? T else {
            return nil
        }
        return data
    }
    
    /// 缓存统计数据
    func cacheStatistics<T>(_ data: T, key: String) {
        statisticsCache[key] = data
        statisticsCacheTime[key] = Date()
    }
    
    /// 生成统计数据缓存键
    func generateStatisticsCacheKey(
        type: StatisticsType,
        startDate: Date? = nil,
        endDate: Date? = nil
    ) -> String {
        var components: [String] = ["stats", type.rawValue]
        
        if let startDate = startDate {
            components.append("start_\(Int(startDate.timeIntervalSince1970))")
        }
        
        if let endDate = endDate {
            components.append("end_\(Int(endDate.timeIntervalSince1970))")
        }
        
        return components.joined(separator: "_")
    }
    
    // MARK: - 缓存管理
    
    /// 清除所有缓存
    func clearAllCache() {
        workRecordsCache.removeAll()
        workRecordsCacheTime.removeAll()
        workTypesCache = nil
        workTypesCacheTime = nil
        statisticsCache.removeAll()
        statisticsCacheTime.removeAll()
    }
    
    /// 清除工作记录缓存
    func clearWorkRecordsCache() {
        workRecordsCache.removeAll()
        workRecordsCacheTime.removeAll()
    }
    
    /// 清除工作类型缓存
    func clearWorkTypesCache() {
        workTypesCache = nil
        workTypesCacheTime = nil
    }
    
    /// 清除统计数据缓存
    func clearStatisticsCache() {
        statisticsCache.removeAll()
        statisticsCacheTime.removeAll()
    }
    
    /// 清理过期缓存
    private func cleanExpiredCache() {
        let now = Date()
        
        // 清理过期的工作记录缓存
        let expiredWorkRecordKeys = workRecordsCacheTime.compactMap { key, time in
            now.timeIntervalSince(time) >= cacheExpireTime ? key : nil
        }
        
        for key in expiredWorkRecordKeys {
            workRecordsCache.removeValue(forKey: key)
            workRecordsCacheTime.removeValue(forKey: key)
        }
        
        // 清理过期的工作类型缓存
        if let cacheTime = workTypesCacheTime,
           now.timeIntervalSince(cacheTime) >= cacheExpireTime {
            workTypesCache = nil
            workTypesCacheTime = nil
        }
        
        // 清理过期的统计数据缓存
        let expiredStatsKeys = statisticsCacheTime.compactMap { key, time in
            now.timeIntervalSince(time) >= cacheExpireTime ? key : nil
        }
        
        for key in expiredStatsKeys {
            statisticsCache.removeValue(forKey: key)
            statisticsCacheTime.removeValue(forKey: key)
        }
    }
    
    /// 移除最旧的缓存
    private func removeOldestCache() {
        guard let oldestKey = workRecordsCacheTime.min(by: { $0.value < $1.value })?.key else {
            return
        }
        
        workRecordsCache.removeValue(forKey: oldestKey)
        workRecordsCacheTime.removeValue(forKey: oldestKey)
    }
    
    /// 获取缓存统计信息
    func getCacheStats() -> CacheStats {
        return CacheStats(
            workRecordsCacheCount: workRecordsCache.count,
            hasWorkTypesCache: workTypesCache != nil,
            statisticsCacheCount: statisticsCache.count,
            totalMemoryUsage: estimateMemoryUsage()
        )
    }
    
    /// 估算内存使用量（简单估算）
    private func estimateMemoryUsage() -> Int {
        var size = 0
        
        // 工作记录缓存大小估算
        for records in workRecordsCache.values {
            size += records.count * 200 // 每个记录大约200字节
        }
        
        // 工作类型缓存大小估算
        if let workTypes = workTypesCache {
            size += workTypes.count * 100 // 每个类型大约100字节
        }
        
        // 统计数据缓存大小估算
        size += statisticsCache.count * 50 // 每个统计项大约50字节
        
        return size
    }
}

// MARK: - 辅助类型

/// 统计类型枚举
enum StatisticsType: String, CaseIterable {
    case totalIncome = "total_income"
    case totalHours = "total_hours"
    case averageHourlyRate = "average_hourly_rate"
    case incomeByWorkType = "income_by_work_type"
    case monthlyIncome = "monthly_income"
    case weeklyIncome = "weekly_income"
    case workDaysCount = "work_days_count"
}

/// 缓存统计信息
struct CacheStats {
    let workRecordsCacheCount: Int
    let hasWorkTypesCache: Bool
    let statisticsCacheCount: Int
    let totalMemoryUsage: Int
    
    var formattedMemoryUsage: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .memory
        return formatter.string(fromByteCount: Int64(totalMemoryUsage))
    }
}
