//
//  MigrationService.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation
import SwiftData

/// 数据库迁移服务
@MainActor
class MigrationService: ObservableObject {
    private let dataManager = DataManager.shared
    
    // MARK: - 版本管理
    
    /// 当前数据库版本
    static let currentVersion = "1.0.0"
    
    /// 获取已安装的数据库版本
    private var installedVersion: String {
        return UserDefaults.standard.string(forKey: "DatabaseVersion") ?? "0.0.0"
    }
    
    /// 设置数据库版本
    private func setDatabaseVersion(_ version: String) {
        UserDefaults.standard.set(version, forKey: "DatabaseVersion")
    }
    
    // MARK: - 迁移检查和执行
    
    /// 检查是否需要迁移
    func needsMigration() -> Bool {
        return installedVersion != Self.currentVersion
    }
    
    /// 执行数据库迁移
    func performMigration() -> MigrationResult {
        let fromVersion = installedVersion
        let toVersion = Self.currentVersion
        
        print("🔄 开始数据库迁移: \(fromVersion) -> \(toVersion)")
        
        do {
            // 根据版本执行相应的迁移
            switch fromVersion {
            case "0.0.0":
                try migrateFromFreshInstall()
            case "1.0.0":
                // 已是最新版本，无需迁移
                break
            default:
                // 未知版本，执行完整重置
                try migrateFromUnknownVersion()
            }
            
            // 更新版本号
            setDatabaseVersion(toVersion)
            
            print("✅ 数据库迁移完成")
            return MigrationResult(
                success: true,
                fromVersion: fromVersion,
                toVersion: toVersion,
                message: "数据库迁移成功完成"
            )
            
        } catch {
            print("❌ 数据库迁移失败: \(error)")
            return MigrationResult(
                success: false,
                fromVersion: fromVersion,
                toVersion: toVersion,
                message: "数据库迁移失败: \(error.localizedDescription)"
            )
        }
    }
    
    // MARK: - 具体迁移方法
    
    /// 从全新安装迁移
    private func migrateFromFreshInstall() throws {
        print("📱 执行全新安装初始化")
        
        // 创建默认工作类型
        let defaultWorkTypes = WorkType.createDefaultWorkTypes()
        for workType in defaultWorkTypes {
            dataManager.addWorkType(workType)
        }
        
        // 设置默认用户偏好
        setDefaultUserPreferences()
    }
    
    /// 从未知版本迁移（重置）
    private func migrateFromUnknownVersion() throws {
        print("⚠️ 检测到未知数据库版本，执行完整重置")
        
        // 备份现有数据
        let backupService = DataSyncService()
        if let backupData = backupService.createBackup() {
            // 保存备份到临时位置（这里简化处理）
            print("💾 已创建数据备份")
        }
        
        // 重置数据库
        _ = backupService.resetAllData()
        
        // 重新初始化
        try migrateFromFreshInstall()
    }
    
    // MARK: - 辅助方法
    
    /// 设置默认用户偏好
    private func setDefaultUserPreferences() {
        let defaults = UserDefaults.standard
        
        // 设置默认值（如果尚未设置）
        if defaults.object(forKey: "WorkReminderEnabled") == nil {
            defaults.set(true, forKey: "WorkReminderEnabled")
        }
        
        if defaults.object(forKey: "StatisticsStartDay") == nil {
            defaults.set(1, forKey: "StatisticsStartDay") // 周一开始
        }
        
        if defaults.object(forKey: "DefaultSalaryType") == nil {
            defaults.set(SalaryType.hourly.rawValue, forKey: "DefaultSalaryType")
        }
        
        if defaults.object(forKey: "CurrencySymbol") == nil {
            defaults.set("¥", forKey: "CurrencySymbol")
        }
    }
    
    /// 验证数据完整性
    func validateDataIntegrity() -> ValidationResult {
        var issues: [String] = []
        
        // 检查工作类型
        let workTypes = dataManager.fetchWorkTypes()
        if workTypes.isEmpty {
            issues.append("没有可用的工作类型")
        }
        
        // 检查工作记录的工作类型引用
        let workRecords = dataManager.fetchWorkRecords()
        let workTypeIds = Set(workTypes.map { $0.id })
        
        for record in workRecords {
            if !workTypeIds.contains(record.workTypeId) {
                issues.append("工作记录 \(record.id) 引用了不存在的工作类型")
            }
        }
        
        // 检查数据一致性
        for record in workRecords {
            if record.startTime >= record.endTime {
                issues.append("工作记录 \(record.id) 的时间设置不正确")
            }
            
            if record.salary < 0 {
                issues.append("工作记录 \(record.id) 的薪资为负数")
            }
        }
        
        return ValidationResult(
            isValid: issues.isEmpty,
            message: issues.isEmpty ? "数据完整性检查通过" : issues.joined(separator: "\n")
        )
    }
    
    /// 修复数据问题
    func repairDataIssues() -> RepairResult {
        var repairedCount = 0
        var errors: [String] = []
        
        // 修复缺失的工作类型
        let workTypes = dataManager.fetchWorkTypes()
        if workTypes.isEmpty {
            let defaultWorkTypes = WorkType.createDefaultWorkTypes()
            for workType in defaultWorkTypes {
                dataManager.addWorkType(workType)
                repairedCount += 1
            }
        }
        
        // 修复无效的工作记录
        let workRecords = dataManager.fetchWorkRecords()
        let workTypeIds = Set(dataManager.fetchWorkTypes().map { $0.id })
        
        for record in workRecords {
            var needsUpdate = false
            
            // 修复无效的工作类型引用
            if !workTypeIds.contains(record.workTypeId) {
                if let defaultWorkType = workTypes.first {
                    record.workTypeId = defaultWorkType.id
                    needsUpdate = true
                    repairedCount += 1
                }
            }
            
            // 修复无效的时间设置
            if record.startTime >= record.endTime {
                record.endTime = Calendar.current.date(byAdding: .hour, value: 1, to: record.startTime) ?? record.endTime
                needsUpdate = true
                repairedCount += 1
            }
            
            // 修复负薪资
            if record.salary < 0 {
                record.salary = 0
                needsUpdate = true
                repairedCount += 1
            }
            
            if needsUpdate {
                dataManager.updateWorkRecord(record)
            }
        }
        
        return RepairResult(
            repairedCount: repairedCount,
            errors: errors,
            message: "修复了 \(repairedCount) 个数据问题"
        )
    }
}

// MARK: - 结果类型

/// 迁移结果
struct MigrationResult {
    let success: Bool
    let fromVersion: String
    let toVersion: String
    let message: String
}

/// 验证结果
struct ValidationResult {
    let isValid: Bool
    let message: String
}

/// 修复结果
struct RepairResult {
    let repairedCount: Int
    let errors: [String]
    let message: String
}
