//
//  DataSyncService.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation

/// 数据同步和备份服务
@MainActor
class DataSyncService: ObservableObject {
    private let dataManager = DataManager.shared
    
    // MARK: - 数据备份
    
    /// 创建数据备份
    func createBackup() -> BackupData? {
        let workRecords = dataManager.fetchWorkRecords()
        let workTypes = dataManager.fetchWorkTypes()
        
        let backup = BackupData(
            version: "1.0",
            createdAt: Date(),
            workRecords: workRecords.map { WorkRecordBackup(from: $0) },
            workTypes: workTypes.map { WorkTypeBackup(from: $0) }
        )
        
        return backup
    }
    
    /// 导出备份到JSON
    func exportBackupToJSON() -> Data? {
        guard let backup = createBackup() else { return nil }
        
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = .prettyPrinted
        
        do {
            return try encoder.encode(backup)
        } catch {
            print("❌ 导出备份失败: \(error)")
            return nil
        }
    }
    
    /// 从JSON恢复备份
    func restoreFromJSON(_ data: Data) -> RestoreResult {
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        do {
            let backup = try decoder.decode(BackupData.self, from: data)
            return restoreFromBackup(backup)
        } catch {
            print("❌ 解析备份文件失败: \(error)")
            return RestoreResult(success: false, message: "备份文件格式错误")
        }
    }
    
    /// 从备份数据恢复
    private func restoreFromBackup(_ backup: BackupData) -> RestoreResult {
        var restoredWorkTypes = 0
        var restoredWorkRecords = 0
        var errors: [String] = []
        
        // 恢复工作类型
        for workTypeBackup in backup.workTypes {
            do {
                let workType = workTypeBackup.toWorkType()
                
                // 检查是否已存在
                if dataManager.fetchWorkType(by: workType.id) == nil {
                    dataManager.addWorkType(workType)
                    restoredWorkTypes += 1
                }
            } catch {
                errors.append("恢复工作类型失败: \(workTypeBackup.name)")
            }
        }
        
        // 恢复工作记录
        for workRecordBackup in backup.workRecords {
            do {
                let workRecord = workRecordBackup.toWorkRecord()
                
                // 验证工作类型是否存在
                if dataManager.fetchWorkType(by: workRecord.workTypeId) != nil {
                    dataManager.addWorkRecord(workRecord)
                    restoredWorkRecords += 1
                } else {
                    errors.append("工作记录关联的工作类型不存在")
                }
            } catch {
                errors.append("恢复工作记录失败")
            }
        }
        
        let message = """
        恢复完成：
        - 工作类型: \(restoredWorkTypes)个
        - 工作记录: \(restoredWorkRecords)个
        \(errors.isEmpty ? "" : "\n错误: \(errors.joined(separator: ", "))")
        """
        
        return RestoreResult(
            success: errors.isEmpty || (restoredWorkTypes > 0 || restoredWorkRecords > 0),
            message: message
        )
    }
    
    // MARK: - 数据清理
    
    /// 清理已删除的数据
    func cleanupDeletedData() -> CleanupResult {
        // 这里可以实现永久删除软删除的数据
        // 由于使用SwiftData，暂时保留软删除机制
        return CleanupResult(deletedCount: 0, message: "数据清理完成")
    }
    
    /// 重置所有数据
    func resetAllData() -> Bool {
        do {
            // 删除所有工作记录
            let workRecords = dataManager.fetchWorkRecords()
            for record in workRecords {
                dataManager.permanentlyDeleteWorkRecord(record)
            }
            
            // 重新创建默认工作类型
            let defaultWorkTypes = WorkType.createDefaultWorkTypes()
            for workType in defaultWorkTypes {
                dataManager.addWorkType(workType)
            }
            
            return true
        } catch {
            print("❌ 重置数据失败: \(error)")
            return false
        }
    }
}

// MARK: - 备份数据结构

/// 备份数据结构
struct BackupData: Codable {
    let version: String
    let createdAt: Date
    let workRecords: [WorkRecordBackup]
    let workTypes: [WorkTypeBackup]
}

/// 工作记录备份结构
struct WorkRecordBackup: Codable {
    let id: UUID
    let date: Date
    let startTime: Date
    let endTime: Date
    let workTypeId: UUID
    let workDescription: String
    let salary: Double
    let salaryType: String
    let workHours: Double
    let notes: String
    let createdAt: Date
    let updatedAt: Date
    
    init(from workRecord: WorkRecord) {
        self.id = workRecord.id
        self.date = workRecord.date
        self.startTime = workRecord.startTime
        self.endTime = workRecord.endTime
        self.workTypeId = workRecord.workTypeId
        self.workDescription = workRecord.workDescription
        self.salary = workRecord.salary
        self.salaryType = workRecord.salaryType.rawValue
        self.workHours = workRecord.workHours
        self.notes = workRecord.notes
        self.createdAt = workRecord.createdAt
        self.updatedAt = workRecord.updatedAt
    }
    
    func toWorkRecord() -> WorkRecord {
        return WorkRecord(
            id: id,
            date: date,
            startTime: startTime,
            endTime: endTime,
            workTypeId: workTypeId,
            workDescription: workDescription,
            salary: salary,
            salaryType: SalaryType(rawValue: salaryType) ?? .hourly,
            notes: notes
        )
    }
}

/// 工作类型备份结构
struct WorkTypeBackup: Codable {
    let id: UUID
    let name: String
    let iconName: String
    let colorHex: String
    let defaultSalary: Double
    let defaultSalaryType: String
    let workDescription: String
    let sortOrder: Int
    let createdAt: Date
    let updatedAt: Date
    
    init(from workType: WorkType) {
        self.id = workType.id
        self.name = workType.name
        self.iconName = workType.iconName
        self.colorHex = workType.colorHex
        self.defaultSalary = workType.defaultSalary
        self.defaultSalaryType = workType.defaultSalaryType.rawValue
        self.workDescription = workType.workDescription
        self.sortOrder = workType.sortOrder
        self.createdAt = workType.createdAt
        self.updatedAt = workType.updatedAt
    }
    
    func toWorkType() -> WorkType {
        return WorkType(
            id: id,
            name: name,
            iconName: iconName,
            colorHex: colorHex,
            defaultSalary: defaultSalary,
            defaultSalaryType: SalaryType(rawValue: defaultSalaryType) ?? .hourly,
            description: workDescription,
            sortOrder: sortOrder
        )
    }
}

// MARK: - 结果结构

/// 恢复结果
struct RestoreResult {
    let success: Bool
    let message: String
}

/// 清理结果
struct CleanupResult {
    let deletedCount: Int
    let message: String
}
