//
//  ValidationService.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation

/// 数据验证服务
struct ValidationService {
    
    // MARK: - 工作记录验证
    
    /// 验证工作记录数据
    static func validateWorkRecord(
        date: Date,
        startTime: Date,
        endTime: Date,
        workTypeId: UUID,
        workDescription: String,
        salary: Double,
        salaryType: SalaryType
    ) -> ValidationResult {
        
        var errors: [ValidationError] = []
        
        // 验证日期
        if date > Date() {
            errors.append(.invalidDate("工作日期不能是未来日期"))
        }
        
        // 验证时间
        if startTime >= endTime {
            errors.append(.invalidTime("开始时间必须早于结束时间"))
        }
        
        // 验证工作时长（不能超过24小时）
        let workHours = endTime.timeIntervalSince(startTime) / 3600
        if workHours > 24 {
            errors.append(.invalidTime("工作时长不能超过24小时"))
        }
        
        if workHours < 0.1 {
            errors.append(.invalidTime("工作时长不能少于6分钟"))
        }
        
        // 验证工作描述
        if workDescription.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append(.invalidDescription("工作内容不能为空"))
        }
        
        if workDescription.count > 200 {
            errors.append(.invalidDescription("工作内容不能超过200个字符"))
        }
        
        // 验证薪资
        if salary < 0 {
            errors.append(.invalidSalary("薪资不能为负数"))
        }
        
        if salary > 10000 {
            errors.append(.invalidSalary("薪资不能超过10000元"))
        }
        
        // 验证时薪合理性
        if salaryType == .hourly && salary > 500 {
            errors.append(.invalidSalary("时薪不能超过500元/小时"))
        }
        
        // 验证日薪合理性
        if salaryType == .daily && salary > 2000 {
            errors.append(.invalidSalary("日薪不能超过2000元/天"))
        }
        
        return ValidationResult(isValid: errors.isEmpty, errors: errors)
    }
    
    /// 验证工作记录时间冲突
    static func validateTimeConflict(
        date: Date,
        startTime: Date,
        endTime: Date,
        excludingRecordId: UUID? = nil,
        existingRecords: [WorkRecord]
    ) -> ValidationResult {
        
        let targetDate = DateHelper.startOfDay(for: date)
        let conflictingRecords = existingRecords.filter { record in
            // 排除当前编辑的记录
            if let excludingId = excludingRecordId, record.id == excludingId {
                return false
            }
            
            // 检查是否是同一天
            let recordDate = DateHelper.startOfDay(for: record.date)
            return recordDate == targetDate
        }
        
        for record in conflictingRecords {
            // 检查时间是否重叠
            if timeRangesOverlap(
                start1: startTime, end1: endTime,
                start2: record.startTime, end2: record.endTime
            ) {
                return ValidationResult(
                    isValid: false,
                    errors: [.timeConflict("该时间段与已有工作记录冲突")]
                )
            }
        }
        
        return ValidationResult(isValid: true, errors: [])
    }
    
    // MARK: - 工作类型验证
    
    /// 验证工作类型数据
    static func validateWorkType(
        name: String,
        iconName: String,
        colorHex: String,
        defaultSalary: Double,
        defaultSalaryType: SalaryType,
        description: String
    ) -> ValidationResult {
        
        var errors: [ValidationError] = []
        
        // 验证名称
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        if trimmedName.isEmpty {
            errors.append(.invalidName("工作类型名称不能为空"))
        }
        
        if trimmedName.count > 20 {
            errors.append(.invalidName("工作类型名称不能超过20个字符"))
        }
        
        // 验证图标名称
        if iconName.isEmpty {
            errors.append(.invalidIcon("必须选择一个图标"))
        }
        
        // 验证颜色
        if !isValidHexColor(colorHex) {
            errors.append(.invalidColor("颜色格式不正确"))
        }
        
        // 验证默认薪资
        if defaultSalary < 0 {
            errors.append(.invalidSalary("默认薪资不能为负数"))
        }
        
        if defaultSalary > 10000 {
            errors.append(.invalidSalary("默认薪资不能超过10000元"))
        }
        
        // 验证描述
        if description.count > 100 {
            errors.append(.invalidDescription("描述不能超过100个字符"))
        }
        
        return ValidationResult(isValid: errors.isEmpty, errors: errors)
    }
    
    // MARK: - 辅助方法
    
    /// 检查两个时间段是否重叠
    private static func timeRangesOverlap(
        start1: Date, end1: Date,
        start2: Date, end2: Date
    ) -> Bool {
        return start1 < end2 && start2 < end1
    }
    
    /// 验证十六进制颜色格式
    private static func isValidHexColor(_ hex: String) -> Bool {
        let pattern = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"
        let regex = try? NSRegularExpression(pattern: pattern)
        let range = NSRange(location: 0, length: hex.utf16.count)
        return regex?.firstMatch(in: hex, options: [], range: range) != nil
    }
}

// MARK: - 验证结果和错误类型

/// 验证结果
struct ValidationResult {
    let isValid: Bool
    let errors: [ValidationError]
    
    var errorMessage: String {
        return errors.map { $0.message }.joined(separator: "\n")
    }
}

/// 验证错误类型
enum ValidationError {
    case invalidDate(String)
    case invalidTime(String)
    case invalidDescription(String)
    case invalidSalary(String)
    case invalidName(String)
    case invalidIcon(String)
    case invalidColor(String)
    case timeConflict(String)
    
    var message: String {
        switch self {
        case .invalidDate(let message),
             .invalidTime(let message),
             .invalidDescription(let message),
             .invalidSalary(let message),
             .invalidName(let message),
             .invalidIcon(let message),
             .invalidColor(let message),
             .timeConflict(let message):
            return message
        }
    }
}

// MARK: - 数据清理服务
extension ValidationService {
    /// 清理工作描述文本
    static func sanitizeWorkDescription(_ description: String) -> String {
        return description
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .replacingOccurrences(of: "\n+", with: "\n", options: .regularExpression)
    }
    
    /// 清理工作类型名称
    static func sanitizeWorkTypeName(_ name: String) -> String {
        return name
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
    }
    
    /// 格式化十六进制颜色
    static func formatHexColor(_ color: String) -> String {
        var hex = color.trimmingCharacters(in: .whitespacesAndNewlines)
        if !hex.hasPrefix("#") {
            hex = "#" + hex
        }
        return hex.uppercased()
    }
}
