//
//  CalendarDataService.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation
import SwiftUI

/// 日历数据服务 - 负责日历相关的数据预加载和缓存
@MainActor
class CalendarDataService: ObservableObject {
    /// 单例实例
    static let shared = CalendarDataService()
    
    /// 数据管理器
    private let dataManager = DataManager.shared
    
    /// 缓存服务
    private let cacheService = CacheService.shared
    
    /// 预加载的工作记录缓存
    @Published private var preloadedWorkRecords: [Date: [WorkRecord]] = [:]
    
    /// 当前加载的月份范围
    private var loadedMonthRange: (start: Date, end: Date)?
    
    /// 预加载任务
    private var preloadTask: Task<Void, Never>?
    
    private init() {}
    
    // MARK: - 公共接口
    
    /// 获取指定日期的工作记录
    func getWorkRecords(for date: Date) -> [WorkRecord] {
        let dayKey = DateHelper.startOfDay(for: date)
        
        // 先从预加载缓存获取
        if let cachedRecords = preloadedWorkRecords[dayKey] {
            return cachedRecords
        }
        
        // 从数据管理器获取
        let records = dataManager.fetchWorkRecords(for: date)
        
        // 缓存结果
        preloadedWorkRecords[dayKey] = records
        
        return records
    }
    
    /// 预加载指定月份的数据
    func preloadMonth(_ date: Date) {
        let monthStart = DateHelper.startOfMonth(for: date)
        let monthEnd = DateHelper.endOfMonth(for: date)
        
        // 检查是否已经加载过这个月份
        if let loadedRange = loadedMonthRange,
           monthStart >= loadedRange.start && monthEnd <= loadedRange.end {
            return
        }
        
        // 取消之前的预加载任务
        preloadTask?.cancel()
        
        // 开始新的预加载任务
        preloadTask = Task {
            await performPreload(from: monthStart, to: monthEnd)
        }
    }
    
    /// 预加载指定日期范围的数据
    func preloadDateRange(from startDate: Date, to endDate: Date) {
        preloadTask?.cancel()
        
        preloadTask = Task {
            await performPreload(from: startDate, to: endDate)
        }
    }
    
    /// 清除预加载缓存
    func clearPreloadedData() {
        preloadedWorkRecords.removeAll()
        loadedMonthRange = nil
        preloadTask?.cancel()
    }
    
    /// 刷新指定日期的数据
    func refreshData(for date: Date) {
        let dayKey = DateHelper.startOfDay(for: date)
        preloadedWorkRecords.removeValue(forKey: dayKey)
        
        // 重新加载数据
        let records = dataManager.fetchWorkRecords(for: date)
        preloadedWorkRecords[dayKey] = records
    }
    
    // MARK: - 私有方法
    
    /// 执行预加载
    private func performPreload(from startDate: Date, to endDate: Date) async {
        let calendar = Calendar.current
        var currentDate = startDate
        var loadedRecords: [Date: [WorkRecord]] = [:]
        
        // 批量加载数据
        let allRecords = dataManager.fetchWorkRecords(from: startDate, to: endDate)
        
        // 按日期分组
        let groupedRecords = Dictionary(grouping: allRecords) { record in
            DateHelper.startOfDay(for: record.date)
        }
        
        // 为每一天创建记录条目（包括空记录的日期）
        while currentDate <= endDate {
            let dayKey = DateHelper.startOfDay(for: currentDate)
            loadedRecords[dayKey] = groupedRecords[dayKey] ?? []
            
            guard let nextDate = calendar.date(byAdding: .day, value: 1, to: currentDate) else {
                break
            }
            currentDate = nextDate
        }
        
        // 更新缓存
        await MainActor.run {
            preloadedWorkRecords.merge(loadedRecords) { _, new in new }
            loadedMonthRange = (startDate, endDate)
        }
    }
    
    // MARK: - 统计数据预加载
    
    /// 预加载月度统计数据
    func preloadMonthlyStats(for date: Date) {
        let monthStart = DateHelper.startOfMonth(for: date)
        let monthEnd = DateHelper.endOfMonth(for: date)
        
        Task {
            // 预加载统计数据到缓存
            let cacheKey = cacheService.generateStatisticsCacheKey(
                type: .totalIncome,
                startDate: monthStart,
                endDate: monthEnd
            )
            
            if cacheService.getCachedStatistics(key: cacheKey, type: Double.self) == nil {
                let income = dataManager.getTotalIncome(from: monthStart, to: monthEnd)
                cacheService.cacheStatistics(income, key: cacheKey)
            }
        }
    }
    
    /// 获取月度工作密度数据
    func getMonthWorkDensity(for date: Date) -> [Date: Int] {
        let monthDates = DateHelper.datesInMonth(for: date)
        var densityData: [Date: Int] = [:]
        
        for date in monthDates {
            let dayKey = DateHelper.startOfDay(for: date)
            let records = getWorkRecords(for: date)
            densityData[dayKey] = records.count
        }
        
        return densityData
    }
    
    /// 获取工作类型分布数据
    func getWorkTypeDistribution(for date: Date) -> [UUID: Int] {
        let monthStart = DateHelper.startOfMonth(for: date)
        let monthEnd = DateHelper.endOfMonth(for: date)
        
        let records = dataManager.fetchWorkRecords(from: monthStart, to: monthEnd)
        
        var distribution: [UUID: Int] = [:]
        for record in records {
            distribution[record.workTypeId, default: 0] += 1
        }
        
        return distribution
    }
    
    // MARK: - 智能预加载
    
    /// 智能预加载相邻月份数据
    func smartPreload(currentDate: Date, viewType: CalendarViewType) {
        let calendar = Calendar.current
        
        switch viewType {
        case .month:
            // 预加载当前月份和前后各一个月
            let previousMonth = calendar.date(byAdding: .month, value: -1, to: currentDate) ?? currentDate
            let nextMonth = calendar.date(byAdding: .month, value: 1, to: currentDate) ?? currentDate
            
            preloadMonth(previousMonth)
            preloadMonth(currentDate)
            preloadMonth(nextMonth)
            
        case .week:
            // 预加载当前周和前后各两周
            let twoWeeksAgo = calendar.date(byAdding: .weekOfYear, value: -2, to: currentDate) ?? currentDate
            let twoWeeksLater = calendar.date(byAdding: .weekOfYear, value: 2, to: currentDate) ?? currentDate
            
            preloadDateRange(from: twoWeeksAgo, to: twoWeeksLater)
            
        case .list:
            // 预加载当前月份和前后各一个月
            let previousMonth = calendar.date(byAdding: .month, value: -1, to: currentDate) ?? currentDate
            let nextMonth = calendar.date(byAdding: .month, value: 1, to: currentDate) ?? currentDate
            
            let startDate = DateHelper.startOfMonth(for: previousMonth)
            let endDate = DateHelper.endOfMonth(for: nextMonth)
            
            preloadDateRange(from: startDate, to: endDate)
        }
    }
    
    /// 获取预加载状态
    func getPreloadStatus() -> PreloadStatus {
        let totalDays = preloadedWorkRecords.count
        let daysWithWork = preloadedWorkRecords.values.filter { !$0.isEmpty }.count
        
        return PreloadStatus(
            totalPreloadedDays: totalDays,
            daysWithWork: daysWithWork,
            loadedRange: loadedMonthRange,
            isLoading: preloadTask != nil && !preloadTask!.isCancelled
        )
    }
}

// MARK: - 预加载状态
struct PreloadStatus {
    let totalPreloadedDays: Int
    let daysWithWork: Int
    let loadedRange: (start: Date, end: Date)?
    let isLoading: Bool
    
    var workDensity: Double {
        guard totalPreloadedDays > 0 else { return 0 }
        return Double(daysWithWork) / Double(totalPreloadedDays)
    }
    
    var formattedRange: String {
        guard let range = loadedRange else { return "未加载" }
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日"
        return "\(formatter.string(from: range.start)) - \(formatter.string(from: range.end))"
    }
}

// MARK: - 日历数据观察器
@MainActor
class CalendarDataObserver: ObservableObject {
    @Published var currentMonth: Date = Date()
    @Published var workRecordsCount: Int = 0
    @Published var totalIncome: Double = 0
    @Published var isLoading: Bool = false
    
    private let calendarDataService = CalendarDataService.shared
    private let dataManager = DataManager.shared
    
    init() {
        updateData()
    }
    
    func updateCurrentMonth(_ date: Date) {
        currentMonth = date
        updateData()
    }
    
    private func updateData() {
        isLoading = true
        
        Task {
            // 预加载当前月份数据
            calendarDataService.preloadMonth(currentMonth)
            
            // 计算统计数据
            let monthStart = DateHelper.startOfMonth(for: currentMonth)
            let monthEnd = DateHelper.endOfMonth(for: currentMonth)
            
            let records = dataManager.fetchWorkRecords(from: monthStart, to: monthEnd)
            let income = SalaryCalculator.calculateTotalIncome(from: records)
            
            await MainActor.run {
                workRecordsCount = records.count
                totalIncome = income
                isLoading = false
            }
        }
    }
}
