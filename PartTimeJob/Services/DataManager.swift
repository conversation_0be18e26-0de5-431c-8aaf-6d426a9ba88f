//
//  DataManager.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation
import SwiftData

/// 数据管理器 - 负责数据库操作和数据访问
@MainActor
class DataManager: ObservableObject {
    /// 单例实例
    static let shared = DataManager()

    /// SwiftData 模型容器
    let container: ModelContainer

    /// 主上下文
    var context: ModelContext {
        return container.mainContext
    }

    /// 缓存服务
    private let cacheService = CacheService.shared
    
    private init() {
        do {
            // 配置 SwiftData 容器
            let schema = Schema([
                WorkRecord.self,
                WorkType.self
            ])
            
            let configuration = ModelConfiguration(
                schema: schema,
                isStoredInMemoryOnly: false
            )
            
            container = try ModelContainer(
                for: schema,
                configurations: [configuration]
            )
            
            // 初始化默认数据
            initializeDefaultData()
            
        } catch {
            fatalError("Failed to create ModelContainer: \(error)")
        }
    }
    
    /// 初始化默认数据
    private func initializeDefaultData() {
        // 检查是否已有工作类型数据
        let workTypeCount = try? context.fetchCount(FetchDescriptor<WorkType>())
        
        if workTypeCount == 0 {
            // 创建默认工作类型
            let defaultWorkTypes = WorkType.createDefaultWorkTypes()
            for workType in defaultWorkTypes {
                context.insert(workType)
            }
            
            do {
                try context.save()
                print("✅ 默认工作类型创建成功")
            } catch {
                print("❌ 创建默认工作类型失败: \(error)")
            }
        }
    }
    
    /// 保存上下文
    func save() {
        do {
            try context.save()
        } catch {
            print("❌ 保存数据失败: \(error)")
        }
    }
}

// MARK: - WorkRecord CRUD Operations
extension DataManager {
    /// 获取所有工作记录
    func fetchWorkRecords() -> [WorkRecord] {
        let cacheKey = cacheService.generateWorkRecordsCacheKey()

        // 尝试从缓存获取
        if let cachedRecords = cacheService.getCachedWorkRecords(key: cacheKey) {
            return cachedRecords
        }

        let descriptor = FetchDescriptor<WorkRecord>(
            predicate: #Predicate { !$0.isDeleted },
            sortBy: [SortDescriptor(\.date, order: .reverse)]
        )

        do {
            let records = try context.fetch(descriptor)
            // 缓存结果
            cacheService.cacheWorkRecords(records, key: cacheKey)
            return records
        } catch {
            print("❌ 获取工作记录失败: \(error)")
            return []
        }
    }
    
    /// 根据日期范围获取工作记录
    func fetchWorkRecords(from startDate: Date, to endDate: Date) -> [WorkRecord] {
        let cacheKey = cacheService.generateWorkRecordsCacheKey(startDate: startDate, endDate: endDate)

        // 尝试从缓存获取
        if let cachedRecords = cacheService.getCachedWorkRecords(key: cacheKey) {
            return cachedRecords
        }

        let descriptor = FetchDescriptor<WorkRecord>(
            predicate: #Predicate { record in
                !record.isDeleted &&
                record.date >= startDate &&
                record.date <= endDate
            },
            sortBy: [SortDescriptor(\.date, order: .reverse)]
        )

        do {
            let records = try context.fetch(descriptor)
            // 缓存结果
            cacheService.cacheWorkRecords(records, key: cacheKey)
            return records
        } catch {
            print("❌ 获取指定日期范围工作记录失败: \(error)")
            return []
        }
    }
    
    /// 添加工作记录
    func addWorkRecord(_ workRecord: WorkRecord) {
        context.insert(workRecord)
        save()
        // 清除相关缓存
        cacheService.clearWorkRecordsCache()
        cacheService.clearStatisticsCache()
    }

    /// 更新工作记录
    func updateWorkRecord(_ workRecord: WorkRecord) {
        workRecord.updatedAt = Date()
        save()
        // 清除相关缓存
        cacheService.clearWorkRecordsCache()
        cacheService.clearStatisticsCache()
    }

    /// 删除工作记录（软删除）
    func deleteWorkRecord(_ workRecord: WorkRecord) {
        workRecord.isDeleted = true
        workRecord.updatedAt = Date()
        save()
        // 清除相关缓存
        cacheService.clearWorkRecordsCache()
        cacheService.clearStatisticsCache()
    }
    
    /// 永久删除工作记录
    func permanentlyDeleteWorkRecord(_ workRecord: WorkRecord) {
        context.delete(workRecord)
        save()
    }

    /// 根据工作类型获取工作记录
    func fetchWorkRecords(by workTypeId: UUID) -> [WorkRecord] {
        let descriptor = FetchDescriptor<WorkRecord>(
            predicate: #Predicate { record in
                !record.isDeleted && record.workTypeId == workTypeId
            },
            sortBy: [SortDescriptor(\.date, order: .reverse)]
        )

        do {
            return try context.fetch(descriptor)
        } catch {
            print("❌ 获取指定工作类型的工作记录失败: \(error)")
            return []
        }
    }

    /// 获取指定日期的工作记录
    func fetchWorkRecords(for date: Date) -> [WorkRecord] {
        let startOfDay = DateHelper.startOfDay(for: date)
        let endOfDay = DateHelper.endOfDay(for: date)
        return fetchWorkRecords(from: startOfDay, to: endOfDay)
    }

    /// 搜索工作记录（按描述或备注）
    func searchWorkRecords(keyword: String) -> [WorkRecord] {
        let descriptor = FetchDescriptor<WorkRecord>(
            predicate: #Predicate { record in
                !record.isDeleted &&
                (record.workDescription.localizedStandardContains(keyword) ||
                 record.notes.localizedStandardContains(keyword))
            },
            sortBy: [SortDescriptor(\.date, order: .reverse)]
        )

        do {
            return try context.fetch(descriptor)
        } catch {
            print("❌ 搜索工作记录失败: \(error)")
            return []
        }
    }

    /// 获取最近的工作记录
    func fetchRecentWorkRecords(limit: Int = 10) -> [WorkRecord] {
        let descriptor = FetchDescriptor<WorkRecord>(
            predicate: #Predicate { !$0.isDeleted },
            sortBy: [SortDescriptor(\.date, order: .reverse)]
        )
        descriptor.fetchLimit = limit

        do {
            return try context.fetch(descriptor)
        } catch {
            print("❌ 获取最近工作记录失败: \(error)")
            return []
        }
    }
}

// MARK: - WorkType CRUD Operations
extension DataManager {
    /// 获取所有工作类型
    func fetchWorkTypes() -> [WorkType] {
        // 尝试从缓存获取
        if let cachedWorkTypes = cacheService.getCachedWorkTypes() {
            return cachedWorkTypes
        }

        let descriptor = FetchDescriptor<WorkType>(
            predicate: #Predicate { !$0.isDeleted && $0.isEnabled },
            sortBy: [SortDescriptor(\.sortOrder)]
        )

        do {
            let workTypes = try context.fetch(descriptor)
            // 缓存结果
            cacheService.cacheWorkTypes(workTypes)
            return workTypes
        } catch {
            print("❌ 获取工作类型失败: \(error)")
            return []
        }
    }
    
    /// 根据ID获取工作类型
    func fetchWorkType(by id: UUID) -> WorkType? {
        let descriptor = FetchDescriptor<WorkType>(
            predicate: #Predicate { $0.id == id && !$0.isDeleted }
        )
        
        do {
            return try context.fetch(descriptor).first
        } catch {
            print("❌ 获取工作类型失败: \(error)")
            return nil
        }
    }
    
    /// 添加工作类型
    func addWorkType(_ workType: WorkType) {
        context.insert(workType)
        save()
    }
    
    /// 更新工作类型
    func updateWorkType(_ workType: WorkType) {
        workType.updatedAt = Date()
        save()
    }
    
    /// 删除工作类型（软删除）
    func deleteWorkType(_ workType: WorkType) {
        workType.isDeleted = true
        workType.updatedAt = Date()
        save()
    }
}

// MARK: - Statistics Operations
extension DataManager {
    /// 获取总收入统计
    func getTotalIncome(from startDate: Date? = nil, to endDate: Date? = nil) -> Double {
        let workRecords: [WorkRecord]

        if let startDate = startDate, let endDate = endDate {
            workRecords = fetchWorkRecords(from: startDate, to: endDate)
        } else {
            workRecords = fetchWorkRecords()
        }

        return SalaryCalculator.calculateTotalIncome(from: workRecords)
    }

    /// 获取总工时统计
    func getTotalWorkHours(from startDate: Date? = nil, to endDate: Date? = nil) -> Double {
        let workRecords: [WorkRecord]

        if let startDate = startDate, let endDate = endDate {
            workRecords = fetchWorkRecords(from: startDate, to: endDate)
        } else {
            workRecords = fetchWorkRecords()
        }

        return SalaryCalculator.calculateTotalWorkHours(from: workRecords)
    }

    /// 获取平均时薪
    func getAverageHourlyRate(from startDate: Date? = nil, to endDate: Date? = nil) -> Double {
        let workRecords: [WorkRecord]

        if let startDate = startDate, let endDate = endDate {
            workRecords = fetchWorkRecords(from: startDate, to: endDate)
        } else {
            workRecords = fetchWorkRecords()
        }

        return SalaryCalculator.calculateAverageHourlyRate(from: workRecords)
    }

    /// 获取工作类型收入统计
    func getIncomeByWorkType(from startDate: Date? = nil, to endDate: Date? = nil) -> [WorkTypeIncome] {
        let workRecords: [WorkRecord]

        if let startDate = startDate, let endDate = endDate {
            workRecords = fetchWorkRecords(from: startDate, to: endDate)
        } else {
            workRecords = fetchWorkRecords()
        }

        let workTypes = fetchWorkTypes()
        return SalaryCalculator.calculateIncomeByWorkType(from: workRecords, workTypes: workTypes)
    }

    /// 获取月度收入统计
    func getMonthlyIncome() -> [MonthlyIncome] {
        let workRecords = fetchWorkRecords()
        return SalaryCalculator.calculateMonthlyIncome(from: workRecords)
    }

    /// 获取周度收入统计
    func getWeeklyIncome() -> [WeeklyIncome] {
        let workRecords = fetchWorkRecords()
        return SalaryCalculator.calculateWeeklyIncome(from: workRecords)
    }

    /// 获取工作记录数量统计
    func getWorkRecordCount(from startDate: Date? = nil, to endDate: Date? = nil) -> Int {
        let workRecords: [WorkRecord]

        if let startDate = startDate, let endDate = endDate {
            workRecords = fetchWorkRecords(from: startDate, to: endDate)
        } else {
            workRecords = fetchWorkRecords()
        }

        return workRecords.count
    }

    /// 获取工作天数统计
    func getWorkDaysCount(from startDate: Date? = nil, to endDate: Date? = nil) -> Int {
        let workRecords: [WorkRecord]

        if let startDate = startDate, let endDate = endDate {
            workRecords = fetchWorkRecords(from: startDate, to: endDate)
        } else {
            workRecords = fetchWorkRecords()
        }

        let uniqueDates = Set(workRecords.map { DateHelper.startOfDay(for: $0.date) })
        return uniqueDates.count
    }
}
