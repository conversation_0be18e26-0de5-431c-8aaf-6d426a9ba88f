//
//  WorkType.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation
import SwiftUI
import SwiftData

/// 工作类型数据模型
@Model
final class WorkType {
    /// 唯一标识符
    var id: UUID
    
    /// 工作类型名称
    var name: String
    
    /// 图标名称（SF Symbols）
    var iconName: String
    
    /// 主题颜色（十六进制字符串）
    var colorHex: String
    
    /// 默认薪资标准
    var defaultSalary: Double
    
    /// 默认薪资类型
    var defaultSalaryType: SalaryType
    
    /// 工作类型描述
    var workDescription: String
    
    /// 排序顺序
    var sortOrder: Int
    
    /// 是否启用
    var isEnabled: Bool
    
    /// 创建时间
    var createdAt: Date
    
    /// 更新时间
    var updatedAt: Date
    
    /// 是否已删除（软删除）
    var isDeleted: Bool
    
    init(
        id: UUID = UUID(),
        name: String,
        iconName: String,
        colorHex: String,
        defaultSalary: Double,
        defaultSalaryType: SalaryType,
        description: String = "",
        sortOrder: Int = 0
    ) {
        self.id = id
        self.name = name
        self.iconName = iconName
        self.colorHex = colorHex
        self.defaultSalary = defaultSalary
        self.defaultSalaryType = defaultSalaryType
        self.workDescription = description
        self.sortOrder = sortOrder
        self.isEnabled = true
        self.createdAt = Date()
        self.updatedAt = Date()
        self.isDeleted = false
    }
}

// MARK: - WorkType Extensions
extension WorkType {
    /// 获取主题颜色
    var themeColor: Color {
        return Color(hex: colorHex) ?? .blue
    }
    
    /// 格式化显示默认薪资
    var formattedDefaultSalary: String {
        switch defaultSalaryType {
        case .hourly:
            return String(format: "¥%.0f/小时", defaultSalary)
        case .daily:
            return String(format: "¥%.0f/天", defaultSalary)
        case .project:
            return String(format: "¥%.0f/项目", defaultSalary)
        }
    }

    /// 获取工作类型等级（基于薪资水平）
    var salaryLevel: SalaryLevel {
        let hourlyRate: Double

        switch defaultSalaryType {
        case .hourly:
            hourlyRate = defaultSalary
        case .daily:
            hourlyRate = defaultSalary / 8 // 假设8小时工作日
        case .project:
            hourlyRate = defaultSalary / 4 // 假设4小时项目
        }

        switch hourlyRate {
        case 0..<20:
            return .entry
        case 20..<50:
            return .intermediate
        case 50..<100:
            return .advanced
        default:
            return .expert
        }
    }

    /// 获取推荐工作时长
    var recommendedDuration: TimeInterval {
        switch defaultSalaryType {
        case .hourly:
            return 3600 * 4 // 4小时
        case .daily:
            return 3600 * 8 // 8小时
        case .project:
            return 3600 * 2 // 2小时
        }
    }

    /// 格式化推荐工作时长
    var formattedRecommendedDuration: String {
        let hours = Int(recommendedDuration / 3600)
        return "\(hours)小时"
    }

    /// 是否为高薪工作类型
    var isHighPaying: Bool {
        return salaryLevel == .advanced || salaryLevel == .expert
    }

    /// 工作类型标签
    var tags: [String] {
        var tags: [String] = []

        // 基于薪资类型添加标签
        tags.append(defaultSalaryType.displayName)

        // 基于薪资水平添加标签
        tags.append(salaryLevel.displayName)

        // 基于薪资高低添加标签
        if isHighPaying {
            tags.append("高薪")
        }

        return tags
    }
}

// MARK: - 预设工作类型
extension WorkType {
    /// 创建预设工作类型
    static func createDefaultWorkTypes() -> [WorkType] {
        return [
            WorkType(
                name: "兼职直播",
                iconName: "video.fill",
                colorHex: "#FF3B30",
                defaultSalary: 50,
                defaultSalaryType: .hourly,
                description: "网络直播、带货等工作",
                sortOrder: 1
            ),
            WorkType(
                name: "超市发传单",
                iconName: "doc.text.fill",
                colorHex: "#FF9500",
                defaultSalary: 120,
                defaultSalaryType: .daily,
                description: "商场、超市发传单推广",
                sortOrder: 2
            ),
            WorkType(
                name: "家教辅导",
                iconName: "book.fill",
                colorHex: "#34C759",
                defaultSalary: 80,
                defaultSalaryType: .hourly,
                description: "一对一或小班辅导",
                sortOrder: 3
            ),
            WorkType(
                name: "外卖配送",
                iconName: "bicycle",
                colorHex: "#007AFF",
                defaultSalary: 6,
                defaultSalaryType: .project,
                description: "外卖、快递配送服务",
                sortOrder: 4
            ),
            WorkType(
                name: "其他工作",
                iconName: "briefcase.fill",
                colorHex: "#AF52DE",
                defaultSalary: 20,
                defaultSalaryType: .hourly,
                description: "其他类型的兼职工作",
                sortOrder: 5
            )
        ]
    }
}

// MARK: - Color Extension
extension Color {
    /// 从十六进制字符串创建颜色
    init?(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - 薪资等级枚举
enum SalaryLevel: String, CaseIterable {
    case entry = "entry"             // 入门级
    case intermediate = "intermediate" // 中级
    case advanced = "advanced"       // 高级
    case expert = "expert"           // 专家级

    var displayName: String {
        switch self {
        case .entry:
            return "入门级"
        case .intermediate:
            return "中级"
        case .advanced:
            return "高级"
        case .expert:
            return "专家级"
        }
    }

    var color: String {
        switch self {
        case .entry:
            return "#8E8E93" // 灰色
        case .intermediate:
            return "#FF9500" // 橙色
        case .advanced:
            return "#34C759" // 绿色
        case .expert:
            return "#AF52DE" // 紫色
        }
    }

    var description: String {
        switch self {
        case .entry:
            return "适合新手，薪资较低"
        case .intermediate:
            return "有一定经验要求，薪资中等"
        case .advanced:
            return "需要丰富经验，薪资较高"
        case .expert:
            return "专业技能要求高，薪资优厚"
        }
    }

    var minHourlyRate: Double {
        switch self {
        case .entry:
            return 0
        case .intermediate:
            return 20
        case .advanced:
            return 50
        case .expert:
            return 100
        }
    }
}
