//
//  WorkRecord.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation
import SwiftData

/// 工作记录数据模型
@Model
final class WorkRecord {
    /// 唯一标识符
    var id: UUID
    
    /// 工作日期
    var date: Date
    
    /// 开始时间
    var startTime: Date
    
    /// 结束时间
    var endTime: Date
    
    /// 工作类型ID
    var workTypeId: UUID
    
    /// 工作内容描述
    var workDescription: String
    
    /// 薪资金额
    var salary: Double
    
    /// 薪资计算方式 (hourly: 时薪, daily: 日薪, project: 项目薪资)
    var salaryType: SalaryType
    
    /// 工作时长（小时）
    var workHours: Double
    
    /// 备注信息
    var notes: String
    
    /// 创建时间
    var createdAt: Date
    
    /// 更新时间
    var updatedAt: Date
    
    /// 是否已删除（软删除）
    var isDeleted: Bool
    
    init(
        id: UUID = UUID(),
        date: Date,
        startTime: Date,
        endTime: Date,
        workTypeId: UUID,
        workDescription: String,
        salary: Double,
        salaryType: SalaryType,
        notes: String = ""
    ) {
        self.id = id
        self.date = date
        self.startTime = startTime
        self.endTime = endTime
        self.workTypeId = workTypeId
        self.workDescription = workDescription
        self.salary = salary
        self.salaryType = salaryType
        self.notes = notes
        self.createdAt = Date()
        self.updatedAt = Date()
        self.isDeleted = false
        
        // 计算工作时长
        self.workHours = endTime.timeIntervalSince(startTime) / 3600
    }
}

/// 薪资计算方式枚举
enum SalaryType: String, CaseIterable, Codable {
    case hourly = "hourly"      // 时薪
    case daily = "daily"        // 日薪
    case project = "project"    // 项目薪资
    
    var displayName: String {
        switch self {
        case .hourly:
            return "时薪"
        case .daily:
            return "日薪"
        case .project:
            return "项目薪资"
        }
    }
}

// MARK: - WorkRecord Extensions
extension WorkRecord {
    /// 格式化显示日期
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        return formatter.string(from: date)
    }
    
    /// 格式化显示时间段
    var formattedTimeRange: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return "\(formatter.string(from: startTime)) - \(formatter.string(from: endTime))"
    }
    
    /// 格式化显示薪资
    var formattedSalary: String {
        return String(format: "¥%.2f", salary)
    }
    
    /// 格式化显示工作时长
    var formattedWorkHours: String {
        return String(format: "%.1f小时", workHours)
    }
    
    /// 计算实际薪资（基于薪资类型）
    func calculateActualSalary() -> Double {
        switch salaryType {
        case .hourly:
            return salary * workHours
        case .daily, .project:
            return salary
        }
    }

    /// 获取工作状态
    var workStatus: WorkStatus {
        let now = Date()
        let calendar = Calendar.current

        if calendar.isDate(date, inSameDayAs: now) {
            if now < startTime {
                return .upcoming
            } else if now >= startTime && now <= endTime {
                return .inProgress
            } else {
                return .completed
            }
        } else if date > now {
            return .upcoming
        } else {
            return .completed
        }
    }

    /// 获取工作效率评级
    var efficiencyRating: EfficiencyRating {
        let actualSalary = calculateActualSalary()
        let hourlyRate = workHours > 0 ? actualSalary / workHours : 0

        switch hourlyRate {
        case 0..<20:
            return .low
        case 20..<50:
            return .medium
        case 50..<100:
            return .high
        default:
            return .excellent
        }
    }

    /// 是否为今天的工作
    var isToday: Bool {
        return DateHelper.isToday(date)
    }

    /// 是否为本周的工作
    var isThisWeek: Bool {
        return DateHelper.isThisWeek(date)
    }

    /// 是否为本月的工作
    var isThisMonth: Bool {
        return DateHelper.isThisMonth(date)
    }

    /// 工作时长等级
    var durationLevel: DurationLevel {
        switch workHours {
        case 0..<2:
            return .short
        case 2..<6:
            return .medium
        case 6..<10:
            return .long
        default:
            return .extraLong
        }
    }
}

// MARK: - 工作状态枚举
enum WorkStatus: String, CaseIterable {
    case upcoming = "upcoming"     // 即将开始
    case inProgress = "inProgress" // 进行中
    case completed = "completed"   // 已完成

    var displayName: String {
        switch self {
        case .upcoming:
            return "即将开始"
        case .inProgress:
            return "进行中"
        case .completed:
            return "已完成"
        }
    }

    var color: String {
        switch self {
        case .upcoming:
            return "#FF9500" // 橙色
        case .inProgress:
            return "#34C759" // 绿色
        case .completed:
            return "#007AFF" // 蓝色
        }
    }
}

// MARK: - 效率评级枚举
enum EfficiencyRating: String, CaseIterable {
    case low = "low"           // 低效率
    case medium = "medium"     // 中等效率
    case high = "high"         // 高效率
    case excellent = "excellent" // 优秀效率

    var displayName: String {
        switch self {
        case .low:
            return "低效率"
        case .medium:
            return "中等效率"
        case .high:
            return "高效率"
        case .excellent:
            return "优秀效率"
        }
    }

    var color: String {
        switch self {
        case .low:
            return "#FF3B30" // 红色
        case .medium:
            return "#FF9500" // 橙色
        case .high:
            return "#34C759" // 绿色
        case .excellent:
            return "#AF52DE" // 紫色
        }
    }

    var stars: Int {
        switch self {
        case .low:
            return 1
        case .medium:
            return 2
        case .high:
            return 3
        case .excellent:
            return 4
        }
    }
}

// MARK: - 工作时长等级枚举
enum DurationLevel: String, CaseIterable {
    case short = "short"         // 短时间 (<2小时)
    case medium = "medium"       // 中等时间 (2-6小时)
    case long = "long"           // 长时间 (6-10小时)
    case extraLong = "extraLong" // 超长时间 (>10小时)

    var displayName: String {
        switch self {
        case .short:
            return "短时间"
        case .medium:
            return "中等时间"
        case .long:
            return "长时间"
        case .extraLong:
            return "超长时间"
        }
    }

    var description: String {
        switch self {
        case .short:
            return "少于2小时"
        case .medium:
            return "2-6小时"
        case .long:
            return "6-10小时"
        case .extraLong:
            return "超过10小时"
        }
    }
}
