//
//  UserPreferences.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation
import SwiftUI

/// 用户偏好设置模型
@MainActor
class UserPreferences: ObservableObject {
    /// 单例实例
    static let shared = UserPreferences()
    
    private let userDefaults = UserDefaults.standard
    
    // MARK: - 工作提醒设置
    
    /// 是否启用工作提醒
    @Published var workReminderEnabled: Bool {
        didSet {
            userDefaults.set(workReminderEnabled, forKey: "WorkReminderEnabled")
        }
    }
    
    /// 工作提醒提前时间（分钟）
    @Published var reminderMinutesBefore: Int {
        didSet {
            userDefaults.set(reminderMinutesBefore, forKey: "ReminderMinutesBefore")
        }
    }
    
    /// 是否启用记录提醒
    @Published var recordReminderEnabled: Bool {
        didSet {
            userDefaults.set(recordReminderEnabled, forKey: "RecordReminderEnabled")
        }
    }
    
    /// 记录提醒时间
    @Published var recordReminderTime: Date {
        didSet {
            userDefaults.set(recordReminderTime, forKey: "RecordReminderTime")
        }
    }
    
    // MARK: - 显示设置
    
    /// 统计开始日期（1=周一，7=周日）
    @Published var statisticsStartDay: Int {
        didSet {
            userDefaults.set(statisticsStartDay, forKey: "StatisticsStartDay")
        }
    }
    
    /// 货币符号
    @Published var currencySymbol: String {
        didSet {
            userDefaults.set(currencySymbol, forKey: "CurrencySymbol")
        }
    }
    
    /// 默认薪资类型
    @Published var defaultSalaryType: SalaryType {
        didSet {
            userDefaults.set(defaultSalaryType.rawValue, forKey: "DefaultSalaryType")
        }
    }
    
    /// 是否显示效率评级
    @Published var showEfficiencyRating: Bool {
        didSet {
            userDefaults.set(showEfficiencyRating, forKey: "ShowEfficiencyRating")
        }
    }
    
    /// 是否显示工作状态
    @Published var showWorkStatus: Bool {
        didSet {
            userDefaults.set(showWorkStatus, forKey: "ShowWorkStatus")
        }
    }
    
    // MARK: - 日历设置
    
    /// 日历视图类型
    @Published var calendarViewType: CalendarViewType {
        didSet {
            userDefaults.set(calendarViewType.rawValue, forKey: "CalendarViewType")
        }
    }
    
    /// 是否显示工作类型图例
    @Published var showWorkTypeLegend: Bool {
        didSet {
            userDefaults.set(showWorkTypeLegend, forKey: "ShowWorkTypeLegend")
        }
    }
    
    /// 是否显示收入预览
    @Published var showIncomePreview: Bool {
        didSet {
            userDefaults.set(showIncomePreview, forKey: "ShowIncomePreview")
        }
    }
    
    // MARK: - 数据设置
    
    /// 是否启用自动备份
    @Published var autoBackupEnabled: Bool {
        didSet {
            userDefaults.set(autoBackupEnabled, forKey: "AutoBackupEnabled")
        }
    }
    
    /// 自动备份频率（天）
    @Published var autoBackupFrequency: Int {
        didSet {
            userDefaults.set(autoBackupFrequency, forKey: "AutoBackupFrequency")
        }
    }
    
    /// 数据保留天数
    @Published var dataRetentionDays: Int {
        didSet {
            userDefaults.set(dataRetentionDays, forKey: "DataRetentionDays")
        }
    }
    
    // MARK: - 主题设置
    
    /// 应用主题
    @Published var appTheme: AppTheme {
        didSet {
            userDefaults.set(appTheme.rawValue, forKey: "AppTheme")
        }
    }
    
    /// 主题色调
    @Published var accentColor: String {
        didSet {
            userDefaults.set(accentColor, forKey: "AccentColor")
        }
    }
    
    private init() {
        // 初始化所有设置
        self.workReminderEnabled = userDefaults.bool(forKey: "WorkReminderEnabled")
        self.reminderMinutesBefore = userDefaults.object(forKey: "ReminderMinutesBefore") as? Int ?? 15
        self.recordReminderEnabled = userDefaults.bool(forKey: "RecordReminderEnabled")
        self.recordReminderTime = userDefaults.object(forKey: "RecordReminderTime") as? Date ?? Calendar.current.date(bySettingHour: 21, minute: 0, second: 0, of: Date()) ?? Date()
        
        self.statisticsStartDay = userDefaults.object(forKey: "StatisticsStartDay") as? Int ?? 1
        self.currencySymbol = userDefaults.string(forKey: "CurrencySymbol") ?? "¥"
        self.defaultSalaryType = SalaryType(rawValue: userDefaults.string(forKey: "DefaultSalaryType") ?? "") ?? .hourly
        self.showEfficiencyRating = userDefaults.object(forKey: "ShowEfficiencyRating") as? Bool ?? true
        self.showWorkStatus = userDefaults.object(forKey: "ShowWorkStatus") as? Bool ?? true
        
        self.calendarViewType = CalendarViewType(rawValue: userDefaults.string(forKey: "CalendarViewType") ?? "") ?? .month
        self.showWorkTypeLegend = userDefaults.object(forKey: "ShowWorkTypeLegend") as? Bool ?? true
        self.showIncomePreview = userDefaults.object(forKey: "ShowIncomePreview") as? Bool ?? true
        
        self.autoBackupEnabled = userDefaults.object(forKey: "AutoBackupEnabled") as? Bool ?? false
        self.autoBackupFrequency = userDefaults.object(forKey: "AutoBackupFrequency") as? Int ?? 7
        self.dataRetentionDays = userDefaults.object(forKey: "DataRetentionDays") as? Int ?? 365
        
        self.appTheme = AppTheme(rawValue: userDefaults.string(forKey: "AppTheme") ?? "") ?? .system
        self.accentColor = userDefaults.string(forKey: "AccentColor") ?? "#007AFF"
    }
    
    // MARK: - 便捷方法
    
    /// 重置所有设置为默认值
    func resetToDefaults() {
        workReminderEnabled = true
        reminderMinutesBefore = 15
        recordReminderEnabled = false
        recordReminderTime = Calendar.current.date(bySettingHour: 21, minute: 0, second: 0, of: Date()) ?? Date()
        
        statisticsStartDay = 1
        currencySymbol = "¥"
        defaultSalaryType = .hourly
        showEfficiencyRating = true
        showWorkStatus = true
        
        calendarViewType = .month
        showWorkTypeLegend = true
        showIncomePreview = true
        
        autoBackupEnabled = false
        autoBackupFrequency = 7
        dataRetentionDays = 365
        
        appTheme = .system
        accentColor = "#007AFF"
    }
    
    /// 获取格式化的记录提醒时间
    var formattedRecordReminderTime: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: recordReminderTime)
    }
    
    /// 获取主题色
    var themeColor: Color {
        return Color(hex: accentColor) ?? .blue
    }
    
    /// 获取统计开始日期的显示名称
    var statisticsStartDayName: String {
        let weekdays = ["", "周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        return weekdays[statisticsStartDay]
    }
}

// MARK: - 枚举定义

/// 日历视图类型
enum CalendarViewType: String, CaseIterable {
    case month = "month"
    case week = "week"
    case list = "list"
    
    var displayName: String {
        switch self {
        case .month:
            return "月视图"
        case .week:
            return "周视图"
        case .list:
            return "列表视图"
        }
    }
}

/// 应用主题
enum AppTheme: String, CaseIterable {
    case system = "system"
    case light = "light"
    case dark = "dark"
    
    var displayName: String {
        switch self {
        case .system:
            return "跟随系统"
        case .light:
            return "浅色模式"
        case .dark:
            return "深色模式"
        }
    }
    
    var colorScheme: ColorScheme? {
        switch self {
        case .system:
            return nil
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
}
