//
//  NotificationManager.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation
import UserNotifications
import SwiftUI

/// 通知管理器
@MainActor
class NotificationManager: NSObject, ObservableObject {
    /// 单例实例
    static let shared = NotificationManager()
    
    /// 通知权限状态
    @Published var authorizationStatus: UNAuthorizationStatus = .notDetermined
    
    /// 用户偏好设置
    private let userPreferences = UserPreferences.shared
    
    override init() {
        super.init()
        UNUserNotificationCenter.current().delegate = self
        checkAuthorizationStatus()
    }
    
    // MARK: - 权限管理
    
    /// 请求通知权限
    func requestAuthorization() async -> Bool {
        do {
            let granted = try await UNUserNotificationCenter.current().requestAuthorization(
                options: [.alert, .badge, .sound]
            )
            await updateAuthorizationStatus()
            return granted
        } catch {
            print("❌ 请求通知权限失败: \(error)")
            return false
        }
    }
    
    /// 检查通知权限状态
    func checkAuthorizationStatus() {
        Task {
            await updateAuthorizationStatus()
        }
    }
    
    /// 更新权限状态
    private func updateAuthorizationStatus() async {
        let settings = await UNUserNotificationCenter.current().notificationSettings()
        authorizationStatus = settings.authorizationStatus
    }
    
    // MARK: - 工作提醒
    
    /// 为工作记录设置提醒
    func scheduleWorkReminder(for workRecord: WorkRecord) async {
        guard userPreferences.workReminderEnabled,
              authorizationStatus == .authorized else { return }
        
        let reminderTime = Calendar.current.date(
            byAdding: .minute,
            value: -userPreferences.reminderMinutesBefore,
            to: workRecord.startTime
        ) ?? workRecord.startTime
        
        // 只为未来的工作设置提醒
        guard reminderTime > Date() else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "工作提醒"
        content.body = "您有一个工作即将开始：\(workRecord.workDescription)"
        content.sound = .default
        content.badge = 1
        
        // 添加用户信息
        content.userInfo = [
            "workRecordId": workRecord.id.uuidString,
            "type": "workReminder"
        ]
        
        let triggerDate = Calendar.current.dateComponents(
            [.year, .month, .day, .hour, .minute],
            from: reminderTime
        )
        let trigger = UNCalendarNotificationTrigger(dateMatching: triggerDate, repeats: false)
        
        let request = UNNotificationRequest(
            identifier: "work_reminder_\(workRecord.id.uuidString)",
            content: content,
            trigger: trigger
        )
        
        do {
            try await UNUserNotificationCenter.current().add(request)
            print("✅ 工作提醒设置成功: \(workRecord.workDescription)")
        } catch {
            print("❌ 设置工作提醒失败: \(error)")
        }
    }
    
    /// 取消工作提醒
    func cancelWorkReminder(for workRecordId: UUID) {
        let identifier = "work_reminder_\(workRecordId.uuidString)"
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [identifier])
    }
    
    // MARK: - 记录提醒
    
    /// 设置每日记录提醒
    func scheduleDailyRecordReminder() async {
        guard userPreferences.recordReminderEnabled,
              authorizationStatus == .authorized else { return }
        
        // 先取消现有的记录提醒
        cancelDailyRecordReminder()
        
        let content = UNMutableNotificationContent()
        content.title = "记录提醒"
        content.body = "别忘了记录今天的工作哦！"
        content.sound = .default
        content.badge = 1
        
        content.userInfo = ["type": "recordReminder"]
        
        let triggerDate = Calendar.current.dateComponents(
            [.hour, .minute],
            from: userPreferences.recordReminderTime
        )
        let trigger = UNCalendarNotificationTrigger(dateMatching: triggerDate, repeats: true)
        
        let request = UNNotificationRequest(
            identifier: "daily_record_reminder",
            content: content,
            trigger: trigger
        )
        
        do {
            try await UNUserNotificationCenter.current().add(request)
            print("✅ 每日记录提醒设置成功")
        } catch {
            print("❌ 设置每日记录提醒失败: \(error)")
        }
    }
    
    /// 取消每日记录提醒
    func cancelDailyRecordReminder() {
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: ["daily_record_reminder"])
    }
    
    // MARK: - 统计提醒
    
    /// 设置周度统计提醒
    func scheduleWeeklyStatsReminder() async {
        guard authorizationStatus == .authorized else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "周度统计"
        content.body = "查看您本周的工作统计和收入情况"
        content.sound = .default
        
        content.userInfo = ["type": "weeklyStats"]
        
        // 每周一上午9点提醒
        var triggerDate = DateComponents()
        triggerDate.weekday = 2 // 周一
        triggerDate.hour = 9
        triggerDate.minute = 0
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: triggerDate, repeats: true)
        
        let request = UNNotificationRequest(
            identifier: "weekly_stats_reminder",
            content: content,
            trigger: trigger
        )
        
        do {
            try await UNUserNotificationCenter.current().add(request)
            print("✅ 周度统计提醒设置成功")
        } catch {
            print("❌ 设置周度统计提醒失败: \(error)")
        }
    }
    
    // MARK: - 通知管理
    
    /// 获取所有待发送的通知
    func getPendingNotifications() async -> [UNNotificationRequest] {
        return await UNUserNotificationCenter.current().pendingNotificationRequests()
    }
    
    /// 清除所有通知
    func clearAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        UNUserNotificationCenter.current().removeAllDeliveredNotifications()
        
        // 重置应用图标徽章
        UIApplication.shared.applicationIconBadgeNumber = 0
    }
    
    /// 清除特定类型的通知
    func clearNotifications(ofType type: String) async {
        let pendingNotifications = await getPendingNotifications()
        let identifiersToRemove = pendingNotifications.compactMap { request in
            if let userInfo = request.content.userInfo as? [String: Any],
               userInfo["type"] as? String == type {
                return request.identifier
            }
            return nil
        }
        
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: identifiersToRemove)
    }
    
    /// 获取通知统计
    func getNotificationStats() async -> NotificationStats {
        let pendingNotifications = await getPendingNotifications()
        
        var workReminders = 0
        var recordReminders = 0
        var statsReminders = 0
        
        for notification in pendingNotifications {
            if let userInfo = notification.content.userInfo as? [String: Any],
               let type = userInfo["type"] as? String {
                switch type {
                case "workReminder":
                    workReminders += 1
                case "recordReminder":
                    recordReminders += 1
                case "weeklyStats":
                    statsReminders += 1
                default:
                    break
                }
            }
        }
        
        return NotificationStats(
            totalPending: pendingNotifications.count,
            workReminders: workReminders,
            recordReminders: recordReminders,
            statsReminders: statsReminders
        )
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension NotificationManager: UNUserNotificationCenterDelegate {
    /// 应用在前台时收到通知
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // 在前台也显示通知
        completionHandler([.banner, .sound, .badge])
    }
    
    /// 用户点击通知
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        
        // 处理不同类型的通知点击
        if let type = userInfo["type"] as? String {
            switch type {
            case "workReminder":
                // 跳转到工作详情或添加工作页面
                handleWorkReminderTap(userInfo: userInfo)
            case "recordReminder":
                // 跳转到添加工作记录页面
                handleRecordReminderTap()
            case "weeklyStats":
                // 跳转到统计页面
                handleStatsReminderTap()
            default:
                break
            }
        }
        
        completionHandler()
    }
    
    private func handleWorkReminderTap(userInfo: [AnyHashable: Any]) {
        // 这里可以发送通知给UI层处理导航
        NotificationCenter.default.post(
            name: .workReminderTapped,
            object: nil,
            userInfo: userInfo
        )
    }
    
    private func handleRecordReminderTap() {
        NotificationCenter.default.post(name: .recordReminderTapped, object: nil)
    }
    
    private func handleStatsReminderTap() {
        NotificationCenter.default.post(name: .statsReminderTapped, object: nil)
    }
}

// MARK: - 通知统计数据结构
struct NotificationStats {
    let totalPending: Int
    let workReminders: Int
    let recordReminders: Int
    let statsReminders: Int
}

// MARK: - 通知名称扩展
extension Notification.Name {
    static let workReminderTapped = Notification.Name("workReminderTapped")
    static let recordReminderTapped = Notification.Name("recordReminderTapped")
    static let statsReminderTapped = Notification.Name("statsReminderTapped")
}
