//
//  DataExporter.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation
import UniformTypeIdentifiers

/// 数据导出工具类
struct DataExporter {
    
    // MARK: - CSV导出
    
    /// 导出工作记录为CSV格式
    static func exportWorkRecordsToCSV(
        _ workRecords: [WorkRecord],
        workTypes: [WorkType]
    ) -> Data? {
        let workTypeDict = Dictionary(uniqueKeysWithValues: workTypes.map { ($0.id, $0.name) })
        
        var csvContent = "日期,开始时间,结束时间,工作类型,工作内容,薪资类型,薪资金额,工作时长,实际收入,备注\n"
        
        for record in workRecords {
            let workTypeName = workTypeDict[record.workTypeId] ?? "未知类型"
            let actualSalary = record.calculateActualSalary()
            
            let row = [
                DateHelper.yearMonthDayFormatter.string(from: record.date),
                DateHelper.hourMinuteFormatter.string(from: record.startTime),
                DateHelper.hourMinuteFormatter.string(from: record.endTime),
                workTypeName,
                escapeCSVField(record.workDescription),
                record.salaryType.displayName,
                String(format: "%.2f", record.salary),
                String(format: "%.2f", record.workHours),
                String(format: "%.2f", actualSalary),
                escapeCSVField(record.notes)
            ].joined(separator: ",")
            
            csvContent += row + "\n"
        }
        
        return csvContent.data(using: .utf8)
    }
    
    /// 导出工作类型为CSV格式
    static func exportWorkTypesToCSV(_ workTypes: [WorkType]) -> Data? {
        var csvContent = "工作类型,图标,颜色,默认薪资,薪资类型,描述,排序\n"
        
        for workType in workTypes {
            let row = [
                escapeCSVField(workType.name),
                workType.iconName,
                workType.colorHex,
                String(format: "%.2f", workType.defaultSalary),
                workType.defaultSalaryType.displayName,
                escapeCSVField(workType.workDescription),
                String(workType.sortOrder)
            ].joined(separator: ",")
            
            csvContent += row + "\n"
        }
        
        return csvContent.data(using: .utf8)
    }
    
    /// 导出统计数据为CSV格式
    static func exportStatisticsToCSV(
        workRecords: [WorkRecord],
        workTypes: [WorkType]
    ) -> Data? {
        var csvContent = "统计项目,数值,单位\n"
        
        // 基础统计
        let totalIncome = SalaryCalculator.calculateTotalIncome(from: workRecords)
        let totalHours = SalaryCalculator.calculateTotalWorkHours(from: workRecords)
        let averageHourlyRate = SalaryCalculator.calculateAverageHourlyRate(from: workRecords)
        let averageDailyIncome = SalaryCalculator.calculateAverageDailyIncome(from: workRecords)
        
        let basicStats = [
            ("总收入", String(format: "%.2f", totalIncome), "元"),
            ("总工时", String(format: "%.2f", totalHours), "小时"),
            ("平均时薪", String(format: "%.2f", averageHourlyRate), "元/小时"),
            ("平均日收入", String(format: "%.2f", averageDailyIncome), "元/天"),
            ("工作记录数", String(workRecords.count), "条")
        ]
        
        for (name, value, unit) in basicStats {
            csvContent += "\(name),\(value),\(unit)\n"
        }
        
        // 工作类型统计
        csvContent += "\n工作类型收入统计\n"
        csvContent += "工作类型,总收入,工作次数,平均收入\n"
        
        let incomeByType = SalaryCalculator.calculateIncomeByWorkType(from: workRecords, workTypes: workTypes)
        for typeIncome in incomeByType {
            let averageIncome = typeIncome.recordCount > 0 ? typeIncome.totalIncome / Double(typeIncome.recordCount) : 0
            csvContent += "\(typeIncome.workType.name),\(String(format: "%.2f", typeIncome.totalIncome)),\(typeIncome.recordCount),\(String(format: "%.2f", averageIncome))\n"
        }
        
        return csvContent.data(using: .utf8)
    }
    
    // MARK: - Excel导出（简化版）
    
    /// 导出为Excel兼容的CSV格式
    static func exportToExcelCSV(
        workRecords: [WorkRecord],
        workTypes: [WorkType]
    ) -> Data? {
        // Excel CSV使用UTF-8 BOM
        let bom = Data([0xEF, 0xBB, 0xBF])
        guard let csvData = exportWorkRecordsToCSV(workRecords, workTypes: workTypes) else {
            return nil
        }
        
        var excelData = Data()
        excelData.append(bom)
        excelData.append(csvData)
        
        return excelData
    }
    
    // MARK: - 文本报告导出
    
    /// 生成文本格式的工作报告
    static func generateTextReport(
        workRecords: [WorkRecord],
        workTypes: [WorkType],
        dateRange: (start: Date, end: Date)
    ) -> String {
        let workTypeDict = Dictionary(uniqueKeysWithValues: workTypes.map { ($0.id, $0) })
        
        var report = """
        工作报告
        ========
        
        报告期间: \(DateHelper.yearMonthDayFormatter.string(from: dateRange.start)) - \(DateHelper.yearMonthDayFormatter.string(from: dateRange.end))
        生成时间: \(DateHelper.yearMonthDayFormatter.string(from: Date())) \(DateHelper.hourMinuteFormatter.string(from: Date()))
        
        """
        
        // 总体统计
        let totalIncome = SalaryCalculator.calculateTotalIncome(from: workRecords)
        let totalHours = SalaryCalculator.calculateTotalWorkHours(from: workRecords)
        let averageHourlyRate = SalaryCalculator.calculateAverageHourlyRate(from: workRecords)
        let workDays = Set(workRecords.map { DateHelper.startOfDay(for: $0.date) }).count
        
        report += """
        总体统计
        --------
        总收入: ¥\(String(format: "%.2f", totalIncome))
        总工时: \(String(format: "%.1f", totalHours))小时
        平均时薪: ¥\(String(format: "%.2f", averageHourlyRate))/小时
        工作天数: \(workDays)天
        工作记录: \(workRecords.count)条
        
        """
        
        // 工作类型统计
        report += "工作类型统计\n"
        report += "------------\n"
        
        let incomeByType = SalaryCalculator.calculateIncomeByWorkType(from: workRecords, workTypes: workTypes)
        for typeIncome in incomeByType {
            let percentage = totalIncome > 0 ? (typeIncome.totalIncome / totalIncome) * 100 : 0
            report += "\(typeIncome.workType.name): ¥\(String(format: "%.2f", typeIncome.totalIncome)) (\(String(format: "%.1f", percentage))%)\n"
        }
        
        report += "\n"
        
        // 详细记录
        report += "详细记录\n"
        report += "--------\n"
        
        let sortedRecords = workRecords.sorted { $0.date > $1.date }
        for record in sortedRecords {
            let workTypeName = workTypeDict[record.workTypeId]?.name ?? "未知类型"
            let actualSalary = record.calculateActualSalary()
            
            report += """
            \(DateHelper.yearMonthDayFormatter.string(from: record.date)) \(DateHelper.hourMinuteFormatter.string(from: record.startTime))-\(DateHelper.hourMinuteFormatter.string(from: record.endTime))
            类型: \(workTypeName) | 内容: \(record.workDescription)
            工时: \(String(format: "%.1f", record.workHours))小时 | 收入: ¥\(String(format: "%.2f", actualSalary))
            
            """
        }
        
        return report
    }
    
    // MARK: - 辅助方法
    
    /// 转义CSV字段
    private static func escapeCSVField(_ field: String) -> String {
        let escapedField = field.replacingOccurrences(of: "\"", with: "\"\"")
        if field.contains(",") || field.contains("\"") || field.contains("\n") {
            return "\"\(escapedField)\""
        }
        return escapedField
    }
    
    /// 获取导出文件名
    static func generateFileName(
        type: ExportType,
        dateRange: (start: Date, end: Date)? = nil
    ) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd"
        
        let dateString: String
        if let range = dateRange {
            dateString = "\(formatter.string(from: range.start))-\(formatter.string(from: range.end))"
        } else {
            dateString = formatter.string(from: Date())
        }
        
        switch type {
        case .workRecordsCSV:
            return "工作记录_\(dateString).csv"
        case .workTypesCSV:
            return "工作类型_\(dateString).csv"
        case .statisticsCSV:
            return "统计数据_\(dateString).csv"
        case .excelCSV:
            return "工作数据_\(dateString).csv"
        case .textReport:
            return "工作报告_\(dateString).txt"
        case .jsonBackup:
            return "数据备份_\(dateString).json"
        }
    }
    
    /// 获取文件类型
    static func getUTType(for exportType: ExportType) -> UTType {
        switch exportType {
        case .workRecordsCSV, .workTypesCSV, .statisticsCSV, .excelCSV:
            return .commaSeparatedText
        case .textReport:
            return .plainText
        case .jsonBackup:
            return .json
        }
    }
}

// MARK: - 导出类型枚举
enum ExportType: String, CaseIterable {
    case workRecordsCSV = "workRecordsCSV"
    case workTypesCSV = "workTypesCSV"
    case statisticsCSV = "statisticsCSV"
    case excelCSV = "excelCSV"
    case textReport = "textReport"
    case jsonBackup = "jsonBackup"
    
    var displayName: String {
        switch self {
        case .workRecordsCSV:
            return "工作记录 (CSV)"
        case .workTypesCSV:
            return "工作类型 (CSV)"
        case .statisticsCSV:
            return "统计数据 (CSV)"
        case .excelCSV:
            return "Excel格式 (CSV)"
        case .textReport:
            return "文本报告 (TXT)"
        case .jsonBackup:
            return "数据备份 (JSON)"
        }
    }
    
    var description: String {
        switch self {
        case .workRecordsCSV:
            return "导出所有工作记录为CSV格式"
        case .workTypesCSV:
            return "导出工作类型配置为CSV格式"
        case .statisticsCSV:
            return "导出统计数据为CSV格式"
        case .excelCSV:
            return "导出为Excel兼容的CSV格式"
        case .textReport:
            return "生成可读的文本报告"
        case .jsonBackup:
            return "完整数据备份（JSON格式）"
        }
    }

    // MARK: - 统一导出接口

    /// 导出数据到指定格式
    /// - Parameters:
    ///   - records: 工作记录数组
    ///   - workTypes: 工作类型数组
    ///   - format: 导出格式
    ///   - includeStatistics: 是否包含统计信息
    ///   - timeRange: 时间范围
    /// - Returns: 导出文件的URL
    static func exportData(
        records: [WorkRecord],
        workTypes: [WorkType],
        format: ExportType,
        includeStatistics: Bool = false,
        timeRange: TimeRange? = nil
    ) async throws -> URL {

        let fileName = generateFileName(format: format, timeRange: timeRange)
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileURL = documentsPath.appendingPathComponent(fileName)

        switch format {
        case .csv:
            let data = exportWorkRecordsToCSV(records, workTypes: workTypes)
            try data?.write(to: fileURL)

        case .excel:
            // Excel格式的CSV（UTF-8 BOM）
            let data = exportWorkRecordsToExcelCSV(records, workTypes: workTypes)
            try data?.write(to: fileURL)

        case .json:
            let data = try exportToJSON(records: records, workTypes: workTypes, includeStatistics: includeStatistics)
            try data.write(to: fileURL)

        case .pdf:
            let data = try await exportToPDF(records: records, workTypes: workTypes, includeStatistics: includeStatistics, timeRange: timeRange)
            try data.write(to: fileURL)
        }

        return fileURL
    }

    // MARK: - JSON导出

    private static func exportToJSON(
        records: [WorkRecord],
        workTypes: [WorkType],
        includeStatistics: Bool
    ) throws -> Data {

        var exportData: [String: Any] = [:]

        // 基本信息
        exportData["exportDate"] = ISO8601DateFormatter().string(from: Date())
        exportData["version"] = "1.0"
        exportData["recordCount"] = records.count

        // 工作记录
        let recordsData = records.map { record in
            [
                "id": record.id.uuidString,
                "date": ISO8601DateFormatter().string(from: record.date),
                "startTime": ISO8601DateFormatter().string(from: record.startTime),
                "endTime": ISO8601DateFormatter().string(from: record.endTime),
                "workTypeId": record.workTypeId.uuidString,
                "workDescription": record.workDescription,
                "salary": record.salary,
                "salaryType": record.salaryType.rawValue,
                "notes": record.notes,
                "workHours": record.workHours,
                "actualSalary": record.actualSalary
            ]
        }
        exportData["workRecords"] = recordsData

        // 工作类型
        let workTypesData = workTypes.map { workType in
            [
                "id": workType.id.uuidString,
                "name": workType.name,
                "iconName": workType.iconName,
                "colorHex": workType.colorHex,
                "defaultSalary": workType.defaultSalary,
                "salaryType": workType.salaryType.rawValue,
                "workDescription": workType.workDescription,
                "isActive": workType.isActive
            ]
        }
        exportData["workTypes"] = workTypesData

        // 统计信息
        if includeStatistics {
            let statistics = calculateStatistics(records: records, workTypes: workTypes)
            exportData["statistics"] = statistics
        }

        return try JSONSerialization.data(withJSONObject: exportData, options: .prettyPrinted)
    }

    // MARK: - PDF导出

    private static func exportToPDF(
        records: [WorkRecord],
        workTypes: [WorkType],
        includeStatistics: Bool,
        timeRange: TimeRange?
    ) async throws -> Data {
        // 这里可以使用PDFKit或其他PDF生成库
        // 暂时返回一个简单的文本内容作为PDF
        let textContent = generateTextReport(records: records, workTypes: workTypes, includeStatistics: includeStatistics, timeRange: timeRange)

        // 简单的PDF生成（实际项目中应该使用专业的PDF库）
        return textContent.data(using: .utf8) ?? Data()
    }

    // MARK: - 辅助方法

    private static func generateFileName(format: ExportType, timeRange: TimeRange?) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
        let timestamp = dateFormatter.string(from: Date())

        let timeRangeString = timeRange?.displayName.replacingOccurrences(of: " ", with: "_") ?? "全部"

        return "工作记录_\(timeRangeString)_\(timestamp).\(format.fileExtension)"
    }

    private static func calculateStatistics(records: [WorkRecord], workTypes: [WorkType]) -> [String: Any] {
        let totalIncome = records.reduce(0) { $0 + $1.actualSalary }
        let totalHours = records.reduce(0) { $0 + $1.workHours }
        let averageHourlyRate = totalHours > 0 ? totalIncome / totalHours : 0

        // 按工作类型统计
        let workTypeStats = Dictionary(grouping: records) { $0.workTypeId }
            .mapValues { records in
                [
                    "count": records.count,
                    "totalIncome": records.reduce(0) { $0 + $1.actualSalary },
                    "totalHours": records.reduce(0) { $0 + $1.workHours }
                ]
            }

        return [
            "totalRecords": records.count,
            "totalIncome": totalIncome,
            "totalHours": totalHours,
            "averageHourlyRate": averageHourlyRate,
            "workTypeStatistics": workTypeStats
        ]
    }

    private static func generateTextReport(
        records: [WorkRecord],
        workTypes: [WorkType],
        includeStatistics: Bool,
        timeRange: TimeRange?
    ) -> String {
        var report = "工作记录报告\n"
        report += "================\n\n"

        if let timeRange = timeRange {
            report += "时间范围: \(timeRange.displayName)\n"
        }
        report += "导出时间: \(DateHelper.fullDateTimeFormatter.string(from: Date()))\n"
        report += "记录数量: \(records.count)条\n\n"

        // 统计信息
        if includeStatistics {
            let totalIncome = records.reduce(0) { $0 + $1.actualSalary }
            let totalHours = records.reduce(0) { $0 + $1.workHours }

            report += "统计摘要\n"
            report += "--------\n"
            report += "总收入: ¥\(String(format: "%.2f", totalIncome))\n"
            report += "总工时: \(String(format: "%.1f", totalHours))小时\n"
            report += "平均时薪: ¥\(String(format: "%.2f", totalHours > 0 ? totalIncome / totalHours : 0))\n\n"
        }

        // 详细记录
        report += "详细记录\n"
        report += "--------\n"

        let workTypeDict = Dictionary(uniqueKeysWithValues: workTypes.map { ($0.id, $0.name) })

        for record in records.sorted(by: { $0.date > $1.date }) {
            let workTypeName = workTypeDict[record.workTypeId] ?? "未知类型"
            report += "\(DateHelper.yearMonthDayFormatter.string(from: record.date)) - \(workTypeName)\n"
            report += "  时间: \(DateHelper.hourMinuteFormatter.string(from: record.startTime)) - \(DateHelper.hourMinuteFormatter.string(from: record.endTime))\n"
            report += "  内容: \(record.workDescription)\n"
            report += "  收入: ¥\(String(format: "%.2f", record.actualSalary))\n\n"
        }

        return report
    }
}
