//
//  AppConfiguration.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation
import SwiftUI

/// 应用配置管理器
class AppConfiguration: ObservableObject {
    static let shared = AppConfiguration()
    
    // MARK: - 环境配置
    
    enum Environment: String, CaseIterable {
        case development = "development"
        case staging = "staging"
        case production = "production"
        
        var displayName: String {
            switch self {
            case .development:
                return "开发环境"
            case .staging:
                return "测试环境"
            case .production:
                return "生产环境"
            }
        }
    }
    
    // MARK: - 配置属性
    
    @Published var currentEnvironment: Environment
    @Published var isDebugMode: Bool
    @Published var enablePerformanceMonitoring: Bool
    @Published var enableCrashReporting: Bool
    @Published var enableAnalytics: Bool
    @Published var maxCacheSize: Int // MB
    @Published var databaseTimeout: TimeInterval
    @Published var networkTimeout: TimeInterval
    
    // MARK: - 应用信息
    
    let appVersion: String
    let buildNumber: String
    let bundleIdentifier: String
    let appName: String
    
    // MARK: - 功能开关
    
    @Published var enableExperimentalFeatures: Bool
    @Published var enableBetaFeatures: Bool
    @Published var enableAdvancedStatistics: Bool
    @Published var enableDataExport: Bool
    @Published var enableCloudSync: Bool
    
    private init() {
        // 从 Info.plist 读取应用信息
        let bundle = Bundle.main
        self.appVersion = bundle.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
        self.buildNumber = bundle.infoDictionary?["CFBundleVersion"] as? String ?? "1"
        self.bundleIdentifier = bundle.bundleIdentifier ?? "com.parttimejob.app"
        self.appName = bundle.infoDictionary?["CFBundleDisplayName"] as? String ?? "兼职记录助手"
        
        // 根据构建配置确定环境
        #if DEBUG
        self.currentEnvironment = .development
        self.isDebugMode = true
        #elseif STAGING
        self.currentEnvironment = .staging
        self.isDebugMode = true
        #else
        self.currentEnvironment = .production
        self.isDebugMode = false
        #endif
        
        // 默认配置
        self.enablePerformanceMonitoring = isDebugMode
        self.enableCrashReporting = !isDebugMode
        self.enableAnalytics = !isDebugMode
        self.maxCacheSize = 100 // 100MB
        self.databaseTimeout = 30.0 // 30秒
        self.networkTimeout = 10.0 // 10秒
        
        // 功能开关
        self.enableExperimentalFeatures = isDebugMode
        self.enableBetaFeatures = currentEnvironment != .production
        self.enableAdvancedStatistics = true
        self.enableDataExport = true
        self.enableCloudSync = false // 暂未实现
        
        // 加载保存的配置
        loadConfiguration()
    }
    
    // MARK: - 配置管理
    
    private func loadConfiguration() {
        let defaults = UserDefaults.standard
        
        // 只在开发环境下允许修改某些配置
        if isDebugMode {
            if let envString = defaults.string(forKey: "app_environment"),
               let env = Environment(rawValue: envString) {
                currentEnvironment = env
            }
            
            enablePerformanceMonitoring = defaults.bool(forKey: "enable_performance_monitoring")
            enableExperimentalFeatures = defaults.bool(forKey: "enable_experimental_features")
        }
        
        // 用户可配置的选项
        enableAdvancedStatistics = defaults.bool(forKey: "enable_advanced_statistics")
        enableDataExport = defaults.bool(forKey: "enable_data_export")
        
        let savedCacheSize = defaults.integer(forKey: "max_cache_size")
        if savedCacheSize > 0 {
            maxCacheSize = savedCacheSize
        }
    }
    
    func saveConfiguration() {
        let defaults = UserDefaults.standard
        
        if isDebugMode {
            defaults.set(currentEnvironment.rawValue, forKey: "app_environment")
            defaults.set(enablePerformanceMonitoring, forKey: "enable_performance_monitoring")
            defaults.set(enableExperimentalFeatures, forKey: "enable_experimental_features")
        }
        
        defaults.set(enableAdvancedStatistics, forKey: "enable_advanced_statistics")
        defaults.set(enableDataExport, forKey: "enable_data_export")
        defaults.set(maxCacheSize, forKey: "max_cache_size")
    }
    
    // MARK: - 环境特定配置
    
    var apiBaseURL: String {
        switch currentEnvironment {
        case .development:
            return "https://dev-api.parttimejob.app"
        case .staging:
            return "https://staging-api.parttimejob.app"
        case .production:
            return "https://api.parttimejob.app"
        }
    }
    
    var logLevel: LogLevel {
        switch currentEnvironment {
        case .development:
            return .debug
        case .staging:
            return .info
        case .production:
            return .warning
        }
    }
    
    var shouldShowDebugInfo: Bool {
        return isDebugMode && enableExperimentalFeatures
    }
    
    // MARK: - 功能检查
    
    func isFeatureEnabled(_ feature: Feature) -> Bool {
        switch feature {
        case .advancedStatistics:
            return enableAdvancedStatistics
        case .dataExport:
            return enableDataExport
        case .cloudSync:
            return enableCloudSync
        case .performanceMonitoring:
            return enablePerformanceMonitoring
        case .experimentalFeatures:
            return enableExperimentalFeatures
        case .betaFeatures:
            return enableBetaFeatures
        }
    }
    
    // MARK: - 调试工具
    
    func getDebugInfo() -> [String: Any] {
        return [
            "app_version": appVersion,
            "build_number": buildNumber,
            "bundle_identifier": bundleIdentifier,
            "environment": currentEnvironment.rawValue,
            "debug_mode": isDebugMode,
            "device_model": UIDevice.current.model,
            "system_version": UIDevice.current.systemVersion,
            "memory_usage": getMemoryUsage(),
            "disk_space": getDiskSpace()
        ]
    }
    
    private func getMemoryUsage() -> [String: Any] {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return [
                "used_mb": Double(info.resident_size) / 1024 / 1024,
                "virtual_mb": Double(info.virtual_size) / 1024 / 1024
            ]
        }
        
        return ["error": "Unable to get memory info"]
    }
    
    private func getDiskSpace() -> [String: Any] {
        do {
            let systemAttributes = try FileManager.default.attributesOfFileSystem(forPath: NSHomeDirectory())
            let freeSpace = systemAttributes[.systemFreeSize] as? NSNumber
            let totalSpace = systemAttributes[.systemSize] as? NSNumber
            
            return [
                "free_gb": (freeSpace?.doubleValue ?? 0) / 1024 / 1024 / 1024,
                "total_gb": (totalSpace?.doubleValue ?? 0) / 1024 / 1024 / 1024
            ]
        } catch {
            return ["error": "Unable to get disk space info"]
        }
    }
}

// MARK: - 功能枚举

enum Feature: String, CaseIterable {
    case advancedStatistics = "advanced_statistics"
    case dataExport = "data_export"
    case cloudSync = "cloud_sync"
    case performanceMonitoring = "performance_monitoring"
    case experimentalFeatures = "experimental_features"
    case betaFeatures = "beta_features"
    
    var displayName: String {
        switch self {
        case .advancedStatistics:
            return "高级统计"
        case .dataExport:
            return "数据导出"
        case .cloudSync:
            return "云同步"
        case .performanceMonitoring:
            return "性能监控"
        case .experimentalFeatures:
            return "实验性功能"
        case .betaFeatures:
            return "测试功能"
        }
    }
    
    var description: String {
        switch self {
        case .advancedStatistics:
            return "启用详细的数据分析和图表"
        case .dataExport:
            return "允许导出工作记录数据"
        case .cloudSync:
            return "同步数据到云端（开发中）"
        case .performanceMonitoring:
            return "监控应用性能指标"
        case .experimentalFeatures:
            return "启用实验性新功能"
        case .betaFeatures:
            return "启用测试阶段功能"
        }
    }
}

// MARK: - 日志级别

enum LogLevel: Int, CaseIterable {
    case debug = 0
    case info = 1
    case warning = 2
    case error = 3
    case critical = 4
    
    var displayName: String {
        switch self {
        case .debug:
            return "调试"
        case .info:
            return "信息"
        case .warning:
            return "警告"
        case .error:
            return "错误"
        case .critical:
            return "严重"
        }
    }
}

// MARK: - 配置视图

struct AppConfigurationView: View {
    @StateObject private var config = AppConfiguration.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                // 应用信息
                Section("应用信息") {
                    InfoRow(title: "应用名称", value: config.appName)
                    InfoRow(title: "版本", value: "\(config.appVersion) (\(config.buildNumber))")
                    InfoRow(title: "环境", value: config.currentEnvironment.displayName)
                    InfoRow(title: "Bundle ID", value: config.bundleIdentifier)
                }
                
                // 功能开关
                Section("功能设置") {
                    ForEach(Feature.allCases, id: \.self) { feature in
                        Toggle(isOn: Binding(
                            get: { config.isFeatureEnabled(feature) },
                            set: { _ in toggleFeature(feature) }
                        )) {
                            VStack(alignment: .leading, spacing: 2) {
                                Text(feature.displayName)
                                    .font(.body)
                                
                                Text(feature.description)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .disabled(!canToggleFeature(feature))
                    }
                }
                
                // 性能设置
                if config.isDebugMode {
                    Section("性能设置") {
                        HStack {
                            Text("缓存大小限制")
                            Spacer()
                            Text("\(config.maxCacheSize) MB")
                                .foregroundColor(.secondary)
                        }
                        
                        HStack {
                            Text("数据库超时")
                            Spacer()
                            Text("\(Int(config.databaseTimeout))秒")
                                .foregroundColor(.secondary)
                        }
                        
                        HStack {
                            Text("网络超时")
                            Spacer()
                            Text("\(Int(config.networkTimeout))秒")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                // 调试信息
                if config.shouldShowDebugInfo {
                    Section("调试信息") {
                        NavigationLink("查看详细信息") {
                            DebugInfoView()
                        }
                    }
                }
            }
            .navigationTitle("应用配置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        config.saveConfiguration()
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func toggleFeature(_ feature: Feature) {
        switch feature {
        case .advancedStatistics:
            config.enableAdvancedStatistics.toggle()
        case .dataExport:
            config.enableDataExport.toggle()
        case .cloudSync:
            config.enableCloudSync.toggle()
        case .performanceMonitoring:
            config.enablePerformanceMonitoring.toggle()
        case .experimentalFeatures:
            config.enableExperimentalFeatures.toggle()
        case .betaFeatures:
            config.enableBetaFeatures.toggle()
        }
    }
    
    private func canToggleFeature(_ feature: Feature) -> Bool {
        switch feature {
        case .cloudSync:
            return false // 暂未实现
        case .performanceMonitoring, .experimentalFeatures:
            return config.isDebugMode
        default:
            return true
        }
    }
}

struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.primary)
            Spacer()
            Text(value)
                .foregroundColor(.secondary)
                .font(.caption)
        }
    }
}

struct DebugInfoView: View {
    @StateObject private var config = AppConfiguration.shared
    @State private var debugInfo: [String: Any] = [:]
    
    var body: some View {
        List {
            ForEach(Array(debugInfo.keys.sorted()), id: \.self) { key in
                HStack {
                    Text(key)
                        .font(.caption)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Text("\(debugInfo[key] ?? "")")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .navigationTitle("调试信息")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            debugInfo = config.getDebugInfo()
        }
    }
}
