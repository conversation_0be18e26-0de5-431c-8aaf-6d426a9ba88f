//
//  AnimationManager.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 动画管理器 - 统一的动画效果和过渡
struct AnimationManager {
    
    // MARK: - 页面过渡动画
    
    /// 淡入淡出过渡
    static func fadeTransition(duration: Double = 0.3) -> AnyTransition {
        .opacity.animation(.easeInOut(duration: duration))
    }
    
    /// 滑动过渡
    static func slideTransition(edge: Edge = .trailing, duration: Double = 0.3) -> AnyTransition {
        .asymmetric(
            insertion: .move(edge: edge).combined(with: .opacity),
            removal: .move(edge: edge.opposite).combined(with: .opacity)
        )
        .animation(.easeInOut(duration: duration))
    }
    
    /// 缩放过渡
    static func scaleTransition(scale: CGFloat = 0.8, duration: Double = 0.3) -> AnyTransition {
        .scale(scale: scale).combined(with: .opacity)
            .animation(.spring(response: duration, dampingFraction: 0.8))
    }
    
    /// 卡片翻转过渡
    static func cardFlipTransition() -> AnyTransition {
        .asymmetric(
            insertion: .scale(scale: 0.1).combined(with: .opacity),
            removal: .scale(scale: 1.2).combined(with: .opacity)
        )
        .animation(.spring(response: 0.5, dampingFraction: 0.7))
    }
    
    // MARK: - 列表动画
    
    /// 列表项插入动画
    static func listInsertAnimation() -> Animation {
        .spring(response: 0.4, dampingFraction: 0.8, blendDuration: 0.2)
    }
    
    /// 列表项删除动画
    static func listRemoveAnimation() -> Animation {
        .easeInOut(duration: 0.3)
    }
    
    /// 列表项移动动画
    static func listMoveAnimation() -> Animation {
        .spring(response: 0.3, dampingFraction: 0.9)
    }
    
    // MARK: - 按钮动画
    
    /// 按钮按下动画
    static func buttonPressAnimation() -> Animation {
        .easeInOut(duration: 0.1)
    }
    
    /// 按钮释放动画
    static func buttonReleaseAnimation() -> Animation {
        .spring(response: 0.3, dampingFraction: 0.6)
    }
    
    /// 浮动按钮动画
    static func floatingButtonAnimation() -> Animation {
        .spring(response: 0.4, dampingFraction: 0.7)
    }
    
    // MARK: - 加载动画
    
    /// 脉冲动画
    static func pulseAnimation(duration: Double = 1.0) -> Animation {
        .easeInOut(duration: duration).repeatForever(autoreverses: true)
    }
    
    /// 旋转动画
    static func rotationAnimation(duration: Double = 1.0) -> Animation {
        .linear(duration: duration).repeatForever(autoreverses: false)
    }
    
    /// 呼吸动画
    static func breathingAnimation(duration: Double = 2.0) -> Animation {
        .easeInOut(duration: duration).repeatForever(autoreverses: true)
    }
    
    // MARK: - 手势动画
    
    /// 拖拽动画
    static func dragAnimation() -> Animation {
        .interactiveSpring(response: 0.3, dampingFraction: 0.8)
    }
    
    /// 长按动画
    static func longPressAnimation() -> Animation {
        .easeInOut(duration: 0.2)
    }
    
    /// 滑动动画
    static func swipeAnimation() -> Animation {
        .spring(response: 0.4, dampingFraction: 0.8)
    }
    
    // MARK: - 数据变化动画
    
    /// 数值变化动画
    static func valueChangeAnimation() -> Animation {
        .easeInOut(duration: 0.5)
    }
    
    /// 图表更新动画
    static func chartUpdateAnimation() -> Animation {
        .easeInOut(duration: 0.8)
    }
    
    /// 进度条动画
    static func progressAnimation() -> Animation {
        .easeInOut(duration: 0.6)
    }
}

// MARK: - 动画视图修饰符

extension View {
    
    // MARK: - 按钮动画
    
    func buttonPressEffect() -> some View {
        self
            .scaleEffect(1.0)
            .onTapGesture {
                withAnimation(AnimationManager.buttonPressAnimation()) {
                    // 按下效果
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(AnimationManager.buttonReleaseAnimation()) {
                        // 释放效果
                    }
                }
            }
    }
    
    func floatingButtonEffect(isVisible: Bool) -> some View {
        self
            .scaleEffect(isVisible ? 1.0 : 0.0)
            .opacity(isVisible ? 1.0 : 0.0)
            .animation(AnimationManager.floatingButtonAnimation(), value: isVisible)
    }
    
    // MARK: - 加载动画
    
    func pulseEffect(isActive: Bool = true) -> some View {
        self
            .opacity(isActive ? 0.6 : 1.0)
            .animation(
                isActive ? AnimationManager.pulseAnimation() : .default,
                value: isActive
            )
    }
    
    func rotationEffect(isActive: Bool = true) -> some View {
        self
            .rotationEffect(.degrees(isActive ? 360 : 0))
            .animation(
                isActive ? AnimationManager.rotationAnimation() : .default,
                value: isActive
            )
    }
    
    func breathingEffect(isActive: Bool = true) -> some View {
        self
            .scaleEffect(isActive ? 1.1 : 1.0)
            .animation(
                isActive ? AnimationManager.breathingAnimation() : .default,
                value: isActive
            )
    }
    
    // MARK: - 出现动画
    
    func slideInFromBottom(delay: Double = 0) -> some View {
        self
            .offset(y: 50)
            .opacity(0)
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(delay)) {
                    // 动画到最终位置
                }
            }
    }
    
    func slideInFromRight(delay: Double = 0) -> some View {
        self
            .offset(x: 50)
            .opacity(0)
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(delay)) {
                    // 动画到最终位置
                }
            }
    }
    
    func fadeInWithScale(delay: Double = 0) -> some View {
        self
            .scaleEffect(0.8)
            .opacity(0)
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(delay)) {
                    // 动画到最终状态
                }
            }
    }
    
    // MARK: - 交互动画
    
    func cardTapEffect() -> some View {
        self
            .scaleEffect(1.0)
            .onTapGesture {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                    // 点击效果
                }
            }
    }
    
    func longPressEffect() -> some View {
        self
            .scaleEffect(1.0)
            .onLongPressGesture(minimumDuration: 0.5) {
                // 长按完成
            } onPressingChanged: { pressing in
                withAnimation(AnimationManager.longPressAnimation()) {
                    // 长按状态变化
                }
            }
    }
    
    // MARK: - 条件动画
    
    func conditionalAnimation<T: Equatable>(
        value: T,
        animation: Animation = .default
    ) -> some View {
        self
            .animation(animation, value: value)
    }
    
    func delayedAnimation<T: Equatable>(
        value: T,
        delay: Double,
        animation: Animation = .default
    ) -> some View {
        self
            .animation(animation.delay(delay), value: value)
    }
}

// MARK: - 自定义动画视图

struct AnimatedCounter: View {
    let value: Double
    let formatter: NumberFormatter
    
    @State private var animatedValue: Double = 0
    
    init(value: Double, formatter: NumberFormatter = NumberFormatter()) {
        self.value = value
        self.formatter = formatter
    }
    
    var body: some View {
        Text(formatter.string(from: NSNumber(value: animatedValue)) ?? "0")
            .onAppear {
                withAnimation(AnimationManager.valueChangeAnimation()) {
                    animatedValue = value
                }
            }
            .onChange(of: value) { newValue in
                withAnimation(AnimationManager.valueChangeAnimation()) {
                    animatedValue = newValue
                }
            }
    }
}

struct AnimatedProgressBar: View {
    let progress: Double
    let height: CGFloat
    let color: Color
    
    @State private var animatedProgress: Double = 0
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(height: height)
                
                Rectangle()
                    .fill(color)
                    .frame(width: geometry.size.width * animatedProgress, height: height)
            }
        }
        .frame(height: height)
        .onAppear {
            withAnimation(AnimationManager.progressAnimation()) {
                animatedProgress = progress
            }
        }
        .onChange(of: progress) { newProgress in
            withAnimation(AnimationManager.progressAnimation()) {
                animatedProgress = newProgress
            }
        }
    }
}

// MARK: - Edge扩展

extension Edge {
    var opposite: Edge {
        switch self {
        case .top: return .bottom
        case .bottom: return .top
        case .leading: return .trailing
        case .trailing: return .leading
        }
    }
}
