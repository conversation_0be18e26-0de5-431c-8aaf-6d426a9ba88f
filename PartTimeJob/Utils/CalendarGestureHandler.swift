//
//  CalendarGestureHandler.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 日历手势处理器
struct CalendarGestureHandler: ViewModifier {
    @Binding var selectedDate: Date
    @Binding var calendarViewType: CalendarViewType
    
    let onDateChange: (Date) -> Void
    let onViewTypeChange: (CalendarViewType) -> Void
    
    @State private var dragOffset: CGSize = .zero
    @State private var isDragging = false
    
    func body(content: Content) -> some View {
        content
            .offset(x: dragOffset.width)
            .scaleEffect(isDragging ? 0.98 : 1.0)
            .animation(.interactiveSpring(response: 0.3, dampingFraction: 0.8), value: dragOffset)
            .animation(.easeInOut(duration: 0.2), value: isDragging)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        if !isDragging {
                            isDragging = true
                        }
                        dragOffset = value.translation
                    }
                    .onEnded { value in
                        isDragging = false
                        
                        let threshold: CGFloat = 100
                        let velocity = value.predictedEndTranslation.width
                        
                        if abs(value.translation.width) > threshold || abs(velocity) > 500 {
                            if value.translation.width > 0 {
                                // 向右滑动 - 上一个时间段
                                navigateToPrevious()
                            } else {
                                // 向左滑动 - 下一个时间段
                                navigateToNext()
                            }
                        }
                        
                        // 重置偏移
                        withAnimation(.easeOut(duration: 0.3)) {
                            dragOffset = .zero
                        }
                    }
            )
    }
    
    private func navigateToPrevious() {
        let calendar = Calendar.current
        let newDate: Date
        
        switch calendarViewType {
        case .month:
            newDate = calendar.date(byAdding: .month, value: -1, to: selectedDate) ?? selectedDate
        case .week:
            newDate = calendar.date(byAdding: .weekOfYear, value: -1, to: selectedDate) ?? selectedDate
        case .list:
            newDate = calendar.date(byAdding: .month, value: -1, to: selectedDate) ?? selectedDate
        }
        
        withAnimation(.easeInOut(duration: 0.3)) {
            selectedDate = newDate
            onDateChange(newDate)
        }
    }
    
    private func navigateToNext() {
        let calendar = Calendar.current
        let newDate: Date
        
        switch calendarViewType {
        case .month:
            newDate = calendar.date(byAdding: .month, value: 1, to: selectedDate) ?? selectedDate
        case .week:
            newDate = calendar.date(byAdding: .weekOfYear, value: 1, to: selectedDate) ?? selectedDate
        case .list:
            newDate = calendar.date(byAdding: .month, value: 1, to: selectedDate) ?? selectedDate
        }
        
        withAnimation(.easeInOut(duration: 0.3)) {
            selectedDate = newDate
            onDateChange(newDate)
        }
    }
}

// MARK: - View Extension
extension View {
    func calendarGestures(
        selectedDate: Binding<Date>,
        calendarViewType: Binding<CalendarViewType>,
        onDateChange: @escaping (Date) -> Void = { _ in },
        onViewTypeChange: @escaping (CalendarViewType) -> Void = { _ in }
    ) -> some View {
        self.modifier(
            CalendarGestureHandler(
                selectedDate: selectedDate,
                calendarViewType: calendarViewType,
                onDateChange: onDateChange,
                onViewTypeChange: onViewTypeChange
            )
        )
    }
}

// MARK: - 日历键盘快捷键处理器
struct CalendarKeyboardHandler: ViewModifier {
    @Binding var selectedDate: Date
    @Binding var calendarViewType: CalendarViewType
    
    let onAddWork: () -> Void
    let onToday: () -> Void
    
    func body(content: Content) -> some View {
        content
            .onReceive(NotificationCenter.default.publisher(for: .keyboardShortcut)) { notification in
                guard let shortcut = notification.object as? KeyboardShortcut else { return }
                handleKeyboardShortcut(shortcut)
            }
    }
    
    private func handleKeyboardShortcut(_ shortcut: KeyboardShortcut) {
        switch shortcut {
        case .addWork:
            onAddWork()
        case .today:
            onToday()
        case .previousMonth:
            navigateToPrevious(.month)
        case .nextMonth:
            navigateToNext(.month)
        case .previousWeek:
            navigateToPrevious(.weekOfYear)
        case .nextWeek:
            navigateToNext(.weekOfYear)
        case .monthView:
            calendarViewType = .month
        case .weekView:
            calendarViewType = .week
        case .listView:
            calendarViewType = .list
        }
    }
    
    private func navigateToPrevious(_ component: Calendar.Component) {
        let calendar = Calendar.current
        if let newDate = calendar.date(byAdding: component, value: -1, to: selectedDate) {
            withAnimation(.easeInOut(duration: 0.3)) {
                selectedDate = newDate
            }
        }
    }
    
    private func navigateToNext(_ component: Calendar.Component) {
        let calendar = Calendar.current
        if let newDate = calendar.date(byAdding: component, value: 1, to: selectedDate) {
            withAnimation(.easeInOut(duration: 0.3)) {
                selectedDate = newDate
            }
        }
    }
}

// MARK: - 键盘快捷键枚举
enum KeyboardShortcut {
    case addWork
    case today
    case previousMonth
    case nextMonth
    case previousWeek
    case nextWeek
    case monthView
    case weekView
    case listView
}

// MARK: - 通知扩展
extension Notification.Name {
    static let keyboardShortcut = Notification.Name("keyboardShortcut")
}

// MARK: - View Extension for Keyboard
extension View {
    func calendarKeyboardShortcuts(
        selectedDate: Binding<Date>,
        calendarViewType: Binding<CalendarViewType>,
        onAddWork: @escaping () -> Void,
        onToday: @escaping () -> Void
    ) -> some View {
        self.modifier(
            CalendarKeyboardHandler(
                selectedDate: selectedDate,
                calendarViewType: calendarViewType,
                onAddWork: onAddWork,
                onToday: onToday
            )
        )
    }
}

// MARK: - 日历动画配置
struct CalendarAnimationConfig {
    static let defaultDuration: Double = 0.3
    static let fastDuration: Double = 0.2
    static let slowDuration: Double = 0.5
    
    static let defaultSpring = Animation.interactiveSpring(response: 0.3, dampingFraction: 0.8)
    static let fastSpring = Animation.interactiveSpring(response: 0.2, dampingFraction: 0.9)
    static let slowSpring = Animation.interactiveSpring(response: 0.5, dampingFraction: 0.7)
    
    static let easeInOut = Animation.easeInOut(duration: defaultDuration)
    static let easeOut = Animation.easeOut(duration: defaultDuration)
    static let easeIn = Animation.easeIn(duration: defaultDuration)
}

// MARK: - 日历触觉反馈
struct CalendarHapticFeedback {
    static func selectionChanged() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    static func dateNavigated() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    static func workRecordTapped() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .rigid)
        impactFeedback.impactOccurred()
    }
    
    static func longPressDetected() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
    }
    
    static func errorOccurred() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.error)
    }
    
    static func successAction() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }
}
