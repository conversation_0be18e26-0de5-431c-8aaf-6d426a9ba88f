//
//  ErrorHandler.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation
import SwiftUI
import os.log

/// 全局错误处理器
@MainActor
class ErrorHandler: ObservableObject {
    static let shared = ErrorHandler()
    
    private let logger = Logger(subsystem: "com.parttimejob.app", category: "ErrorHandler")
    
    @Published var currentError: AppError?
    @Published var showingErrorAlert = false
    @Published var errorHistory: [ErrorRecord] = []
    
    private var maxErrorHistory = 100
    
    private init() {}
    
    // MARK: - 错误处理
    
    func handle(_ error: Error, context: String = "", showToUser: Bool = true) {
        let appError = AppError.from(error, context: context)
        
        // 记录错误
        logError(appError, context: context)
        
        // 添加到历史记录
        addToHistory(appError, context: context)
        
        // 显示给用户
        if showToUser {
            showError(appError)
        }
        
        // 发送崩溃报告（如果是严重错误）
        if appError.severity == .critical {
            sendCrashReport(appError, context: context)
        }
    }
    
    func handle(_ appError: AppError, context: String = "", showToUser: Bool = true) {
        // 记录错误
        logError(appError, context: context)
        
        // 添加到历史记录
        addToHistory(appError, context: context)
        
        // 显示给用户
        if showToUser {
            showError(appError)
        }
        
        // 发送崩溃报告（如果是严重错误）
        if appError.severity == .critical {
            sendCrashReport(appError, context: context)
        }
    }
    
    private func showError(_ error: AppError) {
        currentError = error
        showingErrorAlert = true
        
        // 触觉反馈
        CalendarHapticFeedback.errorOccurred()
    }
    
    private func logError(_ error: AppError, context: String) {
        let logMessage = """
        Error: \(error.title)
        Description: \(error.description)
        Context: \(context)
        Severity: \(error.severity.rawValue)
        Code: \(error.code)
        """
        
        switch error.severity {
        case .low:
            logger.info("\(logMessage)")
        case .medium:
            logger.notice("\(logMessage)")
        case .high:
            logger.error("\(logMessage)")
        case .critical:
            logger.fault("\(logMessage)")
        }
    }
    
    private func addToHistory(_ error: AppError, context: String) {
        let record = ErrorRecord(
            error: error,
            context: context,
            timestamp: Date()
        )
        
        errorHistory.insert(record, at: 0)
        
        // 限制历史记录数量
        if errorHistory.count > maxErrorHistory {
            errorHistory.removeLast()
        }
    }
    
    private func sendCrashReport(_ error: AppError, context: String) {
        // 这里可以集成第三方崩溃报告服务
        // 例如 Firebase Crashlytics, Sentry 等
        logger.critical("Critical error occurred - would send crash report in production")
    }
    
    // MARK: - 错误恢复
    
    func attemptRecovery(for error: AppError) -> Bool {
        switch error.category {
        case .database:
            return attemptDatabaseRecovery()
        case .network:
            return attemptNetworkRecovery()
        case .storage:
            return attemptStorageRecovery()
        case .validation:
            return false // 验证错误通常需要用户修正
        case .system:
            return attemptSystemRecovery()
        case .unknown:
            return false
        }
    }
    
    private func attemptDatabaseRecovery() -> Bool {
        // 尝试重新初始化数据库连接
        do {
            // DataManager.shared.reinitialize()
            logger.info("Database recovery attempted")
            return true
        } catch {
            logger.error("Database recovery failed: \(error)")
            return false
        }
    }
    
    private func attemptNetworkRecovery() -> Bool {
        // 检查网络连接状态
        // 可以集成 Network framework 进行网络状态检测
        logger.info("Network recovery attempted")
        return true
    }
    
    private func attemptStorageRecovery() -> Bool {
        // 清理临时文件，释放存储空间
        do {
            let tempDir = FileManager.default.temporaryDirectory
            let tempFiles = try FileManager.default.contentsOfDirectory(at: tempDir, includingPropertiesForKeys: nil)
            for file in tempFiles {
                try? FileManager.default.removeItem(at: file)
            }
            logger.info("Storage recovery completed")
            return true
        } catch {
            logger.error("Storage recovery failed: \(error)")
            return false
        }
    }
    
    private func attemptSystemRecovery() -> Bool {
        // 执行系统级恢复操作
        PerformanceMonitor.shared.performMemoryCleanup()
        logger.info("System recovery attempted")
        return true
    }
    
    // MARK: - 错误统计
    
    func getErrorStatistics() -> ErrorStatistics {
        let totalErrors = errorHistory.count
        let errorsByCategory = Dictionary(grouping: errorHistory) { $0.error.category }
        let errorsBySeverity = Dictionary(grouping: errorHistory) { $0.error.severity }
        
        let recentErrors = errorHistory.filter { 
            $0.timestamp.timeIntervalSinceNow > -3600 // 最近1小时
        }.count
        
        return ErrorStatistics(
            totalErrors: totalErrors,
            recentErrors: recentErrors,
            errorsByCategory: errorsByCategory.mapValues { $0.count },
            errorsBySeverity: errorsBySeverity.mapValues { $0.count },
            mostCommonError: findMostCommonError()
        )
    }
    
    private func findMostCommonError() -> String? {
        let errorCounts = Dictionary(grouping: errorHistory) { $0.error.code }
            .mapValues { $0.count }
        
        return errorCounts.max(by: { $0.value < $1.value })?.key
    }
    
    // MARK: - 清理
    
    func clearErrorHistory() {
        errorHistory.removeAll()
        logger.info("Error history cleared")
    }
    
    func dismissCurrentError() {
        currentError = nil
        showingErrorAlert = false
    }
}

// MARK: - 应用错误定义

struct AppError: Error, Identifiable {
    let id = UUID()
    let code: String
    let title: String
    let description: String
    let category: ErrorCategory
    let severity: ErrorSeverity
    let userInfo: [String: Any]
    
    init(
        code: String,
        title: String,
        description: String,
        category: ErrorCategory = .unknown,
        severity: ErrorSeverity = .medium,
        userInfo: [String: Any] = [:]
    ) {
        self.code = code
        self.title = title
        self.description = description
        self.category = category
        self.severity = severity
        self.userInfo = userInfo
    }
    
    static func from(_ error: Error, context: String = "") -> AppError {
        if let appError = error as? AppError {
            return appError
        }
        
        // 根据错误类型创建相应的 AppError
        let nsError = error as NSError
        
        switch nsError.domain {
        case NSURLErrorDomain:
            return networkError(from: nsError)
        case NSCocoaErrorDomain:
            return storageError(from: nsError)
        default:
            return AppError(
                code: "UNKNOWN_ERROR",
                title: "未知错误",
                description: error.localizedDescription,
                category: .unknown,
                severity: .medium
            )
        }
    }
    
    private static func networkError(from error: NSError) -> AppError {
        switch error.code {
        case NSURLErrorNotConnectedToInternet:
            return AppError(
                code: "NO_INTERNET",
                title: "网络连接失败",
                description: "请检查您的网络连接",
                category: .network,
                severity: .medium
            )
        case NSURLErrorTimedOut:
            return AppError(
                code: "NETWORK_TIMEOUT",
                title: "网络超时",
                description: "请求超时，请稍后重试",
                category: .network,
                severity: .medium
            )
        default:
            return AppError(
                code: "NETWORK_ERROR",
                title: "网络错误",
                description: error.localizedDescription,
                category: .network,
                severity: .medium
            )
        }
    }
    
    private static func storageError(from error: NSError) -> AppError {
        switch error.code {
        case NSFileWriteFileExistsError:
            return AppError(
                code: "FILE_EXISTS",
                title: "文件已存在",
                description: "目标文件已存在",
                category: .storage,
                severity: .low
            )
        case NSFileWriteNoPermissionError:
            return AppError(
                code: "NO_PERMISSION",
                title: "权限不足",
                description: "没有写入权限",
                category: .storage,
                severity: .high
            )
        default:
            return AppError(
                code: "STORAGE_ERROR",
                title: "存储错误",
                description: error.localizedDescription,
                category: .storage,
                severity: .medium
            )
        }
    }
}

// MARK: - 错误分类和严重程度

enum ErrorCategory: String, CaseIterable {
    case database = "database"
    case network = "network"
    case storage = "storage"
    case validation = "validation"
    case system = "system"
    case unknown = "unknown"
    
    var displayName: String {
        switch self {
        case .database:
            return "数据库"
        case .network:
            return "网络"
        case .storage:
            return "存储"
        case .validation:
            return "验证"
        case .system:
            return "系统"
        case .unknown:
            return "未知"
        }
    }
    
    var icon: String {
        switch self {
        case .database:
            return "cylinder.fill"
        case .network:
            return "wifi.exclamationmark"
        case .storage:
            return "externaldrive.fill.badge.xmark"
        case .validation:
            return "checkmark.circle.badge.xmark"
        case .system:
            return "gear.badge.xmark"
        case .unknown:
            return "questionmark.circle"
        }
    }
}

enum ErrorSeverity: String, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .low:
            return "低"
        case .medium:
            return "中"
        case .high:
            return "高"
        case .critical:
            return "严重"
        }
    }
    
    var color: Color {
        switch self {
        case .low:
            return .green
        case .medium:
            return .yellow
        case .high:
            return .orange
        case .critical:
            return .red
        }
    }
}

// MARK: - 错误记录和统计

struct ErrorRecord {
    let error: AppError
    let context: String
    let timestamp: Date
}

struct ErrorStatistics {
    let totalErrors: Int
    let recentErrors: Int
    let errorsByCategory: [ErrorCategory: Int]
    let errorsBySeverity: [ErrorSeverity: Int]
    let mostCommonError: String?
}

// MARK: - 常见错误定义

extension AppError {
    // 数据库错误
    static let databaseConnectionFailed = AppError(
        code: "DB_CONNECTION_FAILED",
        title: "数据库连接失败",
        description: "无法连接到数据库，请重启应用",
        category: .database,
        severity: .high
    )
    
    static let dataCorrupted = AppError(
        code: "DATA_CORRUPTED",
        title: "数据损坏",
        description: "检测到数据损坏，正在尝试修复",
        category: .database,
        severity: .critical
    )
    
    // 验证错误
    static let invalidWorkTime = AppError(
        code: "INVALID_WORK_TIME",
        title: "工作时间无效",
        description: "结束时间必须晚于开始时间",
        category: .validation,
        severity: .low
    )
    
    static let duplicateWorkRecord = AppError(
        code: "DUPLICATE_WORK_RECORD",
        title: "工作记录冲突",
        description: "该时间段已有工作记录",
        category: .validation,
        severity: .medium
    )
    
    // 存储错误
    static let diskSpaceFull = AppError(
        code: "DISK_SPACE_FULL",
        title: "存储空间不足",
        description: "设备存储空间不足，请清理后重试",
        category: .storage,
        severity: .high
    )
    
    // 系统错误
    static let memoryWarning = AppError(
        code: "MEMORY_WARNING",
        title: "内存不足",
        description: "系统内存不足，正在清理缓存",
        category: .system,
        severity: .medium
    )
}

// MARK: - 错误处理视图修饰符

struct ErrorHandlingModifier: ViewModifier {
    @StateObject private var errorHandler = ErrorHandler.shared
    
    func body(content: Content) -> some View {
        content
            .alert("错误", isPresented: $errorHandler.showingErrorAlert) {
                if let error = errorHandler.currentError {
                    Button("确定") {
                        errorHandler.dismissCurrentError()
                    }
                    
                    if errorHandler.attemptRecovery(for: error) {
                        Button("重试") {
                            errorHandler.dismissCurrentError()
                            // 这里可以触发重试逻辑
                        }
                    }
                }
            } message: {
                if let error = errorHandler.currentError {
                    Text(error.description)
                }
            }
    }
}

extension View {
    func withErrorHandling() -> some View {
        self.modifier(ErrorHandlingModifier())
    }
}
