//
//  AppInitializer.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation
import SwiftUI
import os.log

/// 应用初始化管理器
@MainActor
class AppInitializer: ObservableObject {
    static let shared = AppInitializer()
    
    private let logger = Logger(subsystem: "com.parttimejob.app", category: "AppInitializer")
    
    @Published var initializationState: InitializationState = .notStarted
    @Published var initializationProgress: Double = 0
    @Published var currentStep: String = ""
    @Published var isFirstLaunch: Bool = false
    
    private var initializationSteps: [InitializationStep] = []
    
    private init() {
        setupInitializationSteps()
    }
    
    // MARK: - 初始化步骤设置
    
    private func setupInitializationSteps() {
        initializationSteps = [
            InitializationStep(
                name: "检查应用版本",
                action: checkAppVersion
            ),
            InitializationStep(
                name: "初始化配置",
                action: initializeConfiguration
            ),
            InitializationStep(
                name: "初始化数据库",
                action: initializeDatabase
            ),
            InitializationStep(
                name: "加载用户偏好",
                action: loadUserPreferences
            ),
            InitializationStep(
                name: "初始化服务",
                action: initializeServices
            ),
            InitializationStep(
                name: "检查数据迁移",
                action: checkDataMigration
            ),
            InitializationStep(
                name: "设置通知",
                action: setupNotifications
            ),
            InitializationStep(
                name: "启动性能监控",
                action: startPerformanceMonitoring
            ),
            InitializationStep(
                name: "完成初始化",
                action: finalizeInitialization
            )
        ]
    }
    
    // MARK: - 初始化控制
    
    func startInitialization() async {
        guard initializationState == .notStarted else { return }
        
        logger.info("Starting app initialization")
        initializationState = .inProgress
        initializationProgress = 0
        
        do {
            for (index, step) in initializationSteps.enumerated() {
                currentStep = step.name
                logger.info("Executing step: \(step.name)")
                
                try await step.action()
                
                let progress = Double(index + 1) / Double(initializationSteps.count)
                initializationProgress = progress
                
                // 添加小延迟以显示进度
                try await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
            }
            
            initializationState = .completed
            logger.info("App initialization completed successfully")
            
        } catch {
            initializationState = .failed(error)
            logger.error("App initialization failed: \(error)")
            ErrorHandler.shared.handle(error, context: "App Initialization")
        }
    }
    
    func retryInitialization() async {
        initializationState = .notStarted
        await startInitialization()
    }
    
    // MARK: - 初始化步骤实现
    
    private func checkAppVersion() async throws {
        let currentVersion = AppConfiguration.shared.appVersion
        let lastVersion = UserDefaults.standard.string(forKey: "last_app_version")
        
        if lastVersion == nil {
            isFirstLaunch = true
            logger.info("First app launch detected")
        } else if lastVersion != currentVersion {
            logger.info("App version updated from \(lastVersion ?? "unknown") to \(currentVersion)")
            // 处理版本更新逻辑
            await handleVersionUpdate(from: lastVersion, to: currentVersion)
        }
        
        UserDefaults.standard.set(currentVersion, forKey: "last_app_version")
    }
    
    private func initializeConfiguration() async throws {
        // 初始化应用配置
        let config = AppConfiguration.shared
        
        // 设置日志级别
        // Logger.setLevel(config.logLevel)
        
        // 初始化错误处理器
        // ErrorHandler.shared.initialize()
        
        logger.info("Configuration initialized")
    }
    
    private func initializeDatabase() async throws {
        // 初始化 SwiftData 数据库
        let dataManager = DataManager.shared
        
        // 检查数据库完整性
        let isHealthy = await dataManager.checkDatabaseHealth()
        if !isHealthy {
            logger.warning("Database health check failed, attempting repair")
            await dataManager.repairDatabase()
        }
        
        // 创建默认工作类型（如果是首次启动）
        if isFirstLaunch {
            await dataManager.createDefaultWorkTypes()
        }
        
        logger.info("Database initialized")
    }
    
    private func loadUserPreferences() async throws {
        let preferences = UserPreferences.shared
        
        // 如果是首次启动，设置首次使用日期
        if isFirstLaunch {
            preferences.firstUseDate = Date()
        }
        
        // 加载保存的偏好设置
        preferences.loadPreferences()
        
        logger.info("User preferences loaded")
    }
    
    private func initializeServices() async throws {
        // 初始化缓存服务
        CacheService.shared.initialize()
        
        // 初始化通知管理器
        await NotificationManager.shared.initialize()
        
        // 初始化数据同步服务（如果启用）
        if AppConfiguration.shared.isFeatureEnabled(.cloudSync) {
            // DataSyncService.shared.initialize()
        }
        
        logger.info("Services initialized")
    }
    
    private func checkDataMigration() async throws {
        let migrationService = MigrationService.shared
        
        // 检查是否需要数据迁移
        let needsMigration = await migrationService.checkMigrationNeeded()
        
        if needsMigration {
            logger.info("Data migration needed, starting migration")
            try await migrationService.performMigration()
            logger.info("Data migration completed")
        }
    }
    
    private func setupNotifications() async throws {
        let notificationManager = NotificationManager.shared
        
        // 请求通知权限（如果用户启用了通知）
        if UserPreferences.shared.enableNotifications {
            await notificationManager.requestPermission()
        }
        
        // 设置现有工作记录的提醒
        await notificationManager.scheduleExistingReminders()
        
        logger.info("Notifications setup completed")
    }
    
    private func startPerformanceMonitoring() async throws {
        // 如果启用了性能监控，开始监控
        if AppConfiguration.shared.enablePerformanceMonitoring {
            PerformanceMonitor.shared.startMonitoring()
            logger.info("Performance monitoring started")
        }
    }
    
    private func finalizeInitialization() async throws {
        // 清理临时文件
        cleanupTemporaryFiles()
        
        // 记录启动完成
        UserDefaults.standard.set(Date(), forKey: "last_launch_date")
        
        // 增加启动次数
        let launchCount = UserDefaults.standard.integer(forKey: "launch_count") + 1
        UserDefaults.standard.set(launchCount, forKey: "launch_count")
        
        logger.info("App initialization finalized. Launch count: \(launchCount)")
    }
    
    // MARK: - 版本更新处理
    
    private func handleVersionUpdate(from oldVersion: String?, to newVersion: String) async {
        // 处理版本更新逻辑
        logger.info("Handling version update")
        
        // 可以在这里添加版本特定的迁移逻辑
        // 例如：数据格式更新、新功能介绍等
        
        // 清理旧版本的缓存
        CacheService.shared.clearCache()
        
        // 显示更新日志（可选）
        // showUpdateLog(from: oldVersion, to: newVersion)
    }
    
    // MARK: - 清理工作
    
    private func cleanupTemporaryFiles() {
        let tempDirectory = FileManager.default.temporaryDirectory
        
        do {
            let tempFiles = try FileManager.default.contentsOfDirectory(
                at: tempDirectory,
                includingPropertiesForKeys: [.creationDateKey]
            )
            
            let calendar = Calendar.current
            let cutoffDate = calendar.date(byAdding: .day, value: -7, to: Date()) ?? Date()
            
            for file in tempFiles {
                do {
                    let resourceValues = try file.resourceValues(forKeys: [.creationDateKey])
                    if let creationDate = resourceValues.creationDate,
                       creationDate < cutoffDate {
                        try FileManager.default.removeItem(at: file)
                        logger.debug("Removed old temp file: \(file.lastPathComponent)")
                    }
                } catch {
                    logger.warning("Failed to remove temp file: \(file.lastPathComponent)")
                }
            }
        } catch {
            logger.error("Failed to cleanup temporary files: \(error)")
        }
    }
    
    // MARK: - 健康检查
    
    func performHealthCheck() async -> HealthCheckResult {
        var issues: [String] = []
        
        // 检查数据库健康状态
        let dbHealthy = await DataManager.shared.checkDatabaseHealth()
        if !dbHealthy {
            issues.append("数据库健康检查失败")
        }
        
        // 检查存储空间
        let freeSpace = getAvailableStorageSpace()
        if freeSpace < 100 * 1024 * 1024 { // 少于100MB
            issues.append("存储空间不足")
        }
        
        // 检查内存使用
        let memoryUsage = getMemoryUsage()
        if memoryUsage > 0.8 { // 超过80%
            issues.append("内存使用过高")
        }
        
        return HealthCheckResult(
            isHealthy: issues.isEmpty,
            issues: issues,
            timestamp: Date()
        )
    }
    
    private func getAvailableStorageSpace() -> Int64 {
        do {
            let systemAttributes = try FileManager.default.attributesOfFileSystem(forPath: NSHomeDirectory())
            return systemAttributes[.systemFreeSize] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
    
    private func getMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let totalMemory = Double(ProcessInfo.processInfo.physicalMemory)
            let usedMemory = Double(info.resident_size)
            return usedMemory / totalMemory
        }
        
        return 0
    }
}

// MARK: - 数据结构

enum InitializationState: Equatable {
    case notStarted
    case inProgress
    case completed
    case failed(Error)
    
    static func == (lhs: InitializationState, rhs: InitializationState) -> Bool {
        switch (lhs, rhs) {
        case (.notStarted, .notStarted),
             (.inProgress, .inProgress),
             (.completed, .completed):
            return true
        case (.failed, .failed):
            return true
        default:
            return false
        }
    }
}

struct InitializationStep {
    let name: String
    let action: () async throws -> Void
}

struct HealthCheckResult {
    let isHealthy: Bool
    let issues: [String]
    let timestamp: Date
}

// MARK: - 启动画面视图

struct LaunchScreenView: View {
    @StateObject private var initializer = AppInitializer.shared
    @State private var showMainApp = false
    
    var body: some View {
        ZStack {
            // 背景渐变
            LinearGradient(
                colors: [.blue, .purple],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 40) {
                // 应用图标
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white.opacity(0.2))
                    .frame(width: 120, height: 120)
                    .overlay(
                        Image(systemName: "briefcase.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.white)
                    )
                
                // 应用名称
                Text("兼职记录助手")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                // 初始化状态
                VStack(spacing: 16) {
                    switch initializer.initializationState {
                    case .notStarted, .inProgress:
                        VStack(spacing: 12) {
                            ProgressView(value: initializer.initializationProgress)
                                .progressViewStyle(LinearProgressViewStyle(tint: .white))
                                .frame(width: 200)
                            
                            Text(initializer.currentStep)
                                .font(.body)
                                .foregroundColor(.white.opacity(0.8))
                        }
                        
                    case .completed:
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("初始化完成")
                                .foregroundColor(.white)
                        }
                        .font(.headline)
                        
                    case .failed(let error):
                        VStack(spacing: 12) {
                            HStack {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.red)
                                Text("初始化失败")
                                    .foregroundColor(.white)
                            }
                            .font(.headline)
                            
                            Button("重试") {
                                Task {
                                    await initializer.retryInitialization()
                                }
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 8)
                            .background(Color.white.opacity(0.2))
                            .cornerRadius(8)
                        }
                    }
                }
            }
        }
        .onAppear {
            Task {
                await initializer.startInitialization()
                
                // 初始化完成后延迟一下再显示主应用
                if initializer.initializationState == .completed {
                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                    showMainApp = true
                }
            }
        }
        .onChange(of: initializer.initializationState) { state in
            if case .completed = state {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    showMainApp = true
                }
            }
        }
        .fullScreenCover(isPresented: $showMainApp) {
            MainTabView()
                .environmentObject(DataManager.shared)
                .environmentObject(UserPreferences.shared)
        }
    }
}

#Preview {
    LaunchScreenView()
}
