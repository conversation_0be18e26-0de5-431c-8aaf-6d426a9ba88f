//
//  DesignSystem.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 设计系统 - 统一的设计语言和样式
struct DesignSystem {
    
    // MARK: - 颜色系统
    
    struct Colors {
        // 主色调
        static let primary = Color.blue
        static let primaryLight = Color.blue.opacity(0.1)
        static let primaryDark = Color(red: 0.0, green: 0.3, blue: 0.8)
        
        // 辅助色
        static let secondary = Color.gray
        static let accent = Color.orange
        static let success = Color.green
        static let warning = Color.orange
        static let error = Color.red
        
        // 背景色
        static let background = Color(.systemBackground)
        static let secondaryBackground = Color(.secondarySystemBackground)
        static let tertiaryBackground = Color(.tertiarySystemBackground)
        static let groupedBackground = Color(.systemGroupedBackground)
        
        // 文本色
        static let primaryText = Color(.label)
        static let secondaryText = Color(.secondaryLabel)
        static let tertiaryText = Color(.tertiaryLabel)
        static let placeholderText = Color(.placeholderText)
        
        // 分隔线
        static let separator = Color(.separator)
        static let opaqueSeparator = Color(.opaqueSeparator)
        
        // 工作状态色
        static let workInProgress = Color.orange
        static let workCompleted = Color.green
        static let workUpcoming = Color.blue
        
        // 收入等级色
        static let lowIncome = Color.red
        static let mediumIncome = Color.orange
        static let highIncome = Color.green
        static let excellentIncome = Color.blue
    }
    
    // MARK: - 字体系统
    
    struct Typography {
        // 标题字体
        static let largeTitle = Font.largeTitle.weight(.bold)
        static let title1 = Font.title.weight(.semibold)
        static let title2 = Font.title2.weight(.semibold)
        static let title3 = Font.title3.weight(.medium)
        
        // 正文字体
        static let headline = Font.headline.weight(.medium)
        static let body = Font.body
        static let bodyMedium = Font.body.weight(.medium)
        static let callout = Font.callout
        static let subheadline = Font.subheadline
        
        // 辅助字体
        static let footnote = Font.footnote
        static let caption1 = Font.caption
        static let caption2 = Font.caption2
        
        // 数字字体
        static let monospacedDigit = Font.body.monospacedDigit()
        static let largeMonospacedDigit = Font.title2.monospacedDigit().weight(.semibold)
    }
    
    // MARK: - 间距系统
    
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 20
        static let xxl: CGFloat = 24
        static let xxxl: CGFloat = 32
        
        // 特定用途间距
        static let cardPadding: CGFloat = 16
        static let sectionSpacing: CGFloat = 20
        static let itemSpacing: CGFloat = 12
        static let buttonPadding: CGFloat = 16
    }
    
    // MARK: - 圆角系统
    
    struct CornerRadius {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 6
        static let md: CGFloat = 8
        static let lg: CGFloat = 12
        static let xl: CGFloat = 16
        static let xxl: CGFloat = 20
        
        // 特定组件圆角
        static let button: CGFloat = 12
        static let card: CGFloat = 12
        static let sheet: CGFloat = 16
        static let avatar: CGFloat = 25 // 50x50头像的一半
    }
    
    // MARK: - 阴影系统
    
    struct Shadow {
        static let light = (color: Color.black.opacity(0.1), radius: CGFloat(2), x: CGFloat(0), y: CGFloat(1))
        static let medium = (color: Color.black.opacity(0.15), radius: CGFloat(4), x: CGFloat(0), y: CGFloat(2))
        static let heavy = (color: Color.black.opacity(0.2), radius: CGFloat(8), x: CGFloat(0), y: CGFloat(4))
        
        // 卡片阴影
        static let card = medium
        static let floatingButton = heavy
    }
    
    // MARK: - 动画系统
    
    struct Animation {
        static let quick = SwiftUI.Animation.easeInOut(duration: 0.2)
        static let standard = SwiftUI.Animation.easeInOut(duration: 0.3)
        static let slow = SwiftUI.Animation.easeInOut(duration: 0.5)
        
        // 弹性动画
        static let spring = SwiftUI.Animation.spring(response: 0.5, dampingFraction: 0.8)
        static let bouncy = SwiftUI.Animation.spring(response: 0.3, dampingFraction: 0.6)
        
        // 特定用途动画
        static let buttonPress = quick
        static let cardFlip = standard
        static let pageTransition = slow
        static let modalPresentation = spring
    }
    
    // MARK: - 尺寸系统
    
    struct Size {
        // 按钮尺寸
        static let buttonHeight: CGFloat = 44
        static let smallButtonHeight: CGFloat = 32
        static let largeButtonHeight: CGFloat = 56
        
        // 图标尺寸
        static let iconSmall: CGFloat = 16
        static let iconMedium: CGFloat = 24
        static let iconLarge: CGFloat = 32
        static let iconXLarge: CGFloat = 48
        
        // 头像尺寸
        static let avatarSmall: CGFloat = 32
        static let avatarMedium: CGFloat = 50
        static let avatarLarge: CGFloat = 80
        
        // 卡片尺寸
        static let cardMinHeight: CGFloat = 80
        static let cardMaxWidth: CGFloat = 400
        
        // 列表行高
        static let listRowHeight: CGFloat = 60
        static let compactListRowHeight: CGFloat = 44
    }
    
    // MARK: - 布局系统
    
    struct Layout {
        // 屏幕边距
        static let screenPadding: CGFloat = 16
        static let safeAreaPadding: CGFloat = 20
        
        // 网格列数
        static let gridColumns2 = Array(repeating: GridItem(.flexible()), count: 2)
        static let gridColumns3 = Array(repeating: GridItem(.flexible()), count: 3)
        static let gridColumns4 = Array(repeating: GridItem(.flexible()), count: 4)
        
        // 响应式断点
        static let compactWidth: CGFloat = 375  // iPhone SE
        static let regularWidth: CGFloat = 414  // iPhone Pro Max
        static let iPadWidth: CGFloat = 768     // iPad
    }
}

// MARK: - 视图修饰符扩展

extension View {
    
    // MARK: - 卡片样式
    
    func cardStyle() -> some View {
        self
            .background(DesignSystem.Colors.background)
            .cornerRadius(DesignSystem.CornerRadius.card)
            .shadow(
                color: DesignSystem.Shadow.card.color,
                radius: DesignSystem.Shadow.card.radius,
                x: DesignSystem.Shadow.card.x,
                y: DesignSystem.Shadow.card.y
            )
    }
    
    func lightCardStyle() -> some View {
        self
            .background(DesignSystem.Colors.secondaryBackground)
            .cornerRadius(DesignSystem.CornerRadius.card)
            .shadow(
                color: DesignSystem.Shadow.light.color,
                radius: DesignSystem.Shadow.light.radius,
                x: DesignSystem.Shadow.light.x,
                y: DesignSystem.Shadow.light.y
            )
    }
    
    // MARK: - 按钮样式
    
    func primaryButtonStyle() -> some View {
        self
            .font(DesignSystem.Typography.bodyMedium)
            .foregroundColor(.white)
            .frame(height: DesignSystem.Size.buttonHeight)
            .background(DesignSystem.Colors.primary)
            .cornerRadius(DesignSystem.CornerRadius.button)
    }
    
    func secondaryButtonStyle() -> some View {
        self
            .font(DesignSystem.Typography.bodyMedium)
            .foregroundColor(DesignSystem.Colors.primary)
            .frame(height: DesignSystem.Size.buttonHeight)
            .background(DesignSystem.Colors.primaryLight)
            .cornerRadius(DesignSystem.CornerRadius.button)
    }
    
    // MARK: - 动画效果
    
    func pressAnimation() -> some View {
        self
            .scaleEffect(1.0)
            .animation(DesignSystem.Animation.buttonPress, value: UUID())
    }
    
    func bounceAnimation() -> some View {
        self
            .animation(DesignSystem.Animation.bouncy, value: UUID())
    }
    
    // MARK: - 响应式布局
    
    func responsivePadding() -> some View {
        self
            .padding(.horizontal, DesignSystem.Layout.screenPadding)
    }
    
    func adaptiveFont(compact: Font, regular: Font) -> some View {
        self
            .font(UIScreen.main.bounds.width < DesignSystem.Layout.regularWidth ? compact : regular)
    }
}

// MARK: - 设备适配

extension DesignSystem {
    
    struct Device {
        static var isCompact: Bool {
            UIScreen.main.bounds.width < Layout.regularWidth
        }
        
        static var isRegular: Bool {
            UIScreen.main.bounds.width >= Layout.regularWidth
        }
        
        static var isiPad: Bool {
            UIDevice.current.userInterfaceIdiom == .pad
        }
        
        static var screenWidth: CGFloat {
            UIScreen.main.bounds.width
        }
        
        static var screenHeight: CGFloat {
            UIScreen.main.bounds.height
        }
        
        static var safeAreaInsets: UIEdgeInsets {
            UIApplication.shared.windows.first?.safeAreaInsets ?? UIEdgeInsets()
        }
    }
}
