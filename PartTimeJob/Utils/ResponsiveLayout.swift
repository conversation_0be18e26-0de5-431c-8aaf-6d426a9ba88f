//
//  ResponsiveLayout.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 响应式布局管理器
struct ResponsiveLayout {
    
    // MARK: - 设备类型
    
    enum DeviceType {
        case compact    // iPhone SE, iPhone 12 mini
        case regular    // iPhone 12, iPhone 13
        case large      // iPhone 12 Pro Max, iPhone 13 Pro Max
        case iPad       // iPad
        
        static var current: DeviceType {
            let width = UIScreen.main.bounds.width
            let idiom = UIDevice.current.userInterfaceIdiom
            
            if idiom == .pad {
                return .iPad
            } else {
                switch width {
                case 0..<375:
                    return .compact
                case 375..<414:
                    return .regular
                default:
                    return .large
                }
            }
        }
    }
    
    // MARK: - 屏幕尺寸
    
    struct Screen {
        static let width = UIScreen.main.bounds.width
        static let height = UIScreen.main.bounds.height
        static let size = UIScreen.main.bounds.size
        
        static var isCompact: Bool { DeviceType.current == .compact }
        static var isRegular: Bool { DeviceType.current == .regular }
        static var isLarge: Bool { DeviceType.current == .large }
        static var isiPad: Bool { DeviceType.current == .iPad }
        
        static var safeAreaInsets: UIEdgeInsets {
            UIApplication.shared.windows.first?.safeAreaInsets ?? UIEdgeInsets()
        }
    }
    
    // MARK: - 响应式间距
    
    struct Spacing {
        static var horizontal: CGFloat {
            switch DeviceType.current {
            case .compact:
                return 12
            case .regular:
                return 16
            case .large:
                return 20
            case .iPad:
                return 24
            }
        }
        
        static var vertical: CGFloat {
            switch DeviceType.current {
            case .compact:
                return 8
            case .regular:
                return 12
            case .large:
                return 16
            case .iPad:
                return 20
            }
        }
        
        static var cardPadding: CGFloat {
            switch DeviceType.current {
            case .compact:
                return 12
            case .regular:
                return 16
            case .large:
                return 20
            case .iPad:
                return 24
            }
        }
        
        static var sectionSpacing: CGFloat {
            switch DeviceType.current {
            case .compact:
                return 16
            case .regular:
                return 20
            case .large:
                return 24
            case .iPad:
                return 32
            }
        }
    }
    
    // MARK: - 响应式字体
    
    struct Typography {
        static var largeTitle: Font {
            switch DeviceType.current {
            case .compact:
                return .title.weight(.bold)
            case .regular:
                return .largeTitle.weight(.bold)
            case .large:
                return .largeTitle.weight(.bold)
            case .iPad:
                return .system(size: 40, weight: .bold)
            }
        }
        
        static var title: Font {
            switch DeviceType.current {
            case .compact:
                return .title2.weight(.semibold)
            case .regular:
                return .title.weight(.semibold)
            case .large:
                return .title.weight(.semibold)
            case .iPad:
                return .system(size: 32, weight: .semibold)
            }
        }
        
        static var headline: Font {
            switch DeviceType.current {
            case .compact:
                return .body.weight(.medium)
            case .regular:
                return .headline.weight(.medium)
            case .large:
                return .headline.weight(.medium)
            case .iPad:
                return .system(size: 20, weight: .medium)
            }
        }
        
        static var body: Font {
            switch DeviceType.current {
            case .compact:
                return .callout
            case .regular:
                return .body
            case .large:
                return .body
            case .iPad:
                return .system(size: 18)
            }
        }
        
        static var caption: Font {
            switch DeviceType.current {
            case .compact:
                return .caption2
            case .regular:
                return .caption
            case .large:
                return .caption
            case .iPad:
                return .system(size: 14)
            }
        }
    }
    
    // MARK: - 响应式尺寸
    
    struct Size {
        static var buttonHeight: CGFloat {
            switch DeviceType.current {
            case .compact:
                return 40
            case .regular:
                return 44
            case .large:
                return 48
            case .iPad:
                return 52
            }
        }
        
        static var iconSize: CGFloat {
            switch DeviceType.current {
            case .compact:
                return 20
            case .regular:
                return 24
            case .large:
                return 28
            case .iPad:
                return 32
            }
        }
        
        static var avatarSize: CGFloat {
            switch DeviceType.current {
            case .compact:
                return 40
            case .regular:
                return 50
            case .large:
                return 60
            case .iPad:
                return 80
            }
        }
        
        static var cardMinHeight: CGFloat {
            switch DeviceType.current {
            case .compact:
                return 60
            case .regular:
                return 80
            case .large:
                return 90
            case .iPad:
                return 100
            }
        }
        
        static var listRowHeight: CGFloat {
            switch DeviceType.current {
            case .compact:
                return 50
            case .regular:
                return 60
            case .large:
                return 70
            case .iPad:
                return 80
            }
        }
    }
    
    // MARK: - 网格布局
    
    struct Grid {
        static var columns: Int {
            switch DeviceType.current {
            case .compact:
                return 2
            case .regular:
                return 2
            case .large:
                return 3
            case .iPad:
                return 4
            }
        }
        
        static var calendarColumns: Int {
            return 7 // 日历始终7列
        }
        
        static var statisticsColumns: Int {
            switch DeviceType.current {
            case .compact:
                return 1
            case .regular:
                return 2
            case .large:
                return 2
            case .iPad:
                return 3
            }
        }
        
        static func gridItems(count: Int? = nil) -> [GridItem] {
            let columnCount = count ?? columns
            return Array(repeating: GridItem(.flexible(), spacing: Spacing.horizontal), count: columnCount)
        }
    }
}

// MARK: - 响应式视图修饰符

extension View {
    
    // MARK: - 响应式间距
    
    func responsivePadding() -> some View {
        self.padding(.horizontal, ResponsiveLayout.Spacing.horizontal)
            .padding(.vertical, ResponsiveLayout.Spacing.vertical)
    }
    
    func responsiveCardPadding() -> some View {
        self.padding(ResponsiveLayout.Spacing.cardPadding)
    }
    
    func responsiveSectionSpacing() -> some View {
        self.padding(.bottom, ResponsiveLayout.Spacing.sectionSpacing)
    }
    
    // MARK: - 响应式字体
    
    func responsiveLargeTitle() -> some View {
        self.font(ResponsiveLayout.Typography.largeTitle)
    }
    
    func responsiveTitle() -> some View {
        self.font(ResponsiveLayout.Typography.title)
    }
    
    func responsiveHeadline() -> some View {
        self.font(ResponsiveLayout.Typography.headline)
    }
    
    func responsiveBody() -> some View {
        self.font(ResponsiveLayout.Typography.body)
    }
    
    func responsiveCaption() -> some View {
        self.font(ResponsiveLayout.Typography.caption)
    }
    
    // MARK: - 响应式尺寸
    
    func responsiveButtonHeight() -> some View {
        self.frame(height: ResponsiveLayout.Size.buttonHeight)
    }
    
    func responsiveIconSize() -> some View {
        self.frame(width: ResponsiveLayout.Size.iconSize, height: ResponsiveLayout.Size.iconSize)
    }
    
    func responsiveAvatarSize() -> some View {
        self.frame(width: ResponsiveLayout.Size.avatarSize, height: ResponsiveLayout.Size.avatarSize)
    }
    
    func responsiveCardMinHeight() -> some View {
        self.frame(minHeight: ResponsiveLayout.Size.cardMinHeight)
    }
    
    // MARK: - 条件布局
    
    func compactOnly() -> some View {
        Group {
            if ResponsiveLayout.Screen.isCompact {
                self
            }
        }
    }
    
    func regularAndAbove() -> some View {
        Group {
            if !ResponsiveLayout.Screen.isCompact {
                self
            }
        }
    }
    
    func iPadOnly() -> some View {
        Group {
            if ResponsiveLayout.Screen.isiPad {
                self
            }
        }
    }
    
    func phoneOnly() -> some View {
        Group {
            if !ResponsiveLayout.Screen.isiPad {
                self
            }
        }
    }
    
    // MARK: - 响应式布局
    
    func adaptiveStack<Content: View>(
        @ViewBuilder content: @escaping () -> Content
    ) -> some View {
        Group {
            if ResponsiveLayout.Screen.isCompact {
                VStack(spacing: ResponsiveLayout.Spacing.vertical) {
                    content()
                }
            } else {
                HStack(spacing: ResponsiveLayout.Spacing.horizontal) {
                    content()
                }
            }
        }
    }
    
    func adaptiveGrid<Content: View>(
        columns: Int? = nil,
        spacing: CGFloat? = nil,
        @ViewBuilder content: @escaping () -> Content
    ) -> some View {
        LazyVGrid(
            columns: ResponsiveLayout.Grid.gridItems(count: columns),
            spacing: spacing ?? ResponsiveLayout.Spacing.vertical,
            content: content
        )
    }
}

// MARK: - 响应式容器视图

struct ResponsiveContainer<Content: View>: View {
    let content: Content
    let maxWidth: CGFloat?
    
    init(maxWidth: CGFloat? = nil, @ViewBuilder content: () -> Content) {
        self.maxWidth = maxWidth
        self.content = content()
    }
    
    var body: some View {
        content
            .frame(maxWidth: maxWidth ?? (ResponsiveLayout.Screen.isiPad ? 600 : .infinity))
            .responsivePadding()
    }
}

struct AdaptiveSheet<Content: View>: View {
    @Binding var isPresented: Bool
    let content: Content
    
    init(isPresented: Binding<Bool>, @ViewBuilder content: () -> Content) {
        self._isPresented = isPresented
        self.content = content()
    }
    
    var body: some View {
        Group {
            if ResponsiveLayout.Screen.isiPad {
                content
                    .frame(width: 500, height: 600)
                    .background(Color(.systemBackground))
                    .cornerRadius(16)
                    .shadow(radius: 20)
            } else {
                content
            }
        }
    }
}
