//
//  SalaryCalculator.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation

/// 薪资计算工具类
struct SalaryCalculator {
    
    // MARK: - 基础计算
    
    /// 计算工作时长（小时）
    static func calculateWorkHours(startTime: Date, endTime: Date) -> Double {
        let timeInterval = endTime.timeIntervalSince(startTime)
        return max(0, timeInterval / 3600) // 确保不为负数
    }
    
    /// 根据薪资类型计算实际薪资
    static func calculateActualSalary(
        salaryType: SalaryType,
        baseSalary: Double,
        workHours: Double
    ) -> Double {
        switch salaryType {
        case .hourly:
            return baseSalary * workHours
        case .daily, .project:
            return baseSalary
        }
    }
    
    /// 计算单条工作记录的薪资
    static func calculateSalary(for workRecord: WorkRecord) -> Double {
        return calculateActualSalary(
            salaryType: workRecord.salaryType,
            baseSalary: workRecord.salary,
            workHours: workRecord.workHours
        )
    }
    
    // MARK: - 统计计算
    
    /// 计算工作记录列表的总收入
    static func calculateTotalIncome(from workRecords: [WorkRecord]) -> Double {
        return workRecords.reduce(0) { total, record in
            total + calculateSalary(for: record)
        }
    }
    
    /// 计算工作记录列表的总工时
    static func calculateTotalWorkHours(from workRecords: [WorkRecord]) -> Double {
        return workRecords.reduce(0) { total, record in
            total + record.workHours
        }
    }
    
    /// 计算平均时薪
    static func calculateAverageHourlyRate(from workRecords: [WorkRecord]) -> Double {
        let totalIncome = calculateTotalIncome(from: workRecords)
        let totalHours = calculateTotalWorkHours(from: workRecords)
        
        guard totalHours > 0 else { return 0 }
        return totalIncome / totalHours
    }
    
    /// 计算平均日收入
    static func calculateAverageDailyIncome(from workRecords: [WorkRecord]) -> Double {
        guard !workRecords.isEmpty else { return 0 }
        
        // 按日期分组计算每日收入
        let dailyIncomes = Dictionary(grouping: workRecords, by: { record in
            DateHelper.startOfDay(for: record.date)
        }).mapValues { records in
            calculateTotalIncome(from: records)
        }
        
        let totalDailyIncome = dailyIncomes.values.reduce(0, +)
        return totalDailyIncome / Double(dailyIncomes.count)
    }
    
    // MARK: - 工作类型统计
    
    /// 按工作类型统计收入
    static func calculateIncomeByWorkType(
        from workRecords: [WorkRecord],
        workTypes: [WorkType]
    ) -> [WorkTypeIncome] {
        let workTypeDict = Dictionary(uniqueKeysWithValues: workTypes.map { ($0.id, $0) })
        
        let incomeByType = Dictionary(grouping: workRecords, by: { $0.workTypeId })
            .mapValues { records in
                calculateTotalIncome(from: records)
            }
        
        return incomeByType.compactMap { (workTypeId, income) in
            guard let workType = workTypeDict[workTypeId] else { return nil }
            return WorkTypeIncome(
                workType: workType,
                totalIncome: income,
                recordCount: workRecords.filter { $0.workTypeId == workTypeId }.count
            )
        }.sorted { $0.totalIncome > $1.totalIncome }
    }
    
    /// 按工作类型统计工时
    static func calculateWorkHoursByWorkType(
        from workRecords: [WorkRecord],
        workTypes: [WorkType]
    ) -> [WorkTypeHours] {
        let workTypeDict = Dictionary(uniqueKeysWithValues: workTypes.map { ($0.id, $0) })
        
        let hoursByType = Dictionary(grouping: workRecords, by: { $0.workTypeId })
            .mapValues { records in
                calculateTotalWorkHours(from: records)
            }
        
        return hoursByType.compactMap { (workTypeId, hours) in
            guard let workType = workTypeDict[workTypeId] else { return nil }
            return WorkTypeHours(
                workType: workType,
                totalHours: hours,
                recordCount: workRecords.filter { $0.workTypeId == workTypeId }.count
            )
        }.sorted { $0.totalHours > $1.totalHours }
    }
    
    // MARK: - 时间段统计
    
    /// 按周统计收入
    static func calculateWeeklyIncome(from workRecords: [WorkRecord]) -> [WeeklyIncome] {
        let weeklyGroups = Dictionary(grouping: workRecords) { record in
            DateHelper.startOfWeek(for: record.date)
        }
        
        return weeklyGroups.map { (weekStart, records) in
            WeeklyIncome(
                weekStart: weekStart,
                totalIncome: calculateTotalIncome(from: records),
                totalHours: calculateTotalWorkHours(from: records),
                recordCount: records.count
            )
        }.sorted { $0.weekStart > $1.weekStart }
    }
    
    /// 按月统计收入
    static func calculateMonthlyIncome(from workRecords: [WorkRecord]) -> [MonthlyIncome] {
        let monthlyGroups = Dictionary(grouping: workRecords) { record in
            DateHelper.startOfMonth(for: record.date)
        }
        
        return monthlyGroups.map { (monthStart, records) in
            MonthlyIncome(
                monthStart: monthStart,
                totalIncome: calculateTotalIncome(from: records),
                totalHours: calculateTotalWorkHours(from: records),
                recordCount: records.count
            )
        }.sorted { $0.monthStart > $1.monthStart }
    }
}

// MARK: - 统计数据结构

/// 工作类型收入统计
struct WorkTypeIncome {
    let workType: WorkType
    let totalIncome: Double
    let recordCount: Int
    
    var formattedIncome: String {
        return String(format: "¥%.2f", totalIncome)
    }
}

/// 工作类型工时统计
struct WorkTypeHours {
    let workType: WorkType
    let totalHours: Double
    let recordCount: Int
    
    var formattedHours: String {
        return String(format: "%.1f小时", totalHours)
    }
}

/// 周收入统计
struct WeeklyIncome {
    let weekStart: Date
    let totalIncome: Double
    let totalHours: Double
    let recordCount: Int
    
    var formattedIncome: String {
        return String(format: "¥%.2f", totalIncome)
    }
    
    var formattedHours: String {
        return String(format: "%.1f小时", totalHours)
    }
    
    var weekRange: String {
        let weekEnd = Calendar.current.date(byAdding: .day, value: 6, to: weekStart) ?? weekStart
        return "\(DateHelper.monthDayFormatter.string(from: weekStart)) - \(DateHelper.monthDayFormatter.string(from: weekEnd))"
    }
}

/// 月收入统计
struct MonthlyIncome {
    let monthStart: Date
    let totalIncome: Double
    let totalHours: Double
    let recordCount: Int
    
    var formattedIncome: String {
        return String(format: "¥%.2f", totalIncome)
    }
    
    var formattedHours: String {
        return String(format: "%.1f小时", totalHours)
    }
    
    var monthName: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月"
        return formatter.string(from: monthStart)
    }
}

// MARK: - 高级统计计算
extension SalaryCalculator {
    /// 计算收入增长率
    static func calculateIncomeGrowthRate(
        currentPeriodRecords: [WorkRecord],
        previousPeriodRecords: [WorkRecord]
    ) -> Double {
        let currentIncome = calculateTotalIncome(from: currentPeriodRecords)
        let previousIncome = calculateTotalIncome(from: previousPeriodRecords)

        guard previousIncome > 0 else { return 0 }
        return ((currentIncome - previousIncome) / previousIncome) * 100
    }

    /// 计算工作效率趋势
    static func calculateEfficiencyTrend(from workRecords: [WorkRecord]) -> EfficiencyTrend {
        let sortedRecords = workRecords.sorted { $0.date < $1.date }
        guard sortedRecords.count >= 2 else {
            return EfficiencyTrend(trend: .stable, changeRate: 0, description: "数据不足")
        }

        let midPoint = sortedRecords.count / 2
        let firstHalf = Array(sortedRecords[0..<midPoint])
        let secondHalf = Array(sortedRecords[midPoint...])

        let firstHalfRate = calculateAverageHourlyRate(from: firstHalf)
        let secondHalfRate = calculateAverageHourlyRate(from: secondHalf)

        guard firstHalfRate > 0 else {
            return EfficiencyTrend(trend: .stable, changeRate: 0, description: "基准数据不足")
        }

        let changeRate = ((secondHalfRate - firstHalfRate) / firstHalfRate) * 100

        let trend: TrendDirection
        let description: String

        if changeRate > 10 {
            trend = .increasing
            description = "效率显著提升"
        } else if changeRate > 5 {
            trend = .increasing
            description = "效率稳步提升"
        } else if changeRate < -10 {
            trend = .decreasing
            description = "效率明显下降"
        } else if changeRate < -5 {
            trend = .decreasing
            description = "效率有所下降"
        } else {
            trend = .stable
            description = "效率保持稳定"
        }

        return EfficiencyTrend(trend: trend, changeRate: changeRate, description: description)
    }

    /// 计算最佳工作时段
    static func calculateOptimalWorkingHours(from workRecords: [WorkRecord]) -> OptimalWorkingHours {
        let hourlyData = Dictionary(grouping: workRecords) { record in
            Calendar.current.component(.hour, from: record.startTime)
        }

        var hourlyStats: [Int: (totalIncome: Double, totalHours: Double, count: Int)] = [:]

        for (hour, records) in hourlyData {
            let totalIncome = calculateTotalIncome(from: records)
            let totalHours = calculateTotalWorkHours(from: records)
            hourlyStats[hour] = (totalIncome, totalHours, records.count)
        }

        let bestHour = hourlyStats.max { first, second in
            let firstRate = first.value.totalHours > 0 ? first.value.totalIncome / first.value.totalHours : 0
            let secondRate = second.value.totalHours > 0 ? second.value.totalIncome / second.value.totalHours : 0
            return firstRate < secondRate
        }

        let mostProductiveHour = hourlyStats.max { first, second in
            first.value.count < second.value.count
        }

        return OptimalWorkingHours(
            bestPayingHour: bestHour?.key ?? 9,
            mostProductiveHour: mostProductiveHour?.key ?? 14,
            hourlyStats: hourlyStats
        )
    }

    /// 计算工作类型效率排名
    static func calculateWorkTypeEfficiencyRanking(
        from workRecords: [WorkRecord],
        workTypes: [WorkType]
    ) -> [WorkTypeEfficiency] {
        let workTypeDict = Dictionary(uniqueKeysWithValues: workTypes.map { ($0.id, $0) })

        let efficiencyByType = Dictionary(grouping: workRecords, by: { $0.workTypeId })
            .compactMapValues { records -> WorkTypeEfficiency? in
                guard let workType = workTypeDict[records.first?.workTypeId ?? UUID()] else { return nil }

                let totalIncome = calculateTotalIncome(from: records)
                let totalHours = calculateTotalWorkHours(from: records)
                let averageHourlyRate = totalHours > 0 ? totalIncome / totalHours : 0
                let recordCount = records.count

                return WorkTypeEfficiency(
                    workType: workType,
                    averageHourlyRate: averageHourlyRate,
                    totalIncome: totalIncome,
                    totalHours: totalHours,
                    recordCount: recordCount
                )
            }

        return Array(efficiencyByType.values).sorted { $0.averageHourlyRate > $1.averageHourlyRate }
    }

    /// 预测未来收入
    static func predictFutureIncome(
        from workRecords: [WorkRecord],
        daysToPredict: Int = 30
    ) -> IncomeProjection {
        guard !workRecords.isEmpty else {
            return IncomeProjection(projectedIncome: 0, confidence: 0, description: "无历史数据")
        }

        let recentRecords = workRecords.filter { record in
            let daysAgo = Date().timeIntervalSince(record.date) / 86400
            return daysAgo <= 30 // 最近30天的数据
        }

        guard !recentRecords.isEmpty else {
            return IncomeProjection(projectedIncome: 0, confidence: 0, description: "缺少近期数据")
        }

        let dailyAverageIncome = calculateAverageDailyIncome(from: recentRecords)
        let projectedIncome = dailyAverageIncome * Double(daysToPredict)

        // 计算置信度（基于数据量和一致性）
        let dataPoints = recentRecords.count
        let confidence = min(Double(dataPoints) / 20.0 * 100, 100) // 20个数据点为100%置信度

        let description: String
        if confidence >= 80 {
            description = "预测可靠性高"
        } else if confidence >= 60 {
            description = "预测可靠性中等"
        } else {
            description = "预测可靠性较低"
        }

        return IncomeProjection(
            projectedIncome: projectedIncome,
            confidence: confidence,
            description: description
        )
    }
}

// MARK: - 高级统计数据结构

/// 效率趋势
struct EfficiencyTrend {
    let trend: TrendDirection
    let changeRate: Double
    let description: String

    var formattedChangeRate: String {
        return String(format: "%.1f%%", abs(changeRate))
    }
}

/// 趋势方向
enum TrendDirection {
    case increasing
    case decreasing
    case stable

    var displayName: String {
        switch self {
        case .increasing:
            return "上升"
        case .decreasing:
            return "下降"
        case .stable:
            return "稳定"
        }
    }

    var color: String {
        switch self {
        case .increasing:
            return "#34C759" // 绿色
        case .decreasing:
            return "#FF3B30" // 红色
        case .stable:
            return "#007AFF" // 蓝色
        }
    }
}

/// 最佳工作时段
struct OptimalWorkingHours {
    let bestPayingHour: Int
    let mostProductiveHour: Int
    let hourlyStats: [Int: (totalIncome: Double, totalHours: Double, count: Int)]

    var bestPayingTimeDescription: String {
        return "\(bestPayingHour):00"
    }

    var mostProductiveTimeDescription: String {
        return "\(mostProductiveHour):00"
    }
}

/// 工作类型效率
struct WorkTypeEfficiency {
    let workType: WorkType
    let averageHourlyRate: Double
    let totalIncome: Double
    let totalHours: Double
    let recordCount: Int

    var formattedHourlyRate: String {
        return String(format: "¥%.2f/小时", averageHourlyRate)
    }

    var formattedTotalIncome: String {
        return String(format: "¥%.2f", totalIncome)
    }

    var efficiencyRating: EfficiencyRating {
        switch averageHourlyRate {
        case 0..<20:
            return .low
        case 20..<50:
            return .medium
        case 50..<100:
            return .high
        default:
            return .excellent
        }
    }
}

/// 收入预测
struct IncomeProjection {
    let projectedIncome: Double
    let confidence: Double
    let description: String

    var formattedProjectedIncome: String {
        return String(format: "¥%.2f", projectedIncome)
    }

    var formattedConfidence: String {
        return String(format: "%.0f%%", confidence)
    }
}
