//
//  PerformanceMonitor.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation
import SwiftUI
import os.log

/// 性能监控器
@MainActor
class PerformanceMonitor: ObservableObject {
    static let shared = PerformanceMonitor()
    
    private let logger = Logger(subsystem: "com.parttimejob.app", category: "Performance")
    
    @Published var isMonitoring = false
    @Published var performanceMetrics: PerformanceMetrics = PerformanceMetrics()
    
    private var startTime: CFAbsoluteTime = 0
    private var memoryUsageTimer: Timer?
    private var frameRateTimer: Timer?
    
    private init() {}
    
    // MARK: - 监控控制
    
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        startTime = CFAbsoluteGetTime()
        
        // 开始内存监控
        startMemoryMonitoring()
        
        // 开始帧率监控
        startFrameRateMonitoring()
        
        logger.info("Performance monitoring started")
    }
    
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        
        // 停止定时器
        memoryUsageTimer?.invalidate()
        frameRateTimer?.invalidate()
        
        logger.info("Performance monitoring stopped")
    }
    
    // MARK: - 内存监控
    
    private func startMemoryMonitoring() {
        memoryUsageTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateMemoryUsage()
            }
        }
    }
    
    private func updateMemoryUsage() {
        let memoryInfo = getMemoryUsage()
        performanceMetrics.currentMemoryUsage = memoryInfo.used
        performanceMetrics.maxMemoryUsage = max(performanceMetrics.maxMemoryUsage, memoryInfo.used)
        performanceMetrics.availableMemory = memoryInfo.available
        
        // 检查内存警告
        if memoryInfo.used > memoryInfo.total * 0.8 {
            logger.warning("High memory usage detected: \(memoryInfo.used)MB")
            performanceMetrics.memoryWarnings += 1
        }
    }
    
    private func getMemoryUsage() -> (used: Double, available: Double, total: Double) {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let usedMB = Double(info.resident_size) / 1024 / 1024
            let totalMB = Double(ProcessInfo.processInfo.physicalMemory) / 1024 / 1024
            let availableMB = totalMB - usedMB
            
            return (usedMB, availableMB, totalMB)
        } else {
            return (0, 0, 0)
        }
    }
    
    // MARK: - 帧率监控
    
    private func startFrameRateMonitoring() {
        frameRateTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateFrameRate()
            }
        }
    }
    
    private func updateFrameRate() {
        // 简化的帧率监控实现
        // 在实际应用中，可以使用CADisplayLink来获取更准确的帧率
        let currentTime = CFAbsoluteGetTime()
        let deltaTime = currentTime - startTime
        
        if deltaTime > 0 {
            let estimatedFPS = 60.0 // 假设目标帧率为60fps
            performanceMetrics.currentFPS = estimatedFPS
            performanceMetrics.averageFPS = (performanceMetrics.averageFPS + estimatedFPS) / 2
        }
    }
    
    // MARK: - 数据库性能监控
    
    func measureDatabaseOperation<T>(_ operation: () throws -> T, operationType: String) rethrows -> T {
        let startTime = CFAbsoluteGetTime()
        let result = try operation()
        let endTime = CFAbsoluteGetTime()
        let duration = (endTime - startTime) * 1000 // 转换为毫秒
        
        performanceMetrics.databaseOperations.append(
            DatabaseOperation(type: operationType, duration: duration, timestamp: Date())
        )
        
        // 保持最近100个操作记录
        if performanceMetrics.databaseOperations.count > 100 {
            performanceMetrics.databaseOperations.removeFirst()
        }
        
        // 记录慢查询
        if duration > 100 { // 超过100ms的查询
            logger.warning("Slow database operation: \(operationType) took \(duration)ms")
            performanceMetrics.slowQueries += 1
        }
        
        return result
    }
    
    // MARK: - 网络性能监控
    
    func measureNetworkRequest<T>(_ request: () async throws -> T, requestType: String) async rethrows -> T {
        let startTime = CFAbsoluteGetTime()
        let result = try await request()
        let endTime = CFAbsoluteGetTime()
        let duration = (endTime - startTime) * 1000
        
        await MainActor.run {
            performanceMetrics.networkRequests.append(
                NetworkRequest(type: requestType, duration: duration, timestamp: Date())
            )
            
            // 保持最近50个请求记录
            if performanceMetrics.networkRequests.count > 50 {
                performanceMetrics.networkRequests.removeFirst()
            }
            
            // 记录慢请求
            if duration > 3000 { // 超过3秒的请求
                logger.warning("Slow network request: \(requestType) took \(duration)ms")
                performanceMetrics.slowNetworkRequests += 1
            }
        }
        
        return result
    }
    
    // MARK: - 性能报告
    
    func generatePerformanceReport() -> PerformanceReport {
        let uptime = CFAbsoluteGetTime() - startTime
        
        let avgDatabaseDuration = performanceMetrics.databaseOperations.isEmpty ? 0 :
            performanceMetrics.databaseOperations.map { $0.duration }.reduce(0, +) / Double(performanceMetrics.databaseOperations.count)
        
        let avgNetworkDuration = performanceMetrics.networkRequests.isEmpty ? 0 :
            performanceMetrics.networkRequests.map { $0.duration }.reduce(0, +) / Double(performanceMetrics.networkRequests.count)
        
        return PerformanceReport(
            uptime: uptime,
            currentMemoryUsage: performanceMetrics.currentMemoryUsage,
            maxMemoryUsage: performanceMetrics.maxMemoryUsage,
            memoryWarnings: performanceMetrics.memoryWarnings,
            currentFPS: performanceMetrics.currentFPS,
            averageFPS: performanceMetrics.averageFPS,
            totalDatabaseOperations: performanceMetrics.databaseOperations.count,
            averageDatabaseDuration: avgDatabaseDuration,
            slowQueries: performanceMetrics.slowQueries,
            totalNetworkRequests: performanceMetrics.networkRequests.count,
            averageNetworkDuration: avgNetworkDuration,
            slowNetworkRequests: performanceMetrics.slowNetworkRequests
        )
    }
    
    // MARK: - 内存清理
    
    func performMemoryCleanup() {
        // 清理缓存
        CacheService.shared.clearExpiredCache()
        
        // 清理临时文件
        clearTemporaryFiles()
        
        // 强制垃圾回收
        autoreleasepool {
            // 执行一些清理操作
        }
        
        logger.info("Memory cleanup performed")
    }
    
    private func clearTemporaryFiles() {
        let tempDirectory = FileManager.default.temporaryDirectory
        do {
            let tempFiles = try FileManager.default.contentsOfDirectory(at: tempDirectory, includingPropertiesForKeys: nil)
            for file in tempFiles {
                try? FileManager.default.removeItem(at: file)
            }
        } catch {
            logger.error("Failed to clear temporary files: \(error)")
        }
    }
}

// MARK: - 性能指标数据结构

struct PerformanceMetrics {
    var currentMemoryUsage: Double = 0
    var maxMemoryUsage: Double = 0
    var availableMemory: Double = 0
    var memoryWarnings: Int = 0
    
    var currentFPS: Double = 0
    var averageFPS: Double = 0
    
    var databaseOperations: [DatabaseOperation] = []
    var slowQueries: Int = 0
    
    var networkRequests: [NetworkRequest] = []
    var slowNetworkRequests: Int = 0
}

struct DatabaseOperation {
    let type: String
    let duration: Double // 毫秒
    let timestamp: Date
}

struct NetworkRequest {
    let type: String
    let duration: Double // 毫秒
    let timestamp: Date
}

struct PerformanceReport {
    let uptime: Double
    let currentMemoryUsage: Double
    let maxMemoryUsage: Double
    let memoryWarnings: Int
    let currentFPS: Double
    let averageFPS: Double
    let totalDatabaseOperations: Int
    let averageDatabaseDuration: Double
    let slowQueries: Int
    let totalNetworkRequests: Int
    let averageNetworkDuration: Double
    let slowNetworkRequests: Int
    
    var formattedUptime: String {
        let hours = Int(uptime) / 3600
        let minutes = (Int(uptime) % 3600) / 60
        let seconds = Int(uptime) % 60
        return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
    }
    
    var memoryUsageDescription: String {
        return String(format: "%.1f MB (峰值: %.1f MB)", currentMemoryUsage, maxMemoryUsage)
    }
    
    var fpsDescription: String {
        return String(format: "当前: %.1f fps, 平均: %.1f fps", currentFPS, averageFPS)
    }
    
    var databasePerformanceDescription: String {
        return String(format: "总操作: %d, 平均耗时: %.1f ms, 慢查询: %d", 
                     totalDatabaseOperations, averageDatabaseDuration, slowQueries)
    }
    
    var networkPerformanceDescription: String {
        return String(format: "总请求: %d, 平均耗时: %.1f ms, 慢请求: %d", 
                     totalNetworkRequests, averageNetworkDuration, slowNetworkRequests)
    }
}

// MARK: - 性能监控视图

struct PerformanceMonitorView: View {
    @StateObject private var monitor = PerformanceMonitor.shared
    @State private var showingReport = false
    @State private var performanceReport: PerformanceReport?
    
    var body: some View {
        NavigationView {
            List {
                Section("监控状态") {
                    HStack {
                        Text("性能监控")
                        Spacer()
                        Toggle("", isOn: Binding(
                            get: { monitor.isMonitoring },
                            set: { isOn in
                                if isOn {
                                    monitor.startMonitoring()
                                } else {
                                    monitor.stopMonitoring()
                                }
                            }
                        ))
                    }
                }
                
                if monitor.isMonitoring {
                    Section("实时指标") {
                        HStack {
                            Text("内存使用")
                            Spacer()
                            Text(String(format: "%.1f MB", monitor.performanceMetrics.currentMemoryUsage))
                                .foregroundColor(.blue)
                        }
                        
                        HStack {
                            Text("帧率")
                            Spacer()
                            Text(String(format: "%.1f fps", monitor.performanceMetrics.currentFPS))
                                .foregroundColor(.green)
                        }
                        
                        HStack {
                            Text("数据库操作")
                            Spacer()
                            Text("\(monitor.performanceMetrics.databaseOperations.count)")
                                .foregroundColor(.orange)
                        }
                    }
                }
                
                Section("操作") {
                    Button("生成性能报告") {
                        performanceReport = monitor.generatePerformanceReport()
                        showingReport = true
                    }
                    
                    Button("执行内存清理") {
                        monitor.performMemoryCleanup()
                    }
                    .foregroundColor(.red)
                }
            }
            .navigationTitle("性能监控")
            .sheet(isPresented: $showingReport) {
                if let report = performanceReport {
                    PerformanceReportView(report: report)
                }
            }
        }
    }
}

// MARK: - 性能报告视图

struct PerformanceReportView: View {
    let report: PerformanceReport
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                Section("系统信息") {
                    ReportRow(title: "运行时间", value: report.formattedUptime)
                    ReportRow(title: "内存使用", value: report.memoryUsageDescription)
                    ReportRow(title: "内存警告", value: "\(report.memoryWarnings) 次")
                }
                
                Section("渲染性能") {
                    ReportRow(title: "帧率", value: report.fpsDescription)
                }
                
                Section("数据库性能") {
                    ReportRow(title: "操作统计", value: report.databasePerformanceDescription)
                }
                
                Section("网络性能") {
                    ReportRow(title: "请求统计", value: report.networkPerformanceDescription)
                }
            }
            .navigationTitle("性能报告")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct ReportRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.primary)
            Spacer()
            Text(value)
                .foregroundColor(.secondary)
                .font(.caption)
        }
    }
}
