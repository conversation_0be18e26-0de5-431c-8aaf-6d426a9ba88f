//
//  DateHelper.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import Foundation

/// 日期处理工具类
struct DateHelper {
    // MARK: - 私有配置

    /// 统一的日历配置
    private static let calendar: Calendar = {
        var cal = Calendar.current
        cal.timeZone = TimeZone.current
        cal.firstWeekday = 2 // 设置周一为一周开始
        return cal
    }()

    /// 统一的时区配置
    private static let timeZone = TimeZone.current

    // MARK: - 日期格式化

    /// 年月日格式化器
    static let yearMonthDayFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.timeZone = timeZone
        return formatter
    }()

    /// 月日格式化器
    static let monthDayFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日"
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.timeZone = timeZone
        return formatter
    }()

    /// 时分格式化器
    static let hourMinuteFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.timeZone = timeZone
        return formatter
    }()

    /// 星期格式化器
    static let weekdayFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE"
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.timeZone = timeZone
        return formatter
    }()

    /// 简短星期格式化器
    static let shortWeekdayFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "E"
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.timeZone = timeZone
        return formatter
    }()
    
    // MARK: - 日期计算

    /// 获取当前月份的第一天
    static func startOfMonth(for date: Date = Date()) -> Date {
        let components = calendar.dateComponents([.year, .month], from: date)
        return calendar.date(from: components) ?? date
    }

    /// 获取当前月份的最后一天
    static func endOfMonth(for date: Date = Date()) -> Date {
        let startOfMonth = self.startOfMonth(for: date)
        guard let nextMonth = calendar.date(byAdding: .month, value: 1, to: startOfMonth) else {
            return date
        }
        guard let lastDay = calendar.date(byAdding: .day, value: -1, to: nextMonth) else {
            return date
        }
        return lastDay
    }

    /// 获取当前周的第一天（周一）
    static func startOfWeek(for date: Date = Date()) -> Date {
        let components = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: date)
        return calendar.date(from: components) ?? date
    }

    /// 获取当前周的最后一天（周日）
    static func endOfWeek(for date: Date = Date()) -> Date {
        let startOfWeek = self.startOfWeek(for: date)
        guard let endOfWeek = calendar.date(byAdding: .day, value: 6, to: startOfWeek) else {
            return date
        }
        return endOfWeek
    }

    /// 获取当前年的第一天
    static func startOfYear(for date: Date = Date()) -> Date {
        let components = calendar.dateComponents([.year], from: date)
        return calendar.date(from: components) ?? date
    }

    /// 获取当前年的最后一天
    static func endOfYear(for date: Date = Date()) -> Date {
        let startOfYear = self.startOfYear(for: date)
        guard let nextYear = calendar.date(byAdding: .year, value: 1, to: startOfYear) else {
            return date
        }
        guard let lastDay = calendar.date(byAdding: .day, value: -1, to: nextYear) else {
            return date
        }
        return lastDay
    }

    /// 获取一天的开始时间（00:00:00）
    static func startOfDay(for date: Date) -> Date {
        return calendar.startOfDay(for: date)
    }

    /// 获取一天的结束时间（23:59:59）
    static func endOfDay(for date: Date) -> Date {
        let startOfNextDay = calendar.date(byAdding: .day, value: 1, to: startOfDay(for: date)) ?? date
        guard let endOfDay = calendar.date(byAdding: .second, value: -1, to: startOfNextDay) else {
            return date
        }
        return endOfDay
    }
    
    // MARK: - 日期比较

    /// 判断两个日期是否是同一天
    static func isSameDay(_ date1: Date, _ date2: Date) -> Bool {
        return calendar.isDate(date1, inSameDayAs: date2)
    }

    /// 判断日期是否是今天
    static func isToday(_ date: Date) -> Bool {
        return isSameDay(date, Date())
    }

    /// 判断日期是否是昨天
    static func isYesterday(_ date: Date) -> Bool {
        guard let yesterday = calendar.date(byAdding: .day, value: -1, to: Date()) else {
            return false
        }
        return isSameDay(date, yesterday)
    }

    /// 判断日期是否是明天
    static func isTomorrow(_ date: Date) -> Bool {
        guard let tomorrow = calendar.date(byAdding: .day, value: 1, to: Date()) else {
            return false
        }
        return isSameDay(date, tomorrow)
    }

    /// 判断日期是否在本周
    static func isThisWeek(_ date: Date) -> Bool {
        return calendar.isDate(date, equalTo: Date(), toGranularity: .weekOfYear)
    }

    /// 判断日期是否在本月
    static func isThisMonth(_ date: Date) -> Bool {
        return calendar.isDate(date, equalTo: Date(), toGranularity: .month)
    }

    /// 判断日期是否在本年
    static func isThisYear(_ date: Date) -> Bool {
        return calendar.isDate(date, equalTo: Date(), toGranularity: .year)
    }
    
    // MARK: - 日期生成

    /// 生成指定月份的所有日期
    static func datesInMonth(for date: Date) -> [Date] {
        let startOfMonth = self.startOfMonth(for: date)
        let endOfMonth = self.endOfMonth(for: date)

        var dates: [Date] = []
        var currentDate = startOfMonth

        while currentDate <= endOfMonth {
            dates.append(currentDate)
            guard let nextDate = calendar.date(byAdding: .day, value: 1, to: currentDate) else {
                break // 防止无限循环
            }
            currentDate = nextDate
        }

        return dates
    }
    
    /// 生成日历网格所需的日期（包含上月末和下月初的日期）
    static func calendarDates(for date: Date) -> [Date] {
        let startOfMonth = self.startOfMonth(for: date)
        let endOfMonth = self.endOfMonth(for: date)

        // 获取月初是星期几，使用统一的日历配置（周一开始）
        let weekdayOfStart = calendar.component(.weekday, from: startOfMonth)
        let daysFromPreviousMonth = (weekdayOfStart - calendar.firstWeekday + 7) % 7

        // 获取月末是星期几
        let weekdayOfEnd = calendar.component(.weekday, from: endOfMonth)
        let daysFromNextMonth = (calendar.firstWeekday + 6 - weekdayOfEnd + 7) % 7

        var dates: [Date] = []

        // 添加上月的日期
        for i in (1...daysFromPreviousMonth).reversed() {
            if let previousDate = calendar.date(byAdding: .day, value: -i, to: startOfMonth) {
                dates.append(previousDate)
            }
        }

        // 添加当月的日期
        dates.append(contentsOf: datesInMonth(for: date))

        // 添加下月的日期
        for i in 1...daysFromNextMonth {
            if let nextDate = calendar.date(byAdding: .day, value: i, to: endOfMonth) {
                dates.append(nextDate)
            }
        }

        return dates
    }

    // MARK: - 智能日期处理

    /// 获取相对日期描述
    static func relativeDescription(for date: Date) -> String {
        let calendar = Calendar.current
        let now = Date()

        if isToday(date) {
            return "今天"
        } else if isYesterday(date) {
            return "昨天"
        } else if isTomorrow(date) {
            return "明天"
        } else if isThisWeek(date) {
            return shortWeekdayFormatter.string(from: date)
        } else if isThisYear(date) {
            return monthDayFormatter.string(from: date)
        } else {
            return yearMonthDayFormatter.string(from: date)
        }
    }

    /// 获取时间段描述
    static func timeRangeDescription(startTime: Date, endTime: Date) -> String {
        // 验证时间范围的有效性
        guard endTime >= startTime else {
            return "时间范围无效"
        }

        let duration = endTime.timeIntervalSince(startTime)
        let hours = Int(duration / 3600)
        let minutes = Int((duration.truncatingRemainder(dividingBy: 3600)) / 60)

        let startStr = hourMinuteFormatter.string(from: startTime)
        let endStr = hourMinuteFormatter.string(from: endTime)

        if hours > 0 && minutes > 0 {
            return "\(startStr) - \(endStr) (\(hours)小时\(minutes)分钟)"
        } else if hours > 0 {
            return "\(startStr) - \(endStr) (\(hours)小时)"
        } else if minutes > 0 {
            return "\(startStr) - \(endStr) (\(minutes)分钟)"
        } else {
            return "\(startStr) - \(endStr) (少于1分钟)"
        }
    }

    /// 获取工作时长描述
    static func workDurationDescription(_ duration: TimeInterval) -> String {
        // 处理负数时长
        guard duration >= 0 else {
            return "无效时长"
        }

        let hours = Int(duration / 3600)
        let minutes = Int((duration.truncatingRemainder(dividingBy: 3600)) / 60)

        if hours > 0 && minutes > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else if hours > 0 {
            return "\(hours)小时"
        } else if minutes > 0 {
            return "\(minutes)分钟"
        } else {
            return "少于1分钟"
        }
    }

    /// 获取距离现在的时间描述
    static func timeAgoDescription(from date: Date) -> String {
        let now = Date()
        let timeInterval = now.timeIntervalSince(date)

        if timeInterval < 60 {
            return "刚刚"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes)分钟前"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours)小时前"
        } else if timeInterval < 604800 {
            let days = Int(timeInterval / 86400)
            return "\(days)天前"
        } else {
            return yearMonthDayFormatter.string(from: date)
        }
    }

    // MARK: - 工作日计算

    /// 判断是否为工作日（周一到周五）
    static func isWorkday(_ date: Date) -> Bool {
        let calendar = Calendar.current
        let weekday = calendar.component(.weekday, from: date)
        return weekday >= 2 && weekday <= 6 // 周一到周五
    }

    /// 判断是否为周末
    static func isWeekend(_ date: Date) -> Bool {
        return !isWorkday(date)
    }

    /// 获取指定日期范围内的工作日数量
    static func workdaysCount(from startDate: Date, to endDate: Date) -> Int {
        // 验证日期范围
        guard endDate >= startDate else {
            return 0
        }

        var count = 0
        var currentDate = startDate

        while currentDate <= endDate {
            if isWorkday(currentDate) {
                count += 1
            }
            guard let nextDate = calendar.date(byAdding: .day, value: 1, to: currentDate) else {
                break
            }
            currentDate = nextDate
        }

        return count
    }

    /// 获取下一个工作日
    static func nextWorkday(from date: Date = Date()) -> Date {
        guard var nextDate = calendar.date(byAdding: .day, value: 1, to: date) else {
            return date
        }

        // 防止无限循环，最多查找14天
        var attempts = 0
        while !isWorkday(nextDate) && attempts < 14 {
            guard let newDate = calendar.date(byAdding: .day, value: 1, to: nextDate) else {
                break
            }
            nextDate = newDate
            attempts += 1
        }

        return nextDate
    }

    // MARK: - 时间范围生成

    /// 生成时间选项（用于时间选择器）
    static func generateTimeOptions(interval: Int = 30) -> [Date] {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: Date())
        var times: [Date] = []

        for hour in 0 ..< 24 {
            for minute in stride(from: 0, to: 60, by: interval) {
                if let time = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: startOfDay) {
                    times.append(time)
                }
            }
        }

        return times
    }

    /// 获取常用时间段
    static func getCommonTimeSlots() -> [(name: String, start: Date, end: Date)] {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        let timeSlots: [(String, Int, Int, Int, Int)] = [
            ("上午", 9, 0, 12, 0),
            ("下午", 14, 0, 17, 0),
            ("晚上", 19, 0, 22, 0),
            ("全天", 9, 0, 17, 0)
        ]

        return timeSlots.compactMap { name, startHour, startMinute, endHour, endMinute in
            guard let startTime = calendar.date(bySettingHour: startHour, minute: startMinute, second: 0, of: today),
                  let endTime = calendar.date(bySettingHour: endHour, minute: endMinute, second: 0, of: today)
            else {
                return nil
            }
            return (name, startTime, endTime)
        }
    }
}
