//
//  CalendarHapticFeedback.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import UIKit

/// 日历触觉反馈管理器
struct CalendarHapticFeedback {
    
    // MARK: - 触觉反馈类型
    
    /// 轻微触觉反馈 - 用于日期选择、月份切换
    static func lightImpact() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    /// 中等触觉反馈 - 用于工作记录点击、重要操作
    static func mediumImpact() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    /// 强烈触觉反馈 - 用于长按操作、删除确认
    static func heavyImpact() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
    }
    
    /// 成功反馈 - 用于保存成功、操作完成
    static func successAction() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }
    
    /// 警告反馈 - 用于验证失败、冲突提醒
    static func warningAction() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.warning)
    }
    
    /// 错误反馈 - 用于操作失败、严重错误
    static func errorAction() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.error)
    }
    
    // MARK: - 选择反馈
    
    /// 选择反馈 - 用于选择器变化
    static func selectionChanged() {
        let selectionFeedback = UISelectionFeedbackGenerator()
        selectionFeedback.selectionChanged()
    }
    
    // MARK: - 预准备反馈
    
    /// 预准备触觉反馈 - 在可能触发反馈前调用以减少延迟
    static func prepareImpact() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.prepare()
    }
    
    /// 预准备通知反馈
    static func prepareNotification() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.prepare()
    }
    
    /// 预准备选择反馈
    static func prepareSelection() {
        let selectionFeedback = UISelectionFeedbackGenerator()
        selectionFeedback.prepare()
    }
    
    // MARK: - 组合反馈
    
    /// 日历导航反馈 - 月份切换时的反馈
    static func calendarNavigation() {
        lightImpact()
    }
    
    /// 工作记录操作反馈 - 添加、编辑工作记录时的反馈
    static func workRecordAction() {
        mediumImpact()
    }
    
    /// 长按菜单反馈 - 长按显示菜单时的反馈
    static func longPressMenu() {
        heavyImpact()
    }
    
    /// 数据保存反馈 - 数据保存成功时的反馈
    static func dataSaved() {
        successAction()
    }
    
    /// 验证失败反馈 - 表单验证失败时的反馈
    static func validationFailed() {
        warningAction()
    }
    
    /// 删除操作反馈 - 删除操作时的反馈
    static func deleteAction() {
        heavyImpact()
        
        // 延迟一点再给成功反馈
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            successAction()
        }
    }
    
    /// 时间冲突反馈 - 检测到时间冲突时的反馈
    static func timeConflict() {
        errorAction()
    }
    
    /// 快速添加反馈 - 快速添加工作记录时的反馈
    static func quickAdd() {
        mediumImpact()
        
        // 延迟一点再给成功反馈
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            successAction()
        }
    }
    
    // MARK: - 条件反馈
    
    /// 根据用户设置决定是否触发反馈
    /// - Parameter feedbackType: 反馈类型
    static func conditionalFeedback(_ feedbackType: FeedbackType) {
        // 这里可以检查用户设置是否启用触觉反馈
        // 暂时直接执行反馈
        switch feedbackType {
        case .light:
            lightImpact()
        case .medium:
            mediumImpact()
        case .heavy:
            heavyImpact()
        case .success:
            successAction()
        case .warning:
            warningAction()
        case .error:
            errorAction()
        case .selection:
            selectionChanged()
        }
    }
}

// MARK: - 反馈类型枚举

enum FeedbackType {
    case light
    case medium
    case heavy
    case success
    case warning
    case error
    case selection
}

// MARK: - 触觉反馈扩展

extension CalendarHapticFeedback {
    
    /// 根据工作状态提供不同的反馈
    /// - Parameter workStatus: 工作状态
    static func workStatusFeedback(for workStatus: WorkStatus) {
        switch workStatus {
        case .upcoming:
            lightImpact()
        case .inProgress:
            mediumImpact()
        case .completed:
            successAction()
        }
    }
    
    /// 根据效率评级提供不同的反馈
    /// - Parameter efficiency: 效率评级
    static func efficiencyFeedback(for efficiency: EfficiencyRating) {
        switch efficiency {
        case .low:
            warningAction()
        case .medium:
            lightImpact()
        case .high:
            mediumImpact()
        case .excellent:
            successAction()
        }
    }
    
    /// 批量操作反馈
    /// - Parameters:
    ///   - successCount: 成功数量
    ///   - totalCount: 总数量
    static func batchOperationFeedback(successCount: Int, totalCount: Int) {
        if successCount == totalCount {
            // 全部成功
            successAction()
        } else if successCount > 0 {
            // 部分成功
            warningAction()
        } else {
            // 全部失败
            errorAction()
        }
    }
    
    /// 时间段选择反馈
    /// - Parameter isValidTimeRange: 是否为有效时间段
    static func timeRangeSelectionFeedback(isValidTimeRange: Bool) {
        if isValidTimeRange {
            lightImpact()
        } else {
            warningAction()
        }
    }
}

// MARK: - 触觉反馈设置

extension CalendarHapticFeedback {
    
    /// 检查设备是否支持触觉反馈
    static var isHapticFeedbackSupported: Bool {
        return UIDevice.current.userInterfaceIdiom == .phone
    }
    
    /// 检查是否应该启用触觉反馈（基于用户设置）
    static var isHapticFeedbackEnabled: Bool {
        // 这里可以从UserPreferences读取设置
        // 暂时返回true
        return isHapticFeedbackSupported
    }
    
    /// 安全的触觉反馈执行 - 只在支持且启用时执行
    /// - Parameter action: 反馈动作
    static func safeExecute(_ action: () -> Void) {
        guard isHapticFeedbackEnabled else { return }
        action()
    }
}
