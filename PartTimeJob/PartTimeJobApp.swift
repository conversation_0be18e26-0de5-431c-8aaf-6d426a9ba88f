//
//  PartTimeJobApp.swift
//  PartTimeJob
//
//  Created by K4 on 2025/8/29.
//

import SwiftUI
import SwiftData

@main
struct PartTimeJobApp: App {
    @StateObject private var migrationService = MigrationService()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(DataManager.shared)
                .environmentObject(migrationService)
                .modelContainer(DataManager.shared.container)
                .task {
                    await performInitialSetup()
                }
        }
    }

    /// 执行应用初始化设置
    @MainActor
    private func performInitialSetup() async {
        // 检查并执行数据库迁移
        if migrationService.needsMigration() {
            let result = migrationService.performMigration()
            if result.success {
                print("✅ 数据库迁移成功: \(result.message)")
            } else {
                print("❌ 数据库迁移失败: \(result.message)")
            }
        }

        // 验证数据完整性
        let validationResult = migrationService.validateDataIntegrity()
        if !validationResult.isValid {
            print("⚠️ 数据完整性问题: \(validationResult.message)")

            // 尝试修复数据问题
            let repairResult = migrationService.repairDataIssues()
            print("🔧 数据修复结果: \(repairResult.message)")
        }
    }
}
