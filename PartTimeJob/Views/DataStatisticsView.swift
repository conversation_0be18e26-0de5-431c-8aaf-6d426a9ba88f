//
//  DataStatisticsView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 数据统计视图
struct DataStatisticsView: View {
    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences
    @Environment(\.dismiss) private var dismiss
    
    @State private var dataStats: DataStatistics?
    @State private var isLoading = true
    @State private var selectedTimeRange: TimeRange = .all
    
    var body: some View {
        NavigationView {
            Group {
                if isLoading {
                    LoadingIndicator(message: "正在计算统计数据...")
                } else if let stats = dataStats {
                    statisticsContent(stats)
                } else {
                    ErrorView(
                        title: "加载失败",
                        message: "无法加载统计数据",
                        retryAction: {
                            loadStatistics()
                        }
                    )
                }
            }
            .navigationTitle("数据统计")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                loadStatistics()
            }
        }
    }
    
    private func statisticsContent(_ stats: DataStatistics) -> some View {
        ScrollView {
            VStack(spacing: 20) {
                // 时间范围选择器
                timeRangeSelector
                
                // 总体统计
                overallStatsSection(stats)
                
                // 数据分布
                dataDistributionSection(stats)
                
                // 存储统计
                storageStatsSection(stats)
                
                // 使用模式
                usagePatternSection(stats)
                
                // 数据质量
                dataQualitySection(stats)
            }
            .padding()
        }
    }
    
    // MARK: - 时间范围选择器
    
    private var timeRangeSelector: some View {
        Picker("时间范围", selection: $selectedTimeRange) {
            ForEach(TimeRange.allCases, id: \.self) { range in
                Text(range.displayName).tag(range)
            }
        }
        .pickerStyle(SegmentedPickerStyle())
        .onChange(of: selectedTimeRange) { _ in
            loadStatistics()
        }
    }
    
    // MARK: - 总体统计部分
    
    private func overallStatsSection(_ stats: DataStatistics) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("总体统计")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                StatCard(
                    title: "工作记录",
                    value: "\(stats.totalWorkRecords)",
                    subtitle: "条记录",
                    icon: "doc.text.fill",
                    color: .blue
                )
                
                StatCard(
                    title: "工作类型",
                    value: "\(stats.totalWorkTypes)",
                    subtitle: "种类型",
                    icon: "folder.fill",
                    color: .orange
                )
                
                StatCard(
                    title: "工作天数",
                    value: "\(stats.totalWorkDays)",
                    subtitle: "天",
                    icon: "calendar.fill",
                    color: .green
                )
                
                StatCard(
                    title: "总收入",
                    value: userPreferences.currencySymbol + String(format: "%.0f", stats.totalIncome),
                    subtitle: "收入",
                    icon: "banknote.fill",
                    color: .purple
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 数据分布部分
    
    private func dataDistributionSection(_ stats: DataStatistics) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("数据分布")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                // 按月分布
                DistributionRow(
                    title: "最活跃月份",
                    value: stats.mostActiveMonth,
                    count: stats.mostActiveMonthCount,
                    icon: "calendar.badge.clock"
                )
                
                // 按工作类型分布
                DistributionRow(
                    title: "最常用工作类型",
                    value: stats.mostUsedWorkType,
                    count: stats.mostUsedWorkTypeCount,
                    icon: "star.fill"
                )
                
                // 按时长分布
                DistributionRow(
                    title: "平均工作时长",
                    value: String(format: "%.1f小时", stats.averageWorkDuration),
                    count: nil,
                    icon: "clock.fill"
                )
                
                // 按收入分布
                DistributionRow(
                    title: "平均时薪",
                    value: userPreferences.currencySymbol + String(format: "%.0f", stats.averageHourlyRate),
                    count: nil,
                    icon: "dollarsign.circle.fill"
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 存储统计部分
    
    private func storageStatsSection(_ stats: DataStatistics) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("存储统计")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                StorageRow(
                    title: "数据库大小",
                    value: formatFileSize(stats.databaseSize),
                    icon: "cylinder.fill",
                    color: .blue
                )
                
                StorageRow(
                    title: "缓存大小",
                    value: formatFileSize(stats.cacheSize),
                    icon: "memorychip.fill",
                    color: .orange
                )
                
                StorageRow(
                    title: "临时文件",
                    value: formatFileSize(stats.tempFileSize),
                    icon: "doc.badge.gearshape.fill",
                    color: .gray
                )
                
                StorageRow(
                    title: "总占用空间",
                    value: formatFileSize(stats.totalStorageUsed),
                    icon: "externaldrive.fill",
                    color: .purple
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 使用模式部分
    
    private func usagePatternSection(_ stats: DataStatistics) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("使用模式")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                PatternRow(
                    title: "首次使用",
                    value: DateHelper.yearMonthDayFormatter.string(from: stats.firstUseDate),
                    icon: "calendar.badge.plus"
                )
                
                PatternRow(
                    title: "最后使用",
                    value: DateHelper.yearMonthDayFormatter.string(from: stats.lastUseDate),
                    icon: "clock.badge"
                )
                
                PatternRow(
                    title: "使用天数",
                    value: "\(stats.totalUsageDays) 天",
                    icon: "chart.line.uptrend.xyaxis"
                )
                
                PatternRow(
                    title: "平均每日记录",
                    value: String(format: "%.1f 条", stats.averageDailyRecords),
                    icon: "chart.bar.fill"
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 数据质量部分
    
    private func dataQualitySection(_ stats: DataStatistics) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("数据质量")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                QualityRow(
                    title: "完整记录",
                    percentage: stats.completeRecordsPercentage,
                    color: .green
                )
                
                QualityRow(
                    title: "有效时间",
                    percentage: stats.validTimePercentage,
                    color: .blue
                )
                
                QualityRow(
                    title: "有描述记录",
                    percentage: stats.recordsWithDescriptionPercentage,
                    color: .orange
                )
                
                QualityRow(
                    title: "数据一致性",
                    percentage: stats.dataConsistencyPercentage,
                    color: .purple
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 辅助方法
    
    private func loadStatistics() {
        isLoading = true
        
        Task {
            let stats = await calculateStatistics()
            
            await MainActor.run {
                dataStats = stats
                isLoading = false
            }
        }
    }
    
    private func calculateStatistics() async -> DataStatistics {
        let dateRange = selectedTimeRange.dateRange
        let allRecords = dataManager.fetchWorkRecords(from: dateRange.start, to: dateRange.end)
        let allWorkTypes = dataManager.fetchWorkTypes()
        
        // 计算各种统计数据
        let totalWorkRecords = allRecords.count
        let totalWorkTypes = allWorkTypes.count
        let uniqueDates = Set(allRecords.map { DateHelper.startOfDay(for: $0.date) })
        let totalWorkDays = uniqueDates.count
        let totalIncome = SalaryCalculator.calculateTotalIncome(from: allRecords)
        
        // 最活跃月份
        let monthCounts = Dictionary(grouping: allRecords) { record in
            DateHelper.startOfMonth(for: record.date)
        }.mapValues { $0.count }
        
        let mostActiveMonth = monthCounts.max(by: { $0.value < $1.value })
        let mostActiveMonthName = mostActiveMonth?.key.formatted(.dateTime.month(.wide).year()) ?? "无"
        let mostActiveMonthCount = mostActiveMonth?.value ?? 0
        
        // 最常用工作类型
        let workTypeCounts = Dictionary(grouping: allRecords) { $0.workTypeId }
            .mapValues { $0.count }
        
        let mostUsedWorkTypeId = workTypeCounts.max(by: { $0.value < $1.value })?.key
        let mostUsedWorkType = allWorkTypes.first { $0.id == mostUsedWorkTypeId }?.name ?? "无"
        let mostUsedWorkTypeCount = workTypeCounts.values.max() ?? 0
        
        // 平均工作时长
        let totalHours = SalaryCalculator.calculateTotalWorkHours(from: allRecords)
        let averageWorkDuration = totalWorkRecords > 0 ? totalHours / Double(totalWorkRecords) : 0
        
        // 平均时薪
        let averageHourlyRate = SalaryCalculator.calculateAverageHourlyRate(from: allRecords)
        
        // 存储统计
        let databaseSize = getDatabaseSize()
        let cacheSize = getCacheSize()
        let tempFileSize = getTempFileSize()
        let totalStorageUsed = databaseSize + cacheSize + tempFileSize
        
        // 使用模式
        let firstUseDate = userPreferences.firstUseDate
        let lastUseDate = allRecords.map { $0.date }.max() ?? Date()
        let totalUsageDays = Calendar.current.dateComponents([.day], from: firstUseDate, to: Date()).day ?? 0
        let averageDailyRecords = totalUsageDays > 0 ? Double(totalWorkRecords) / Double(totalUsageDays) : 0
        
        // 数据质量
        let completeRecords = allRecords.filter { !$0.workDescription.isEmpty && $0.salary > 0 }
        let completeRecordsPercentage = totalWorkRecords > 0 ? Double(completeRecords.count) / Double(totalWorkRecords) * 100 : 0
        
        let validTimeRecords = allRecords.filter { $0.startTime < $0.endTime }
        let validTimePercentage = totalWorkRecords > 0 ? Double(validTimeRecords.count) / Double(totalWorkRecords) * 100 : 0
        
        let recordsWithDescription = allRecords.filter { !$0.workDescription.isEmpty }
        let recordsWithDescriptionPercentage = totalWorkRecords > 0 ? Double(recordsWithDescription.count) / Double(totalWorkRecords) * 100 : 0
        
        let dataConsistencyPercentage = 95.0 // 简化实现
        
        return DataStatistics(
            totalWorkRecords: totalWorkRecords,
            totalWorkTypes: totalWorkTypes,
            totalWorkDays: totalWorkDays,
            totalIncome: totalIncome,
            mostActiveMonth: mostActiveMonthName,
            mostActiveMonthCount: mostActiveMonthCount,
            mostUsedWorkType: mostUsedWorkType,
            mostUsedWorkTypeCount: mostUsedWorkTypeCount,
            averageWorkDuration: averageWorkDuration,
            averageHourlyRate: averageHourlyRate,
            databaseSize: databaseSize,
            cacheSize: cacheSize,
            tempFileSize: tempFileSize,
            totalStorageUsed: totalStorageUsed,
            firstUseDate: firstUseDate,
            lastUseDate: lastUseDate,
            totalUsageDays: totalUsageDays,
            averageDailyRecords: averageDailyRecords,
            completeRecordsPercentage: completeRecordsPercentage,
            validTimePercentage: validTimePercentage,
            recordsWithDescriptionPercentage: recordsWithDescriptionPercentage,
            dataConsistencyPercentage: dataConsistencyPercentage
        )
    }
    
    private func getDatabaseSize() -> Int64 {
        // 简化实现，实际应该获取 SwiftData 数据库文件大小
        return 1024 * 1024 // 1MB
    }
    
    private func getCacheSize() -> Int64 {
        // 获取缓存目录大小
        let cacheDir = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first
        return getDirectorySize(cacheDir)
    }
    
    private func getTempFileSize() -> Int64 {
        // 获取临时文件大小
        let tempDir = FileManager.default.temporaryDirectory
        return getDirectorySize(tempDir)
    }
    
    private func getDirectorySize(_ directory: URL?) -> Int64 {
        guard let directory = directory else { return 0 }
        
        do {
            let contents = try FileManager.default.contentsOfDirectory(at: directory, includingPropertiesForKeys: [.fileSizeKey])
            var totalSize: Int64 = 0
            
            for url in contents {
                let resourceValues = try url.resourceValues(forKeys: [.fileSizeKey])
                totalSize += Int64(resourceValues.fileSize ?? 0)
            }
            
            return totalSize
        } catch {
            return 0
        }
    }
    
    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}

// MARK: - 数据统计结构

struct DataStatistics {
    let totalWorkRecords: Int
    let totalWorkTypes: Int
    let totalWorkDays: Int
    let totalIncome: Double
    let mostActiveMonth: String
    let mostActiveMonthCount: Int
    let mostUsedWorkType: String
    let mostUsedWorkTypeCount: Int
    let averageWorkDuration: Double
    let averageHourlyRate: Double
    let databaseSize: Int64
    let cacheSize: Int64
    let tempFileSize: Int64
    let totalStorageUsed: Int64
    let firstUseDate: Date
    let lastUseDate: Date
    let totalUsageDays: Int
    let averageDailyRecords: Double
    let completeRecordsPercentage: Double
    let validTimePercentage: Double
    let recordsWithDescriptionPercentage: Double
    let dataConsistencyPercentage: Double
}

// MARK: - 统计组件

struct DistributionRow: View {
    let title: String
    let value: String
    let count: Int?
    let icon: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(value)
                    .font(.body)
                    .foregroundColor(.primary)
            }
            
            Spacer()
            
            if let count = count {
                Text("\(count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
}

struct StorageRow: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(title)
                .font(.body)
                .foregroundColor(.primary)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

struct PatternRow: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.green)
                .frame(width: 20)
            
            Text(title)
                .font(.body)
                .foregroundColor(.primary)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

struct QualityRow: View {
    let title: String
    let percentage: Double
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Text(title)
                    .font(.body)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text(String(format: "%.1f%%", percentage))
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(color)
            }
            
            ProgressBar(progress: percentage / 100, color: color)
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    DataStatisticsView()
        .environmentObject(DataManager.shared)
        .environmentObject(UserPreferences.shared)
}
