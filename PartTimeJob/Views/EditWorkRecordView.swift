//
//  EditWorkRecordView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 编辑工作记录视图
struct EditWorkRecordView: View {
    let workRecord: WorkRecord
    
    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 表单状态
    @State private var workDate: Date
    @State private var startTime: Date
    @State private var endTime: Date
    @State private var selectedWorkType: WorkType?
    @State private var workDescription: String
    @State private var salary: Double
    @State private var salaryType: SalaryType
    @State private var notes: String
    
    // MARK: - UI状态
    @State private var showingWorkTypePicker = false
    @State private var showingTimePicker = false
    @State private var showingValidationAlert = false
    @State private var validationMessage = ""
    @State private var isLoading = false
    @State private var activeTimeField: TimeField = .start
    @State private var hasChanges = false
    
    init(workRecord: WorkRecord) {
        self.workRecord = workRecord
        
        // 初始化状态
        _workDate = State(initialValue: workRecord.date)
        _startTime = State(initialValue: workRecord.startTime)
        _endTime = State(initialValue: workRecord.endTime)
        _workDescription = State(initialValue: workRecord.workDescription)
        _salary = State(initialValue: workRecord.salary)
        _salaryType = State(initialValue: workRecord.salaryType)
        _notes = State(initialValue: workRecord.notes)
    }
    
    var body: some View {
        NavigationView {
            Form {
                // 基本信息部分
                basicInfoSection
                
                // 时间设置部分
                timeSettingsSection
                
                // 工作类型部分
                workTypeSection
                
                // 薪资设置部分
                salarySection
                
                // 工作描述部分
                descriptionSection
                
                // 变更预览部分
                if hasChanges {
                    changesPreviewSection
                }
            }
            .navigationTitle("编辑工作记录")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        if hasChanges {
                            // 显示确认对话框
                        } else {
                            dismiss()
                        }
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveChanges()
                    }
                    .disabled(!isFormValid || isLoading || !hasChanges)
                }
            }
            .onAppear {
                setupInitialValues()
                trackChanges()
            }
            .onChange(of: workDate) { _ in checkForChanges() }
            .onChange(of: startTime) { _ in checkForChanges() }
            .onChange(of: endTime) { _ in checkForChanges() }
            .onChange(of: selectedWorkType) { _ in checkForChanges() }
            .onChange(of: workDescription) { _ in checkForChanges() }
            .onChange(of: salary) { _ in checkForChanges() }
            .onChange(of: salaryType) { _ in checkForChanges() }
            .onChange(of: notes) { _ in checkForChanges() }
            .sheet(isPresented: $showingWorkTypePicker) {
                WorkTypePickerView(
                    selectedWorkType: $selectedWorkType,
                    workTypes: dataManager.fetchWorkTypes()
                )
            }
            .sheet(isPresented: $showingTimePicker) {
                TimePickerView(
                    activeField: $activeTimeField,
                    startTime: $startTime,
                    endTime: $endTime,
                    workDate: workDate
                )
            }
            .alert("验证错误", isPresented: $showingValidationAlert) {
                Button("确定", role: .cancel) { }
            } message: {
                Text(validationMessage)
            }
        }
    }
    
    // MARK: - 基本信息部分
    private var basicInfoSection: some View {
        Section("基本信息") {
            DatePicker(
                "工作日期",
                selection: $workDate,
                displayedComponents: [.date]
            )
            .datePickerStyle(CompactDatePickerStyle())
        }
    }
    
    // MARK: - 时间设置部分
    private var timeSettingsSection: some View {
        Section("工作时间") {
            HStack {
                Label("开始时间", systemImage: "clock")
                Spacer()
                Button(DateHelper.hourMinuteFormatter.string(from: startTime)) {
                    activeTimeField = .start
                    showingTimePicker = true
                }
                .foregroundColor(.blue)
            }
            
            HStack {
                Label("结束时间", systemImage: "clock.fill")
                Spacer()
                Button(DateHelper.hourMinuteFormatter.string(from: endTime)) {
                    activeTimeField = .end
                    showingTimePicker = true
                }
                .foregroundColor(.blue)
            }
            
            HStack {
                Label("工作时长", systemImage: "timer")
                Spacer()
                Text(DateHelper.workDurationDescription(calculatedWorkHours * 3600))
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - 工作类型部分
    private var workTypeSection: some View {
        Section("工作类型") {
            Button {
                showingWorkTypePicker = true
            } label: {
                HStack {
                    if let workType = selectedWorkType {
                        Image(systemName: workType.iconName)
                            .foregroundColor(workType.themeColor)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text(workType.name)
                                .foregroundColor(.primary)
                            
                            Text(workType.formattedDefaultSalary)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    } else {
                        Image(systemName: "folder")
                            .foregroundColor(.gray)
                        
                        Text("选择工作类型")
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - 薪资设置部分
    private var salarySection: some View {
        Section("薪资设置") {
            Picker("薪资类型", selection: $salaryType) {
                ForEach(SalaryType.allCases, id: \.self) { type in
                    Text(type.displayName).tag(type)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            
            HStack {
                Text("薪资金额")
                Spacer()
                TextField("0", value: $salary, format: .number)
                    .keyboardType(.decimalPad)
                    .multilineTextAlignment(.trailing)
                    .frame(width: 100)
                Text(userPreferences.currencySymbol)
                    .foregroundColor(.secondary)
            }
            
            if salaryType == .hourly {
                HStack {
                    Text("预计收入")
                    Spacer()
                    Text(userPreferences.currencySymbol + String(format: "%.2f", estimatedSalary))
                        .foregroundColor(.green)
                        .fontWeight(.semibold)
                }
            }
        }
    }
    
    // MARK: - 工作描述部分
    private var descriptionSection: some View {
        Section("工作描述") {
            TextField("请输入工作内容", text: $workDescription, axis: .vertical)
                .lineLimit(3...6)
            
            TextField("备注（可选）", text: $notes, axis: .vertical)
                .lineLimit(2...4)
        }
    }
    
    // MARK: - 变更预览部分
    private var changesPreviewSection: some View {
        Section("变更预览") {
            VStack(alignment: .leading, spacing: 8) {
                Text("以下内容将被更新：")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                ForEach(changes, id: \.field) { change in
                    ChangeRow(change: change)
                }
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var calculatedWorkHours: Double {
        return max(0, endTime.timeIntervalSince(startTime) / 3600)
    }
    
    private var estimatedSalary: Double {
        return SalaryCalculator.calculateActualSalary(
            salaryType: salaryType,
            baseSalary: salary,
            workHours: calculatedWorkHours
        )
    }
    
    private var isFormValid: Bool {
        return selectedWorkType != nil &&
               !workDescription.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               salary > 0 &&
               startTime < endTime
    }
    
    private var changes: [FieldChange] {
        var changes: [FieldChange] = []
        
        if workDate != workRecord.date {
            changes.append(FieldChange(
                field: "工作日期",
                oldValue: DateHelper.yearMonthDayFormatter.string(from: workRecord.date),
                newValue: DateHelper.yearMonthDayFormatter.string(from: workDate)
            ))
        }
        
        if startTime != workRecord.startTime || endTime != workRecord.endTime {
            changes.append(FieldChange(
                field: "工作时间",
                oldValue: workRecord.formattedTimeRange,
                newValue: "\(DateHelper.hourMinuteFormatter.string(from: startTime)) - \(DateHelper.hourMinuteFormatter.string(from: endTime))"
            ))
        }
        
        if selectedWorkType?.id != workRecord.workTypeId {
            let oldWorkType = dataManager.fetchWorkType(by: workRecord.workTypeId)
            changes.append(FieldChange(
                field: "工作类型",
                oldValue: oldWorkType?.name ?? "未知",
                newValue: selectedWorkType?.name ?? "未选择"
            ))
        }
        
        if workDescription != workRecord.workDescription {
            changes.append(FieldChange(
                field: "工作描述",
                oldValue: workRecord.workDescription,
                newValue: workDescription
            ))
        }
        
        if salary != workRecord.salary {
            changes.append(FieldChange(
                field: "薪资金额",
                oldValue: String(format: "%.2f", workRecord.salary),
                newValue: String(format: "%.2f", salary)
            ))
        }
        
        if salaryType != workRecord.salaryType {
            changes.append(FieldChange(
                field: "薪资类型",
                oldValue: workRecord.salaryType.displayName,
                newValue: salaryType.displayName
            ))
        }
        
        if notes != workRecord.notes {
            changes.append(FieldChange(
                field: "备注",
                oldValue: workRecord.notes.isEmpty ? "无" : workRecord.notes,
                newValue: notes.isEmpty ? "无" : notes
            ))
        }
        
        return changes
    }
    
    // MARK: - 方法
    
    private func setupInitialValues() {
        selectedWorkType = dataManager.fetchWorkType(by: workRecord.workTypeId)
    }
    
    private func trackChanges() {
        // 初始状态不算作变更
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            checkForChanges()
        }
    }
    
    private func checkForChanges() {
        hasChanges = !changes.isEmpty
    }
    
    private func saveChanges() {
        guard let workType = selectedWorkType else { return }
        
        isLoading = true
        
        // 验证数据
        let validationResult = ValidationService.validateWorkRecord(
            date: workDate,
            startTime: startTime,
            endTime: endTime,
            workTypeId: workType.id,
            workDescription: workDescription,
            salary: salary,
            salaryType: salaryType
        )
        
        if !validationResult.isValid {
            validationMessage = validationResult.errorMessage
            showingValidationAlert = true
            isLoading = false
            return
        }
        
        // 检查时间冲突（排除当前记录）
        let existingRecords = dataManager.fetchWorkRecords(for: workDate).filter { $0.id != workRecord.id }
        let conflictResult = ValidationService.validateTimeConflict(
            date: workDate,
            startTime: startTime,
            endTime: endTime,
            existingRecords: existingRecords
        )
        
        if !conflictResult.isValid {
            validationMessage = conflictResult.errorMessage
            showingValidationAlert = true
            isLoading = false
            return
        }
        
        // 更新工作记录
        workRecord.date = workDate
        workRecord.startTime = startTime
        workRecord.endTime = endTime
        workRecord.workTypeId = workType.id
        workRecord.workDescription = ValidationService.sanitizeWorkDescription(workDescription)
        workRecord.salary = salary
        workRecord.salaryType = salaryType
        workRecord.notes = notes.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 保存到数据库
        dataManager.updateWorkRecord(workRecord)
        
        isLoading = false
        dismiss()
    }
}

// MARK: - 字段变更结构
struct FieldChange {
    let field: String
    let oldValue: String
    let newValue: String
}

// MARK: - 变更行组件
struct ChangeRow: View {
    let change: FieldChange
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(change.field)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            HStack {
                Text(change.oldValue)
                    .font(.caption)
                    .foregroundColor(.red)
                    .strikethrough()
                
                Image(systemName: "arrow.right")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Text(change.newValue)
                    .font(.caption)
                    .foregroundColor(.green)
            }
        }
        .padding(.vertical, 2)
    }
}

#Preview {
    EditWorkRecordView(
        workRecord: WorkRecord(
            date: Date(),
            startTime: Date(),
            endTime: Calendar.current.date(byAdding: .hour, value: 3, to: Date()) ?? Date(),
            workTypeId: UUID(),
            workDescription: "直播带货，销售护肤品",
            salary: 50,
            salaryType: .hourly,
            notes: "今天表现不错"
        )
    )
    .environmentObject(DataManager.shared)
    .environmentObject(UserPreferences.shared)
}
