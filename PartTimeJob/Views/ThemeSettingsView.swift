//
//  ThemeSettingsView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 主题设置视图
struct ThemeSettingsView: View {
    @EnvironmentObject private var userPreferences: UserPreferences
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedTheme: AppTheme
    @State private var selectedAccentColor: AccentColor
    @State private var enableDynamicColors: Bool
    @State private var showPreview = false
    
    init() {
        _selectedTheme = State(initialValue: UserPreferences.shared.appTheme)
        _selectedAccentColor = State(initialValue: UserPreferences.shared.accentColor)
        _enableDynamicColors = State(initialValue: UserPreferences.shared.enableDynamicColors)
    }
    
    var body: some View {
        NavigationView {
            List {
                // 主题选择部分
                themeSelectionSection
                
                // 强调色选择部分
                accentColorSection
                
                // 动态颜色部分
                dynamicColorsSection
                
                // 预览部分
                previewSection
            }
            .navigationTitle("主题设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveThemeSettings()
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 主题选择部分
    private var themeSelectionSection: some View {
        Section("外观主题") {
            ForEach(AppTheme.allCases, id: \.self) { theme in
                Button {
                    selectedTheme = theme
                } label: {
                    HStack {
                        // 主题预览图标
                        ThemePreviewIcon(theme: theme)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(theme.displayName)
                                .font(.body)
                                .foregroundColor(.primary)
                            
                            Text(theme.description)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if selectedTheme == theme {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.blue)
                        }
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    // MARK: - 强调色选择部分
    private var accentColorSection: some View {
        Section("强调色") {
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(AccentColor.allCases, id: \.self) { color in
                        Button {
                            selectedAccentColor = color
                        } label: {
                            VStack(spacing: 8) {
                                Circle()
                                    .fill(color.color)
                                    .frame(width: 40, height: 40)
                                    .overlay(
                                        Circle()
                                            .stroke(selectedAccentColor == color ? Color.primary : Color.clear, lineWidth: 3)
                                    )
                                
                                Text(color.displayName)
                                    .font(.caption)
                                    .foregroundColor(.primary)
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - 动态颜色部分
    private var dynamicColorsSection: some View {
        Section("动态效果") {
            Toggle(isOn: $enableDynamicColors) {
                HStack {
                    Image(systemName: "paintpalette.fill")
                        .foregroundColor(.purple)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("动态颜色")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Text("根据系统设置自动调整颜色")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            if enableDynamicColors {
                HStack {
                    Image(systemName: "sun.max.fill")
                        .foregroundColor(.orange)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("自动切换")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Text("跟随系统明暗模式自动切换")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
    
    // MARK: - 预览部分
    private var previewSection: some View {
        Section("预览效果") {
            Button {
                showPreview = true
            } label: {
                HStack {
                    Image(systemName: "eye.fill")
                        .foregroundColor(.blue)
                        .frame(width: 24)
                    
                    Text("预览主题效果")
                        .font(.body)
                        .foregroundColor(.blue)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .buttonStyle(PlainButtonStyle())
            .sheet(isPresented: $showPreview) {
                ThemePreviewView(
                    theme: selectedTheme,
                    accentColor: selectedAccentColor,
                    enableDynamicColors: enableDynamicColors
                )
            }
            
            // 当前设置预览卡片
            ThemePreviewCard(
                theme: selectedTheme,
                accentColor: selectedAccentColor
            )
        }
    }
    
    // MARK: - 方法
    
    private func saveThemeSettings() {
        userPreferences.appTheme = selectedTheme
        userPreferences.accentColor = selectedAccentColor
        userPreferences.enableDynamicColors = enableDynamicColors
        
        // 应用主题变更
        applyThemeChanges()
        
        CalendarHapticFeedback.successAction()
    }
    
    private func applyThemeChanges() {
        // 这里可以添加主题应用逻辑
        // 例如更新全局颜色、字体等
    }
}

// MARK: - 应用主题枚举
enum AppTheme: String, CaseIterable {
    case system = "system"
    case light = "light"
    case dark = "dark"
    
    var displayName: String {
        switch self {
        case .system:
            return "跟随系统"
        case .light:
            return "浅色模式"
        case .dark:
            return "深色模式"
        }
    }
    
    var description: String {
        switch self {
        case .system:
            return "自动跟随系统设置"
        case .light:
            return "始终使用浅色主题"
        case .dark:
            return "始终使用深色主题"
        }
    }
    
    var colorScheme: ColorScheme? {
        switch self {
        case .system:
            return nil
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
}

// MARK: - 强调色枚举
enum AccentColor: String, CaseIterable {
    case blue = "blue"
    case green = "green"
    case orange = "orange"
    case red = "red"
    case purple = "purple"
    case pink = "pink"
    case teal = "teal"
    case indigo = "indigo"
    
    var displayName: String {
        switch self {
        case .blue:
            return "蓝色"
        case .green:
            return "绿色"
        case .orange:
            return "橙色"
        case .red:
            return "红色"
        case .purple:
            return "紫色"
        case .pink:
            return "粉色"
        case .teal:
            return "青色"
        case .indigo:
            return "靛蓝"
        }
    }
    
    var color: Color {
        switch self {
        case .blue:
            return .blue
        case .green:
            return .green
        case .orange:
            return .orange
        case .red:
            return .red
        case .purple:
            return .purple
        case .pink:
            return .pink
        case .teal:
            return .teal
        case .indigo:
            return .indigo
        }
    }
}

// MARK: - 主题预览图标
struct ThemePreviewIcon: View {
    let theme: AppTheme
    
    var body: some View {
        HStack(spacing: 4) {
            switch theme {
            case .system:
                Circle()
                    .fill(LinearGradient(colors: [.white, .black], startPoint: .leading, endPoint: .trailing))
                    .frame(width: 20, height: 20)
            case .light:
                Circle()
                    .fill(Color.white)
                    .frame(width: 20, height: 20)
                    .overlay(Circle().stroke(Color.gray, lineWidth: 1))
            case .dark:
                Circle()
                    .fill(Color.black)
                    .frame(width: 20, height: 20)
            }
        }
        .frame(width: 40)
    }
}

// MARK: - 主题预览卡片
struct ThemePreviewCard: View {
    let theme: AppTheme
    let accentColor: AccentColor
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("预览效果")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Circle()
                    .fill(accentColor.color)
                    .frame(width: 12, height: 12)
            }
            
            HStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 4) {
                    Rectangle()
                        .fill(accentColor.color)
                        .frame(height: 4)
                        .cornerRadius(2)
                    
                    Rectangle()
                        .fill(Color(.systemGray4))
                        .frame(height: 4)
                        .cornerRadius(2)
                    
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(height: 4)
                        .cornerRadius(2)
                }
                
                Spacer()
                
                VStack(spacing: 4) {
                    Circle()
                        .fill(accentColor.color)
                        .frame(width: 16, height: 16)
                    
                    Circle()
                        .fill(Color(.systemGray4))
                        .frame(width: 12, height: 12)
                    
                    Circle()
                        .fill(Color(.systemGray5))
                        .frame(width: 8, height: 8)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - 主题预览视图
struct ThemePreviewView: View {
    let theme: AppTheme
    let accentColor: AccentColor
    let enableDynamicColors: Bool
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("主题预览")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                VStack(spacing: 16) {
                    // 模拟日历视图
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
                        ForEach(1...14, id: \.self) { day in
                            Circle()
                                .fill(day == 7 ? accentColor.color : Color(.systemGray5))
                                .frame(width: 32, height: 32)
                                .overlay(
                                    Text("\(day)")
                                        .font(.caption)
                                        .foregroundColor(day == 7 ? .white : .primary)
                                )
                        }
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(16)
                    
                    // 模拟按钮
                    Button("示例按钮") {
                        // 预览按钮
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(accentColor.color)
                    .cornerRadius(12)
                    
                    // 模拟卡片
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Circle()
                                .fill(accentColor.color)
                                .frame(width: 12, height: 12)
                            
                            Text("工作记录示例")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Spacer()
                        }
                        
                        Text("这是一个工作记录的预览效果")
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                }
                .padding()
                
                Spacer()
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("预览")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
        .preferredColorScheme(theme.colorScheme)
        .accentColor(accentColor.color)
    }
}

#Preview {
    ThemeSettingsView()
        .environmentObject(UserPreferences.shared)
}
