//
//  AboutView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 关于应用视图
struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var showingLicenses = false
    @State private var showingPrivacyPolicy = false
    @State private var showingTermsOfService = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 应用图标和信息
                    appInfoSection
                    
                    // 功能特色
                    featuresSection
                    
                    // 开发团队
                    teamSection
                    
                    // 技术栈
                    technologySection
                    
                    // 法律信息
                    legalSection
                    
                    // 联系方式
                    contactSection
                }
                .padding()
            }
            .navigationTitle("关于应用")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingLicenses) {
                LicensesView()
            }
            .sheet(isPresented: $showingPrivacyPolicy) {
                WebView(url: "https://your-website.com/privacy")
            }
            .sheet(isPresented: $showingTermsOfService) {
                WebView(url: "https://your-website.com/terms")
            }
        }
    }
    
    // MARK: - 应用信息部分
    private var appInfoSection: some View {
        VStack(spacing: 16) {
            // 应用图标
            RoundedRectangle(cornerRadius: 20)
                .fill(LinearGradient(
                    colors: [.blue, .purple],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(width: 100, height: 100)
                .overlay(
                    Image(systemName: "briefcase.fill")
                        .font(.system(size: 50))
                        .foregroundColor(.white)
                )
            
            VStack(spacing: 8) {
                Text("兼职记录助手")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text("版本 \(appVersion) (\(buildNumber))")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text("让兼职工作管理变得简单高效")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
        }
    }
    
    // MARK: - 功能特色部分
    private var featuresSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("功能特色")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                FeatureCard(
                    icon: "calendar.badge.plus",
                    title: "智能日历",
                    description: "直观的日历视图管理工作安排",
                    color: .blue
                )
                
                FeatureCard(
                    icon: "chart.bar.fill",
                    title: "数据统计",
                    description: "详细的收入和工时统计分析",
                    color: .green
                )
                
                FeatureCard(
                    icon: "bell.fill",
                    title: "智能提醒",
                    description: "工作开始和结束的及时提醒",
                    color: .orange
                )
                
                FeatureCard(
                    icon: "square.and.arrow.up",
                    title: "数据导出",
                    description: "多格式数据导出和备份",
                    color: .purple
                )
            }
        }
    }
    
    // MARK: - 开发团队部分
    private var teamSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("开发团队")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                TeamMemberCard(
                    name: "iOS Expert",
                    role: "主要开发者",
                    avatar: "person.circle.fill",
                    description: "负责应用的整体架构和功能实现"
                )
                
                TeamMemberCard(
                    name: "UI Designer",
                    role: "界面设计师",
                    avatar: "paintbrush.fill",
                    description: "负责用户界面和用户体验设计"
                )
            }
        }
    }
    
    // MARK: - 技术栈部分
    private var technologySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("技术栈")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                TechnologyRow(name: "SwiftUI", description: "现代化的用户界面框架")
                TechnologyRow(name: "SwiftData", description: "数据持久化和管理")
                TechnologyRow(name: "UserNotifications", description: "本地通知和提醒")
                TechnologyRow(name: "Charts", description: "数据可视化图表")
                TechnologyRow(name: "Combine", description: "响应式编程框架")
            }
        }
    }
    
    // MARK: - 法律信息部分
    private var legalSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("法律信息")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                Button {
                    showingPrivacyPolicy = true
                } label: {
                    LegalRow(title: "隐私政策", icon: "hand.raised.fill")
                }
                .buttonStyle(PlainButtonStyle())
                
                Button {
                    showingTermsOfService = true
                } label: {
                    LegalRow(title: "服务条款", icon: "doc.text.fill")
                }
                .buttonStyle(PlainButtonStyle())
                
                Button {
                    showingLicenses = true
                } label: {
                    LegalRow(title: "开源许可", icon: "heart.fill")
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    // MARK: - 联系方式部分
    private var contactSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("联系我们")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                ContactRow(
                    icon: "envelope.fill",
                    title: "邮箱",
                    value: "<EMAIL>",
                    action: {
                        openEmail()
                    }
                )
                
                ContactRow(
                    icon: "link",
                    title: "官网",
                    value: "www.parttimejob.app",
                    action: {
                        openWebsite()
                    }
                )
                
                ContactRow(
                    icon: "star.fill",
                    title: "App Store 评分",
                    value: "为我们评分",
                    action: {
                        openAppStore()
                    }
                )
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var appVersion: String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }
    
    private var buildNumber: String {
        return Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
    }
    
    // MARK: - 方法
    
    private func openEmail() {
        if let url = URL(string: "mailto:<EMAIL>") {
            UIApplication.shared.open(url)
        }
    }
    
    private func openWebsite() {
        if let url = URL(string: "https://www.parttimejob.app") {
            UIApplication.shared.open(url)
        }
    }
    
    private func openAppStore() {
        if let url = URL(string: "https://apps.apple.com/app/id123456789") {
            UIApplication.shared.open(url)
        }
    }
}

// MARK: - 功能卡片组件
struct FeatureCard: View {
    let icon: String
    let title: String
    let description: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            VStack(spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - 团队成员卡片
struct TeamMemberCard: View {
    let name: String
    let role: String
    let avatar: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: avatar)
                .font(.title)
                .foregroundColor(.blue)
                .frame(width: 40)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(name)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(role)
                    .font(.subheadline)
                    .foregroundColor(.blue)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - 技术栈行组件
struct TechnologyRow: View {
    let name: String
    let description: String
    
    var body: some View {
        HStack {
            Text(name)
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Spacer()
            
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 法律信息行组件
struct LegalRow: View {
    let title: String
    let icon: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 20)
            
            Text(title)
                .font(.body)
                .foregroundColor(.blue)
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 8)
    }
}

// MARK: - 联系方式行组件
struct ContactRow: View {
    let icon: String
    let title: String
    let value: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.blue)
                    .frame(width: 20)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .foregroundColor(.primary)
                    
                    Text(value)
                        .font(.caption)
                        .foregroundColor(.blue)
                }
                
                Spacer()
                
                Image(systemName: "arrow.up.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 开源许可视图
struct LicensesView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                Section("开源库") {
                    LicenseRow(name: "SwiftUI", license: "MIT License")
                    LicenseRow(name: "SwiftData", license: "Apple License")
                    LicenseRow(name: "Charts", license: "MIT License")
                }
                
                Section("致谢") {
                    Text("感谢所有开源社区的贡献者，让这个应用得以实现。")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .padding(.vertical, 8)
                }
            }
            .navigationTitle("开源许可")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - 许可证行组件
struct LicenseRow: View {
    let name: String
    let license: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(name)
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Text(license)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 网页视图
struct WebView: UIViewRepresentable {
    let url: String
    
    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView()
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        if let url = URL(string: url) {
            let request = URLRequest(url: url)
            webView.load(request)
        }
    }
}

import WebKit

#Preview {
    AboutView()
}
