//
//  EditWorkTypeView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 编辑工作类型视图
struct EditWorkTypeView: View {
    let workType: WorkType
    
    @EnvironmentObject private var dataManager: DataManager
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 表单状态
    @State private var name = ""
    @State private var selectedIcon = ""
    @State private var selectedColor = Color.blue
    @State private var defaultSalary: Double = 0
    @State private var salaryType: SalaryType = .hourly
    @State private var workDescription = ""
    @State private var isActive = true
    
    // MARK: - UI状态
    @State private var showingIconPicker = false
    @State private var showingColorPicker = false
    @State private var showingValidationAlert = false
    @State private var showingUsageStats = false
    @State private var validationMessage = ""
    @State private var isLoading = false
    @State private var usageStats: WorkTypeUsageStats?
    
    var body: some View {
        NavigationView {
            Form {
                // 使用统计部分
                if let stats = usageStats {
                    usageStatsSection(stats)
                }
                
                // 基本信息部分
                basicInfoSection
                
                // 外观设置部分
                appearanceSection
                
                // 薪资设置部分
                salarySection
                
                // 其他设置部分
                otherSettingsSection
                
                // 预览部分
                previewSection
                
                // 危险操作部分
                dangerZoneSection
            }
            .navigationTitle("编辑工作类型")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveChanges()
                    }
                    .disabled(!isFormValid || isLoading)
                }
            }
            .alert("验证失败", isPresented: $showingValidationAlert) {
                Button("确定") { }
            } message: {
                Text(validationMessage)
            }
            .sheet(isPresented: $showingIconPicker) {
                IconPickerView(selectedIcon: $selectedIcon)
            }
            .sheet(isPresented: $showingColorPicker) {
                ColorPickerView(selectedColor: $selectedColor)
            }
            .onAppear {
                loadWorkTypeData()
                loadUsageStats()
            }
        }
    }
    
    // MARK: - 使用统计部分
    private func usageStatsSection(_ stats: WorkTypeUsageStats) -> some View {
        Section("使用统计") {
            VStack(spacing: 12) {
                HStack {
                    StatCard(
                        title: "使用次数",
                        value: "\(stats.totalRecords)",
                        icon: "clock",
                        color: .blue
                    )
                    
                    StatCard(
                        title: "总收入",
                        value: "¥\(stats.totalIncome, specifier: "%.0f")",
                        icon: "yensign.circle",
                        color: .green
                    )
                }
                
                HStack {
                    StatCard(
                        title: "总工时",
                        value: "\(stats.totalHours, specifier: "%.1f")h",
                        icon: "timer",
                        color: .orange
                    )
                    
                    StatCard(
                        title: "平均收入",
                        value: "¥\(stats.averageIncome, specifier: "%.0f")",
                        icon: "chart.bar",
                        color: .purple
                    )
                }
                
                if let lastUsed = stats.lastUsedDate {
                    HStack {
                        Text("最后使用：")
                            .foregroundColor(.secondary)
                        Text(DateHelper.timeAgoDescription(from: lastUsed))
                            .foregroundColor(.primary)
                        Spacer()
                    }
                    .font(.caption)
                }
            }
        }
    }
    
    // MARK: - 基本信息部分
    private var basicInfoSection: some View {
        Section("基本信息") {
            TextField("工作类型名称", text: $name)
                .textFieldStyle(RoundedBorderTextFieldStyle())
            
            TextField("工作描述（可选）", text: $workDescription, axis: .vertical)
                .lineLimit(2...4)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
    }
    
    // MARK: - 外观设置部分
    private var appearanceSection: some View {
        Section("外观设置") {
            // 图标选择
            HStack {
                Text("图标")
                Spacer()
                Button {
                    showingIconPicker = true
                } label: {
                    HStack {
                        Image(systemName: selectedIcon)
                            .foregroundColor(selectedColor)
                        Text("更改图标")
                            .foregroundColor(.blue)
                    }
                }
            }
            
            // 颜色选择
            HStack {
                Text("颜色")
                Spacer()
                Button {
                    showingColorPicker = true
                } label: {
                    HStack {
                        Circle()
                            .fill(selectedColor)
                            .frame(width: 24, height: 24)
                        Text("更改颜色")
                            .foregroundColor(.blue)
                    }
                }
            }
        }
    }
    
    // MARK: - 薪资设置部分
    private var salarySection: some View {
        Section("薪资设置") {
            Picker("薪资类型", selection: $salaryType) {
                ForEach(SalaryType.allCases, id: \.self) { type in
                    Text(type.displayName).tag(type)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            
            HStack {
                Text("默认薪资")
                Spacer()
                TextField("0", value: $defaultSalary, format: .number)
                    .keyboardType(.decimalPad)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(width: 100)
                Text(salaryType.unit)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - 其他设置部分
    private var otherSettingsSection: some View {
        Section("其他设置") {
            Toggle("启用此工作类型", isOn: $isActive)
        }
    }
    
    // MARK: - 预览部分
    private var previewSection: some View {
        Section("预览") {
            HStack(spacing: 16) {
                // 图标预览
                ZStack {
                    Circle()
                        .fill(selectedColor)
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: selectedIcon)
                        .font(.title2)
                        .foregroundColor(.white)
                }
                
                // 信息预览
                VStack(alignment: .leading, spacing: 4) {
                    Text(name.isEmpty ? "工作类型名称" : name)
                        .font(.headline)
                        .foregroundColor(name.isEmpty ? .secondary : .primary)
                    
                    HStack {
                        Text(salaryType.displayName)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.blue.opacity(0.1))
                            .foregroundColor(.blue)
                            .cornerRadius(4)
                        
                        Text("¥\(defaultSalary, specifier: "%.0f")")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.green)
                        
                        Spacer()
                    }
                    
                    if !workDescription.isEmpty {
                        Text(workDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                }
                
                Spacer()
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - 危险操作部分
    private var dangerZoneSection: some View {
        Section("危险操作") {
            Button("删除工作类型") {
                // 这里可以添加删除确认逻辑
            }
            .foregroundColor(.red)
        }
    }
    
    // MARK: - 计算属性
    private var isFormValid: Bool {
        return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               defaultSalary >= 0
    }
    
    // MARK: - 数据加载
    private func loadWorkTypeData() {
        name = workType.name
        selectedIcon = workType.iconName
        selectedColor = Color(hex: workType.colorHex) ?? .blue
        defaultSalary = workType.defaultSalary
        salaryType = workType.salaryType
        workDescription = workType.workDescription
        isActive = workType.isActive
    }
    
    private func loadUsageStats() {
        let records = dataManager.fetchWorkRecords().filter { $0.workTypeId == workType.id }
        
        let totalRecords = records.count
        let totalIncome = records.reduce(0) { $0 + $1.actualSalary }
        let totalHours = records.reduce(0) { $0 + $1.workHours }
        let lastUsedDate = records.map { $0.date }.max()
        
        usageStats = WorkTypeUsageStats(
            totalRecords: totalRecords,
            totalIncome: totalIncome,
            totalHours: totalHours,
            lastUsedDate: lastUsedDate,
            averageIncome: totalRecords > 0 ? totalIncome / Double(totalRecords) : 0
        )
    }
    
    // MARK: - 保存方法
    private func saveChanges() {
        guard isFormValid else {
            validationMessage = "请填写完整的工作类型信息"
            showingValidationAlert = true
            return
        }
        
        isLoading = true
        
        // 检查名称是否与其他工作类型重复
        let existingWorkTypes = dataManager.fetchWorkTypes().filter { $0.id != workType.id }
        if existingWorkTypes.contains(where: { $0.name == name.trimmingCharacters(in: .whitespacesAndNewlines) }) {
            validationMessage = "工作类型名称已存在，请使用其他名称"
            showingValidationAlert = true
            isLoading = false
            return
        }
        
        // 更新工作类型
        let updatedWorkType = WorkType(
            id: workType.id,
            name: name.trimmingCharacters(in: .whitespacesAndNewlines),
            iconName: selectedIcon,
            colorHex: selectedColor.toHex(),
            defaultSalary: defaultSalary,
            salaryType: salaryType,
            workDescription: workDescription.trimmingCharacters(in: .whitespacesAndNewlines),
            isActive: isActive,
            createdAt: workType.createdAt,
            updatedAt: Date()
        )
        
        // 保存到数据库
        dataManager.updateWorkType(updatedWorkType)
        
        isLoading = false
        CalendarHapticFeedback.successAction()
        dismiss()
    }
}

// MARK: - 统计卡片组件

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
            }
            
            HStack {
                Text(value)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                Spacer()
            }
        }
        .padding(8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

#Preview {
    EditWorkTypeView(workType: WorkType.createDefaultWorkTypes()[0])
        .environmentObject(DataManager.shared)
}
