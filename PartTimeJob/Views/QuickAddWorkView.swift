//
//  QuickAddWorkView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 快速添加工作记录视图
struct QuickAddWorkView: View {
    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences
    
    @State private var showingFullAddView = false
    @State private var selectedWorkType: WorkType?
    @State private var quickWorkDescription = ""
    @State private var isWorking = false
    @State private var currentWorkRecord: WorkRecord?
    @State private var workStartTime: Date?
    @State private var showingTemplates = false
    @State private var workTemplates: [WorkTemplate] = []
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 顶部状态卡片
                currentStatusCard
                
                // 快速操作区域
                if !isWorking {
                    quickStartSection
                } else {
                    workingStatusSection
                }
                
                // 快捷操作按钮
                quickActionsSection
                
                Spacer()
            }
            .padding()
            .navigationTitle("快速添加")
            .navigationBarTitleDisplayMode(.large)
            .sheet(isPresented: $showingFullAddView) {
                AddWorkRecordView(selectedDate: Date())
            }
        }
    }
    
    // MARK: - 当前状态卡片
    private var currentStatusCard: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: isWorking ? "play.circle.fill" : "plus.circle.fill")
                    .font(.title)
                    .foregroundColor(isWorking ? .green : .blue)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(isWorking ? "工作进行中" : "准备开始工作")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(isWorking ? "点击结束按钮完成记录" : "选择工作类型快速开始")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            if isWorking, let startTime = workStartTime {
                HStack {
                    Text("开始时间:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(DateHelper.hourMinuteFormatter.string(from: startTime))
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Text("已工作:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(elapsedTimeText)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 快速开始部分
    private var quickStartSection: some View {
        VStack(spacing: 16) {
            Text("快速开始工作")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 工作类型选择
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(recentWorkTypes, id: \.id) { workType in
                    QuickWorkTypeCard(
                        workType: workType,
                        isSelected: selectedWorkType?.id == workType.id,
                        onTap: {
                            selectedWorkType = workType
                        }
                    )
                }
            }
            
            // 工作描述输入
            VStack(alignment: .leading, spacing: 8) {
                Text("工作内容（可选）")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                TextField("简单描述今天的工作...", text: $quickWorkDescription)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            
            // 开始工作按钮
            Button {
                startQuickWork()
            } label: {
                HStack {
                    Image(systemName: "play.fill")
                    Text("开始工作")
                }
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(selectedWorkType != nil ? Color.green : Color.gray)
                .cornerRadius(12)
            }
            .disabled(selectedWorkType == nil)
        }
    }
    
    // MARK: - 工作中状态部分
    private var workingStatusSection: some View {
        VStack(spacing: 16) {
            if let workType = selectedWorkType {
                HStack {
                    Image(systemName: workType.iconName)
                        .font(.title2)
                        .foregroundColor(workType.themeColor)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(workType.name)
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        if !quickWorkDescription.isEmpty {
                            Text(quickWorkDescription)
                                .font(.body)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer()
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            }
            
            // 结束工作按钮
            Button {
                endQuickWork()
            } label: {
                HStack {
                    Image(systemName: "stop.fill")
                    Text("结束工作")
                }
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.red)
                .cornerRadius(12)
            }
        }
    }
    
    // MARK: - 快捷操作部分
    private var quickActionsSection: some View {
        VStack(spacing: 12) {
            Text("其他操作")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                QuickActionButton(
                    title: "详细添加",
                    subtitle: "填写完整的工作记录信息",
                    icon: "doc.text.fill",
                    color: .blue
                ) {
                    showingFullAddView = true
                }
                
                QuickActionButton(
                    title: "复制昨日",
                    subtitle: "复制昨天的工作安排",
                    icon: "doc.on.doc.fill",
                    color: .orange
                ) {
                    copyYesterdayWork()
                }
                
                QuickActionButton(
                    title: "模板创建",
                    subtitle: "使用预设模板快速创建",
                    icon: "square.grid.3x3.fill",
                    color: .purple
                ) {
                    showingTemplates = true
                }
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var recentWorkTypes: [WorkType] {
        let allWorkTypes = dataManager.fetchWorkTypes()
        // 这里可以根据使用频率排序，暂时返回前4个
        return Array(allWorkTypes.prefix(4))
    }
    
    private var elapsedTimeText: String {
        guard let startTime = workStartTime else { return "00:00" }
        let elapsed = Date().timeIntervalSince(startTime)
        return DateHelper.workDurationDescription(elapsed)
    }
    
    // MARK: - 方法
    
    private func startQuickWork() {
        guard let workType = selectedWorkType else { return }
        
        isWorking = true
        workStartTime = Date()
        
        // 创建临时工作记录
        currentWorkRecord = WorkRecord(
            date: Date(),
            startTime: Date(),
            endTime: Date(), // 临时值，结束时会更新
            workTypeId: workType.id,
            workDescription: quickWorkDescription.isEmpty ? "快速记录" : quickWorkDescription,
            salary: workType.defaultSalary,
            salaryType: workType.defaultSalaryType,
            notes: ""
        )
        
        // 触觉反馈
        CalendarHapticFeedback.successAction()
    }
    
    private func endQuickWork() {
        guard let startTime = workStartTime,
              let workType = selectedWorkType else { return }
        
        let endTime = Date()
        
        // 创建完整的工作记录
        let workRecord = WorkRecord(
            date: Date(),
            startTime: startTime,
            endTime: endTime,
            workTypeId: workType.id,
            workDescription: quickWorkDescription.isEmpty ? "快速记录" : quickWorkDescription,
            salary: workType.defaultSalary,
            salaryType: workType.defaultSalaryType,
            notes: "通过快速添加创建"
        )
        
        // 保存到数据库
        dataManager.addWorkRecord(workRecord)
        
        // 重置状态
        isWorking = false
        workStartTime = nil
        currentWorkRecord = nil
        selectedWorkType = nil
        quickWorkDescription = ""
        
        // 触觉反馈
        CalendarHapticFeedback.successAction()
    }
    
    private func copyYesterdayWork() {
        let calendar = Calendar.current
        guard let yesterday = calendar.date(byAdding: .day, value: -1, to: Date()) else { return }
        
        let yesterdayRecords = dataManager.fetchWorkRecords(for: yesterday)
        
        for record in yesterdayRecords {
            // 创建今天的工作记录
            let newStartTime = calendar.date(
                bySettingHour: calendar.component(.hour, from: record.startTime),
                minute: calendar.component(.minute, from: record.startTime),
                second: 0,
                of: Date()
            ) ?? record.startTime
            
            let newEndTime = calendar.date(
                bySettingHour: calendar.component(.hour, from: record.endTime),
                minute: calendar.component(.minute, from: record.endTime),
                second: 0,
                of: Date()
            ) ?? record.endTime
            
            let newRecord = WorkRecord(
                date: Date(),
                startTime: newStartTime,
                endTime: newEndTime,
                workTypeId: record.workTypeId,
                workDescription: record.workDescription,
                salary: record.salary,
                salaryType: record.salaryType,
                notes: "复制自昨日工作"
            )
            
            dataManager.addWorkRecord(newRecord)
        }
        
        CalendarHapticFeedback.successAction()
    }
}

// MARK: - 快速工作类型卡片
struct QuickWorkTypeCard: View {
    let workType: WorkType
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Image(systemName: workType.iconName)
                    .font(.title2)
                    .foregroundColor(workType.themeColor)
                
                Text(workType.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Text(workType.formattedDefaultSalary)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(height: 80)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? workType.themeColor.opacity(0.2) : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? workType.themeColor : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 快捷操作按钮
struct QuickActionButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                    .frame(width: 32)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - 模板相关方法

    private func loadWorkTemplates() {
        // 加载预设模板
        workTemplates = [
            WorkTemplate(
                name: "直播带货",
                workTypeId: nil, // 需要用户选择
                description: "直播带货，销售产品",
                defaultDuration: 4.0, // 4小时
                defaultSalary: 100,
                salaryType: .fixed
            ),
            WorkTemplate(
                name: "兼职服务员",
                workTypeId: nil,
                description: "餐厅服务员工作",
                defaultDuration: 6.0, // 6小时
                defaultSalary: 20,
                salaryType: .hourly
            ),
            WorkTemplate(
                name: "家教辅导",
                workTypeId: nil,
                description: "一对一家教辅导",
                defaultDuration: 2.0, // 2小时
                defaultSalary: 80,
                salaryType: .hourly
            )
        ]
    }

    private func applyTemplate(_ template: WorkTemplate) {
        // 应用模板到快速添加表单
        quickWorkDescription = template.description

        // 如果有指定工作类型，自动选择
        if let workTypeId = template.workTypeId {
            selectedWorkType = dataManager.fetchWorkTypes().first { $0.id == workTypeId }
        }

        showingTemplates = false
    }
}

// MARK: - 工作模板数据结构

struct WorkTemplate {
    let name: String
    let workTypeId: UUID?
    let description: String
    let defaultDuration: Double // 小时
    let defaultSalary: Double
    let salaryType: SalaryType
}

// MARK: - 工作模板选择视图

struct WorkTemplateSelectionView: View {
    let templates: [WorkTemplate]
    let onTemplateSelected: (WorkTemplate) -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List {
                Section("预设模板") {
                    ForEach(templates, id: \.name) { template in
                        Button {
                            onTemplateSelected(template)
                            dismiss()
                        } label: {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(template.name)
                                    .font(.headline)
                                    .foregroundColor(.primary)

                                Text(template.description)
                                    .font(.body)
                                    .foregroundColor(.secondary)

                                HStack {
                                    Text("\(template.defaultDuration, specifier: "%.1f")小时")
                                        .font(.caption)
                                        .foregroundColor(.blue)

                                    Spacer()

                                    Text("¥\(template.defaultSalary, specifier: "%.0f")")
                                        .font(.caption)
                                        .foregroundColor(.green)
                                }
                            }
                            .padding(.vertical, 4)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
            .navigationTitle("选择模板")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    QuickAddWorkView()
        .environmentObject(DataManager.shared)
        .environmentObject(UserPreferences.shared)
}
