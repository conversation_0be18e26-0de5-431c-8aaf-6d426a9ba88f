//
//  SettingsView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 设置主视图
struct SettingsView: View {
    @EnvironmentObject private var userPreferences: UserPreferences
    @EnvironmentObject private var dataManager: DataManager
    
    @State private var showingProfileEdit = false
    @State private var showingDataExport = false
    @State private var showingAbout = false
    @State private var showingDeleteConfirmation = false
    
    var body: some View {
        NavigationView {
            List {
                // 个人资料部分
                profileSection
                
                // 显示设置部分
                displaySection
                
                // 通知设置部分
                notificationSection
                
                // 数据管理部分
                dataManagementSection
                
                // 关于应用部分
                aboutSection
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.large)
            .sheet(isPresented: $showingProfileEdit) {
                ProfileEditView()
            }
            .sheet(isPresented: $showingDataExport) {
                DataExportView()
            }
            .sheet(isPresented: $showingAbout) {
                AboutView()
            }
            .alert("清除所有数据", isPresented: $showingDeleteConfirmation) {
                Button("取消", role: .cancel) { }
                But<PERSON>("确认清除", role: .destructive) {
                    clearAllData()
                }
            } message: {
                Text("此操作将删除所有工作记录和设置，且无法恢复。确定要继续吗？")
            }
        }
    }
    
    // MARK: - 个人资料部分
    private var profileSection: some View {
        Section {
            Button {
                showingProfileEdit = true
            } label: {
                HStack(spacing: 16) {
                    // 头像
                    Circle()
                        .fill(LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .frame(width: 60, height: 60)
                        .overlay(
                            Text(userPreferences.userName.prefix(1).uppercased())
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )
                    
                    // 用户信息
                    VStack(alignment: .leading, spacing: 4) {
                        Text(userPreferences.userName.isEmpty ? "点击设置姓名" : userPreferences.userName)
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Text(userPreferences.userTitle.isEmpty ? "设置个人简介" : userPreferences.userTitle)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            Image(systemName: "calendar")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                            
                            Text("已使用 \(daysSinceFirstUse) 天")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - 显示设置部分
    private var displaySection: some View {
        Section("显示设置") {
            // 主题设置
            NavigationLink {
                ThemeSettingsView()
            } label: {
                SettingsRow(
                    icon: "paintbrush.fill",
                    title: "主题设置",
                    subtitle: userPreferences.appTheme.displayName,
                    iconColor: .purple
                )
            }
            
            // 货币设置
            HStack {
                SettingsRow(
                    icon: "dollarsign.circle.fill",
                    title: "货币符号",
                    subtitle: userPreferences.currencySymbol,
                    iconColor: .green
                )
                
                Spacer()
                
                Picker("货币", selection: $userPreferences.currencySymbol) {
                    Text("¥").tag("¥")
                    Text("$").tag("$")
                    Text("€").tag("€")
                    Text("£").tag("£")
                }
                .pickerStyle(MenuPickerStyle())
            }
            
            // 日历视图类型
            HStack {
                SettingsRow(
                    icon: "calendar",
                    title: "默认日历视图",
                    subtitle: userPreferences.calendarViewType.displayName,
                    iconColor: .blue
                )
                
                Spacer()
                
                Picker("视图", selection: $userPreferences.calendarViewType) {
                    ForEach(CalendarViewType.allCases, id: \.self) { type in
                        Text(type.displayName).tag(type)
                    }
                }
                .pickerStyle(MenuPickerStyle())
            }
            
            // 显示选项
            Toggle(isOn: $userPreferences.showWorkTypeLegend) {
                SettingsRow(
                    icon: "square.grid.3x3",
                    title: "显示工作类型图例",
                    subtitle: "在日历下方显示",
                    iconColor: .orange
                )
            }
            
            Toggle(isOn: $userPreferences.showEfficiencyRating) {
                SettingsRow(
                    icon: "star.fill",
                    title: "显示效率评级",
                    subtitle: "在日历中显示星级",
                    iconColor: .yellow
                )
            }
            
            Toggle(isOn: $userPreferences.showIncomePreview) {
                SettingsRow(
                    icon: "banknote",
                    title: "显示收入预览",
                    subtitle: "在日历中显示收入",
                    iconColor: .green
                )
            }
        }
    }
    
    // MARK: - 通知设置部分
    private var notificationSection: some View {
        Section("通知设置") {
            NavigationLink {
                NotificationSettingsView()
            } label: {
                SettingsRow(
                    icon: "bell.fill",
                    title: "工作提醒",
                    subtitle: userPreferences.enableNotifications ? "已开启" : "已关闭",
                    iconColor: .red
                )
            }
            
            Toggle(isOn: $userPreferences.enableNotifications) {
                SettingsRow(
                    icon: "bell.badge",
                    title: "推送通知",
                    subtitle: "工作开始和结束提醒",
                    iconColor: .red
                )
            }
            
            if userPreferences.enableNotifications {
                HStack {
                    SettingsRow(
                        icon: "clock",
                        title: "提前提醒",
                        subtitle: "\(userPreferences.notificationMinutesBefore) 分钟",
                        iconColor: .blue
                    )
                    
                    Spacer()
                    
                    Picker("提醒时间", selection: $userPreferences.notificationMinutesBefore) {
                        Text("5分钟").tag(5)
                        Text("10分钟").tag(10)
                        Text("15分钟").tag(15)
                        Text("30分钟").tag(30)
                    }
                    .pickerStyle(MenuPickerStyle())
                }
            }
        }
    }
    
    // MARK: - 数据管理部分
    private var dataManagementSection: some View {
        Section("数据管理") {
            Button {
                showingDataExport = true
            } label: {
                SettingsRow(
                    icon: "square.and.arrow.up",
                    title: "导出数据",
                    subtitle: "备份工作记录",
                    iconColor: .blue
                )
            }
            .buttonStyle(PlainButtonStyle())
            
            NavigationLink {
                DataStatisticsView()
            } label: {
                SettingsRow(
                    icon: "chart.bar.fill",
                    title: "数据统计",
                    subtitle: "查看详细统计",
                    iconColor: .green
                )
            }
            
            Button {
                showingDeleteConfirmation = true
            } label: {
                SettingsRow(
                    icon: "trash.fill",
                    title: "清除所有数据",
                    subtitle: "删除所有记录",
                    iconColor: .red
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - 关于应用部分
    private var aboutSection: some View {
        Section("关于") {
            Button {
                showingAbout = true
            } label: {
                SettingsRow(
                    icon: "info.circle.fill",
                    title: "关于应用",
                    subtitle: "版本 \(appVersion)",
                    iconColor: .gray
                )
            }
            .buttonStyle(PlainButtonStyle())
            
            Link(destination: URL(string: "https://github.com/your-repo")!) {
                SettingsRow(
                    icon: "link",
                    title: "GitHub",
                    subtitle: "查看源代码",
                    iconColor: .black
                )
            }
            
            Button {
                shareApp()
            } label: {
                SettingsRow(
                    icon: "heart.fill",
                    title: "推荐给朋友",
                    subtitle: "分享这个应用",
                    iconColor: .pink
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - 计算属性
    
    private var daysSinceFirstUse: Int {
        let calendar = Calendar.current
        let firstUseDate = userPreferences.firstUseDate
        let components = calendar.dateComponents([.day], from: firstUseDate, to: Date())
        return max(1, components.day ?? 1)
    }
    
    private var appVersion: String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }
    
    // MARK: - 方法
    
    private func clearAllData() {
        dataManager.clearAllData()
        userPreferences.resetToDefaults()
        CalendarHapticFeedback.successAction()
    }
    
    private func shareApp() {
        let shareText = "我在使用「兼职记录助手」管理我的兼职工作，非常好用！推荐给你试试。"
        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true)
        }
    }
}

// MARK: - 设置行组件
struct SettingsRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let iconColor: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(iconColor)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.body)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

#Preview {
    SettingsView()
        .environmentObject(UserPreferences.shared)
        .environmentObject(DataManager.shared)
}
