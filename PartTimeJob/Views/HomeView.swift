//
//  HomeView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 首页视图 - 日历展示
struct HomeView: View {
    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences

    @State private var selectedDate = Date()
    @State private var showingAddWork = false
    @State private var showingWorkDetail = false
    @State private var selectedWorkRecord: WorkRecord?
    @State private var calendarViewType: CalendarViewType = .month
    @State private var showingDatePicker = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部统计卡片
                if userPreferences.showIncomePreview {
                    todayStatsCard
                }

                // 日历工具栏
                CalendarToolbar(
                    selectedDate: $selectedDate,
                    calendarViewType: $calendarViewType,
                    onDatePickerTap: {
                        showingDatePicker = true
                    },
                    onTodayTap: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            selectedDate = Date()
                        }
                    },
                    onAddWorkTap: {
                        selectedDate = Date()
                        showingAddWork = true
                    }
                )

                // 月份导航（仅月视图显示）
                if calendarViewType == .month {
                    MonthNavigationView(
                        selectedDate: $selectedDate,
                        onPreviousMonth: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                selectedDate = Calendar.current.date(byAdding: .month, value: -1, to: selectedDate) ?? selectedDate
                            }
                        },
                        onNextMonth: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                selectedDate = Calendar.current.date(byAdding: .month, value: 1, to: selectedDate) ?? selectedDate
                            }
                        }
                    )
                }

                // 日历内容
                calendarContentView
                    .calendarGestures(
                        selectedDate: $selectedDate,
                        calendarViewType: $calendarViewType,
                        onDateChange: { newDate in
                            CalendarHapticFeedback.dateNavigated()
                        },
                        onViewTypeChange: { newType in
                            userPreferences.calendarViewType = newType
                        }
                    )

                // 工作类型图例
                if userPreferences.showWorkTypeLegend {
                    workTypeLegendView
                }
            }
            .calendarKeyboardShortcuts(
                selectedDate: $selectedDate,
                calendarViewType: $calendarViewType,
                onAddWork: {
                    selectedDate = Date()
                    showingAddWork = true
                },
                onToday: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedDate = Date()
                    }
                }
            )
            .navigationTitle("工作日历")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button {
                        showingDatePicker = true
                    } label: {
                        Image(systemName: "calendar.badge.clock")
                            .foregroundColor(.blue)
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        selectedDate = Date()
                        showingAddWork = true
                    } label: {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                }
            }
            .sheet(isPresented: $showingAddWork) {
                AddWorkRecordView(selectedDate: selectedDate)
            }
            .sheet(isPresented: $showingWorkDetail) {
                if let workRecord = selectedWorkRecord {
                    WorkRecordDetailView(workRecord: workRecord)
                }
            }
            .sheet(isPresented: $showingDatePicker) {
                DatePickerView(selectedDate: $selectedDate)
            }
            .onAppear {
                calendarViewType = userPreferences.calendarViewType
            }
        }
    }

    // MARK: - 顶部统计卡片
    private var todayStatsCard: some View {
        VStack(spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("今日概览")
                        .font(.headline)
                        .foregroundColor(.primary)

                    Text(DateHelper.relativeDescription(for: Date()))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text(todayIncomeText)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)

                    Text(todayHoursText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // 今日工作状态
            if !todayWorkRecords.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(todayWorkRecords, id: \.id) { record in
                            TodayWorkCard(record: record) {
                                selectedWorkRecord = record
                                showingWorkDetail = true
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                }
            }
        }
        .responsiveCardPadding()
        .lightCardStyle()
        .responsivePadding()
    }

    // MARK: - 视图切换控制
    private var viewTypeSegmentedControl: some View {
        Picker("视图类型", selection: $calendarViewType) {
            Text("月").tag(CalendarViewType.month)
            Text("周").tag(CalendarViewType.week)
            Text("列表").tag(CalendarViewType.list)
        }
        .pickerStyle(SegmentedPickerStyle())
        .padding(.horizontal)
        .onChange(of: calendarViewType) { newValue in
            userPreferences.calendarViewType = newValue
        }
    }

    // MARK: - 月份导航视图
    private var monthNavigationView: some View {
        HStack {
            Button {
                withAnimation(.easeInOut(duration: 0.3)) {
                    selectedDate = Calendar.current.date(byAdding: .month, value: -1, to: selectedDate) ?? selectedDate
                }
            } label: {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
            
            Spacer()
            
            Text(monthYearString)
                .font(.title2)
                .fontWeight(.semibold)
            
            Spacer()
            
            Button {
                withAnimation(.easeInOut(duration: 0.3)) {
                    selectedDate = Calendar.current.date(byAdding: .month, value: 1, to: selectedDate) ?? selectedDate
                }
            } label: {
                Image(systemName: "chevron.right")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 10)
    }
    
    // MARK: - 日历内容视图
    private var calendarContentView: some View {
        Group {
            switch calendarViewType {
            case .month:
                monthCalendarView
            case .week:
                weekCalendarView
            case .list:
                listCalendarView
            }
        }
    }

    // MARK: - 月视图
    private var monthCalendarView: some View {
        CalendarGridContainer {
            ForEach(calendarDates, id: \.self) { date in
                EnhancedCalendarDayView(
                    date: date,
                    isCurrentMonth: Calendar.current.isDate(date, equalTo: selectedDate, toGranularity: .month),
                    isToday: DateHelper.isToday(date),
                    isSelected: Calendar.current.isDate(date, inSameDayAs: selectedDate),
                    workRecords: workRecordsForDate(date),
                    workTypes: dataManager.fetchWorkTypes(),
                    maxHoursInMonth: maxWorkHoursInCurrentMonth,
                    showEfficiencyRating: userPreferences.showEfficiencyRating,
                    showWorkStatus: userPreferences.showWorkStatus,
                    onTap: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            selectedDate = date
                        }
                    },
                    onLongPress: {
                        selectedDate = date
                        showingAddWork = true
                    },
                    onWorkRecordTap: { record in
                        selectedWorkRecord = record
                        showingWorkDetail = true
                    }
                )
            }
        }
        .padding(.horizontal)
        .gesture(
            DragGesture()
                .onEnded { value in
                    let threshold: CGFloat = 50
                    if value.translation.x > threshold {
                        // 向右滑动，显示上个月
                        withAnimation(DesignSystem.Animation.standard) {
                            selectedDate = Calendar.current.date(byAdding: .month, value: -1, to: selectedDate) ?? selectedDate
                            CalendarHapticFeedback.lightImpact()
                        }
                    } else if value.translation.x < -threshold {
                        // 向左滑动，显示下个月
                        withAnimation(DesignSystem.Animation.standard) {
                            selectedDate = Calendar.current.date(byAdding: .month, value: 1, to: selectedDate) ?? selectedDate
                            CalendarHapticFeedback.lightImpact()
                        }
                    }
                }
        )
    }

    // MARK: - 周视图
    private var weekCalendarView: some View {
        VStack(spacing: 8) {
            // 星期标题
            weekdayHeaderView

            // 周日期行
            HStack(spacing: 0) {
                ForEach(weekDates, id: \.self) { date in
                    CalendarDayView(
                        date: date,
                        isCurrentMonth: true,
                        isToday: DateHelper.isToday(date),
                        isSelected: Calendar.current.isDate(date, inSameDayAs: selectedDate),
                        workRecords: workRecordsForDate(date),
                        workTypes: dataManager.fetchWorkTypes(),
                        maxHoursInMonth: maxWorkHoursInCurrentMonth,
                        showEfficiencyRating: userPreferences.showEfficiencyRating,
                        showWorkStatus: userPreferences.showWorkStatus,
                        onTap: {
                            selectedDate = date
                        },
                        onLongPress: {
                            selectedDate = date
                            showingAddWork = true
                        },
                        onWorkRecordTap: { record in
                            selectedWorkRecord = record
                            showingWorkDetail = true
                        }
                    )
                }
            }
        }
        .padding(.horizontal)
    }

    // MARK: - 列表视图
    private var listCalendarView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(groupedWorkRecords.keys.sorted(by: >), id: \.self) { date in
                    VStack(alignment: .leading, spacing: 8) {
                        // 日期标题
                        HStack {
                            Text(DateHelper.relativeDescription(for: date))
                                .font(.headline)
                                .foregroundColor(.primary)

                            Spacer()

                            Text(DateHelper.yearMonthDayFormatter.string(from: date))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        // 工作记录列表
                        ForEach(groupedWorkRecords[date] ?? [], id: \.id) { record in
                            WorkRecordListItem(record: record) {
                                selectedWorkRecord = record
                                showingWorkDetail = true
                            }
                        }
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                }
            }
            .padding()
        }
    }
    
    // MARK: - 星期标题视图
    private var weekdayHeaderView: some View {
        HStack(spacing: 0) {
            ForEach(weekdays, id: \.self) { weekday in
                Text(weekday)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
            }
        }
        .background(DesignSystem.Colors.tertiaryBackground)
    }
    
    // MARK: - 工作类型图例
    private var workTypeLegendView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("工作类型")
                .font(.headline)
                .padding(.horizontal)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(dataManager.fetchWorkTypes(), id: \.id) { workType in
                        HStack(spacing: 6) {
                            Circle()
                                .fill(workType.themeColor)
                                .frame(width: 12, height: 12)

                            Text(workType.name)
                                .font(.caption)
                                .foregroundColor(.primary)
                        }
                        .padding(.horizontal, DesignSystem.Spacing.sm)
                        .padding(.vertical, DesignSystem.Spacing.xs)
                        .background(DesignSystem.Colors.tertiaryBackground)
                        .cornerRadius(DesignSystem.CornerRadius.sm)
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(.vertical, 8)
    }

    // MARK: - 计算属性

    private var monthYearString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月"
        return formatter.string(from: selectedDate)
    }

    private var weekdays: [String] {
        return ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
    }

    private var calendarDates: [Date] {
        return DateHelper.calendarDates(for: selectedDate)
    }

    private var weekDates: [Date] {
        let startOfWeek = DateHelper.startOfWeek(for: selectedDate)
        return (0..<7).compactMap { offset in
            Calendar.current.date(byAdding: .day, value: offset, to: startOfWeek)
        }
    }

    private var todayWorkRecords: [WorkRecord] {
        return workRecordsForDate(Date())
    }

    private var todayIncomeText: String {
        let income = SalaryCalculator.calculateTotalIncome(from: todayWorkRecords)
        return userPreferences.currencySymbol + String(format: "%.0f", income)
    }

    private var todayHoursText: String {
        let hours = SalaryCalculator.calculateTotalWorkHours(from: todayWorkRecords)
        return String(format: "%.1f小时", hours)
    }

    private var groupedWorkRecords: [Date: [WorkRecord]] {
        let recentRecords = dataManager.fetchWorkRecords(
            from: Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date(),
            to: Date()
        )

        return Dictionary(grouping: recentRecords) { record in
            DateHelper.startOfDay(for: record.date)
        }
    }

    private var maxRecordsInCurrentMonth: Int {
        let monthDates = DateHelper.datesInMonth(for: selectedDate)
        return monthDates.map { workRecordsForDate($0).count }.max() ?? 1
    }

    private var maxWorkHoursInCurrentMonth: Double {
        let monthDates = DateHelper.datesInMonth(for: selectedDate)
        let dailyHours = monthDates.map { date in
            workRecordsForDate(date).reduce(0) { $0 + $1.workHours }
        }
        return dailyHours.max() ?? 8.0 // 默认8小时作为最大值
    }

    private func workRecordsForDate(_ date: Date) -> [WorkRecord] {
        let startOfDay = DateHelper.startOfDay(for: date)
        let endOfDay = DateHelper.endOfDay(for: date)
        return dataManager.fetchWorkRecords(from: startOfDay, to: endOfDay)
    }
}

// MARK: - 日历日期视图
struct CalendarDayView: View {
    let date: Date
    let isCurrentMonth: Bool
    let isToday: Bool
    let isSelected: Bool
    let workRecords: [WorkRecord]
    let workTypes: [WorkType]
    let maxHoursInMonth: Double
    let showEfficiencyRating: Bool
    let showWorkStatus: Bool
    let onTap: () -> Void
    let onLongPress: () -> Void
    let onWorkRecordTap: (WorkRecord) -> Void

    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences
    @State private var showingWorkRecords = false

    var body: some View {
        VStack(spacing: 2) {
            // 日期数字
            Text("\(Calendar.current.component(.day, from: date))")
                .font(.system(size: 16, weight: isToday ? .bold : .medium))
                .foregroundColor(textColor)

            // 工作密度指示器和状态
            VStack(spacing: 1) {
                // 工作密度可视化指示器
                WorkDensityIndicator(
                    workRecords: workRecords,
                    workTypes: workTypes,
                    maxHoursInMonth: maxHoursInMonth
                )
                .frame(height: 16)

                // 工作类型色彩条（多类型时显示）
                if workRecords.count > 1 && userPreferences.showWorkTypeLegend {
                    HStack(spacing: 1) {
                        ForEach(Array(workTypeIndicators.enumerated()), id: \.offset) { index, color in
                            Rectangle()
                                .fill(color)
                                .frame(width: 2, height: 2)
                        }
                    }
                    .frame(height: 3)
                }

                // 效率评级或工作状态
                if showEfficiencyRating && !workRecords.isEmpty {
                    Text(efficiencyText)
                        .font(.system(size: 8))
                        .foregroundColor(efficiencyColor)
                } else if showWorkStatus && !workRecords.isEmpty {
                    Text(statusText)
                        .font(.system(size: 8))
                        .foregroundColor(statusColor)
                } else if userPreferences.showIncomePreview && !workRecords.isEmpty {
                    Text(incomePreviewText)
                        .font(.system(size: 8))
                        .foregroundColor(.green)
                }
            }
        }
        .frame(height: 60)
        .frame(maxWidth: .infinity)
        .background(backgroundColor)
        .overlay(
            RoundedRectangle(cornerRadius: 6)
                .stroke(borderColor, lineWidth: borderWidth)
        )
        .onTapGesture {
            if !workRecords.isEmpty {
                showingWorkRecords = true
            } else {
                onTap()
            }
        }
        .onLongPressGesture {
            onLongPress()
        }
        .sheet(isPresented: $showingWorkRecords) {
            DayWorkRecordsView(
                date: date,
                workRecords: workRecords,
                onWorkRecordTap: onWorkRecordTap,
                onAddWork: onLongPress
            )
        }
    }
    
    private var textColor: Color {
        if !isCurrentMonth {
            return .secondary
        } else if isSelected {
            return .white
        } else if isToday {
            return .blue
        } else {
            return .primary
        }
    }

    private var backgroundColor: Color {
        if isSelected {
            return .blue
        } else if !workRecords.isEmpty && isCurrentMonth {
            return Color.blue.opacity(0.1)
        } else if isCurrentMonth {
            return Color(.systemBackground)
        } else {
            return Color(.systemGray6)
        }
    }

    private var borderColor: Color {
        if isToday && !isSelected {
            return .blue
        } else if isSelected {
            return .blue
        } else {
            return .clear
        }
    }

    private var borderWidth: CGFloat {
        return (isToday || isSelected) ? 2 : 0
    }

    private var workTypeIndicators: [Color] {
        let workTypes = dataManager.fetchWorkTypes()
        let workTypeDict = Dictionary(uniqueKeysWithValues: workTypes.map { ($0.id, $0) })

        let uniqueWorkTypeIds = Set(workRecords.map { $0.workTypeId })
        let colors = uniqueWorkTypeIds.compactMap { id in
            workTypeDict[id]?.themeColor
        }

        return Array(colors.prefix(3)) // 最多显示3个指示器
    }

    private var efficiencyText: String {
        guard let bestRecord = workRecords.max(by: { $0.efficiencyRating.stars < $1.efficiencyRating.stars }) else {
            return ""
        }
        return String(repeating: "★", count: bestRecord.efficiencyRating.stars)
    }

    private var efficiencyColor: Color {
        guard let bestRecord = workRecords.max(by: { $0.efficiencyRating.stars < $1.efficiencyRating.stars }) else {
            return .gray
        }
        return Color(hex: bestRecord.efficiencyRating.color) ?? .gray
    }

    private var statusText: String {
        if let inProgressRecord = workRecords.first(where: { $0.workStatus == .inProgress }) {
            return "进行中"
        } else if workRecords.allSatisfy({ $0.workStatus == .completed }) {
            return "已完成"
        } else {
            return "待开始"
        }
    }

    private var statusColor: Color {
        if workRecords.contains(where: { $0.workStatus == .inProgress }) {
            return .green
        } else if workRecords.allSatisfy({ $0.workStatus == .completed }) {
            return .blue
        } else {
            return .orange
        }
    }

    private var incomePreviewText: String {
        let totalIncome = SalaryCalculator.calculateTotalIncome(from: workRecords)
        if totalIncome >= 1000 {
            return String(format: "%.1fk", totalIncome / 1000)
        } else if totalIncome >= 100 {
            return String(format: "%.0f", totalIncome)
        } else if totalIncome > 0 {
            return String(format: "%.0f", totalIncome)
        } else {
            return ""
        }
    }
}

// MARK: - 今日工作卡片
struct TodayWorkCard: View {
    let record: WorkRecord
    let onTap: () -> Void

    @EnvironmentObject private var dataManager: DataManager

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Circle()
                    .fill(workTypeColor)
                    .frame(width: 8, height: 8)

                Text(workTypeName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Spacer()

                Text(record.workStatus.displayName)
                    .font(.caption2)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color(hex: record.workStatus.color)?.opacity(0.2))
                    .foregroundColor(Color(hex: record.workStatus.color))
                    .cornerRadius(4)
            }

            Text(record.formattedTimeRange)
                .font(.caption2)
                .foregroundColor(.secondary)

            Text(record.formattedSalary)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.green)
        }
        .padding(8)
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        .onTapGesture {
            onTap()
        }
    }

    private var workTypeName: String {
        return dataManager.fetchWorkType(by: record.workTypeId)?.name ?? "未知类型"
    }

    private var workTypeColor: Color {
        return dataManager.fetchWorkType(by: record.workTypeId)?.themeColor ?? .gray
    }
}

// MARK: - 工作记录列表项
struct WorkRecordListItem: View {
    let record: WorkRecord
    let onTap: () -> Void

    @EnvironmentObject private var dataManager: DataManager

    var body: some View {
        HStack(spacing: 12) {
            // 工作类型指示器
            VStack {
                Circle()
                    .fill(workTypeColor)
                    .frame(width: 12, height: 12)

                Rectangle()
                    .fill(workTypeColor.opacity(0.3))
                    .frame(width: 2)
                    .frame(maxHeight: .infinity)
            }
            .frame(width: 12)

            // 工作信息
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(workTypeName)
                        .font(.headline)
                        .foregroundColor(.primary)

                    Spacer()

                    Text(record.formattedSalary)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                }

                Text(record.workDescription)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .lineLimit(2)

                HStack {
                    Text(record.formattedTimeRange)
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()

                    if record.efficiencyRating != .low {
                        HStack(spacing: 2) {
                            ForEach(0..<record.efficiencyRating.stars, id: \.self) { _ in
                                Image(systemName: "star.fill")
                                    .font(.caption2)
                                    .foregroundColor(.yellow)
                            }
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .onTapGesture {
            onTap()
        }
    }

    private var workTypeName: String {
        return dataManager.fetchWorkType(by: record.workTypeId)?.name ?? "未知类型"
    }

    private var workTypeColor: Color {
        return dataManager.fetchWorkType(by: record.workTypeId)?.themeColor ?? .gray
    }
}

// MARK: - 日工作记录视图
struct DayWorkRecordsView: View {
    let date: Date
    let workRecords: [WorkRecord]
    let onWorkRecordTap: (WorkRecord) -> Void
    let onAddWork: () -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 日期标题
                VStack(spacing: 8) {
                    Text(DateHelper.relativeDescription(for: date))
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(DateHelper.yearMonthDayFormatter.string(from: date))
                        .font(.caption)
                        .foregroundColor(.secondary)

                    // 统计信息
                    HStack(spacing: 20) {
                        VStack {
                            Text("\(workRecords.count)")
                                .font(.title3)
                                .fontWeight(.bold)
                            Text("工作记录")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        VStack {
                            Text(totalIncomeText)
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundColor(.green)
                            Text("总收入")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        VStack {
                            Text(totalHoursText)
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundColor(.blue)
                            Text("总工时")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))

                // 工作记录列表
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(workRecords.sorted(by: { $0.startTime < $1.startTime }), id: \.id) { record in
                            WorkRecordListItem(record: record) {
                                onWorkRecordTap(record)
                                dismiss()
                            }
                        }
                    }
                    .padding()
                }
            }
            .navigationTitle("工作详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        onAddWork()
                        dismiss()
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
        }
    }

    private var totalIncomeText: String {
        let income = SalaryCalculator.calculateTotalIncome(from: workRecords)
        return String(format: "¥%.0f", income)
    }

    private var totalHoursText: String {
        let hours = SalaryCalculator.calculateTotalWorkHours(from: workRecords)
        return String(format: "%.1f小时", hours)
    }
}

// MARK: - 日期选择器视图
struct DatePickerView: View {
    @Binding var selectedDate: Date
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                DatePicker(
                    "选择日期",
                    selection: $selectedDate,
                    displayedComponents: [.date]
                )
                .datePickerStyle(GraphicalDatePickerStyle())
                .padding()

                Spacer()
            }
            .navigationTitle("选择日期")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确定") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - 占位视图已移除，功能已实现

struct StatisticsView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("统计分析")
                    .font(.title)
                
                Spacer()
                
                Text("此功能将在后续阶段实现")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .navigationTitle("统计分析")
        }
    }
}

struct ProfileView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("个人设置")
                    .font(.title)
                
                Spacer()
                
                Text("此功能将在后续阶段实现")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .navigationTitle("个人设置")
        }
    }
}

// MARK: - 增强版日历日期视图
struct EnhancedCalendarDayView: View {
    let date: Date
    let isCurrentMonth: Bool
    let isToday: Bool
    let isSelected: Bool
    let workRecords: [WorkRecord]
    let workTypes: [WorkType]
    let maxHoursInMonth: Double
    let showEfficiencyRating: Bool
    let showWorkStatus: Bool
    let onTap: () -> Void
    let onLongPress: () -> Void
    let onWorkRecordTap: (WorkRecord) -> Void

    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences
    @State private var showingWorkRecords = false
    @State private var showingQuickActions = false

    var body: some View {
        VStack(spacing: 2) {
            // 日期数字
            Text("\(Calendar.current.component(.day, from: date))")
                .font(.system(size: 16, weight: isToday ? .bold : .medium))
                .foregroundColor(textColor)

            // 工作密度和状态指示器
            VStack(spacing: 1) {
                // 工作类型指示器
                if !workRecords.isEmpty {
                    HStack(spacing: 1) {
                        ForEach(Array(workTypeIndicators.enumerated()), id: \.offset) { index, color in
                            Circle()
                                .fill(color)
                                .frame(width: 4, height: 4)
                        }

                        // 工作密度指示器
                        if workRecords.count > 3 {
                            WorkDensityIndicator(
                                workRecords: workRecords,
                                workTypes: workTypes,
                                maxHoursInMonth: maxHoursInMonth
                            )
                        }
                    }
                    .frame(height: 6)
                } else {
                    Spacer()
                        .frame(height: 6)
                }

                // 效率评级或工作状态
                if showEfficiencyRating && !workRecords.isEmpty {
                    Text(efficiencyText)
                        .font(.system(size: 8))
                        .foregroundColor(efficiencyColor)
                } else if showWorkStatus && !workRecords.isEmpty {
                    Text(statusText)
                        .font(.system(size: 8))
                        .foregroundColor(statusColor)
                }

                // 收入预览
                if userPreferences.showIncomePreview && !workRecords.isEmpty {
                    Text(incomeText)
                        .font(.system(size: 7))
                        .foregroundColor(.green)
                        .lineLimit(1)
                }
            }
        }
        .frame(height: 70)
        .frame(maxWidth: .infinity)
        .background(backgroundColor)
        .overlay(
            RoundedRectangle(cornerRadius: 6)
                .stroke(borderColor, lineWidth: borderWidth)
        )
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
        .onTapGesture {
            if !workRecords.isEmpty && isCurrentMonth {
                showingWorkRecords = true
            } else {
                onTap()
            }
        }
        .onLongPressGesture {
            if isCurrentMonth {
                showingQuickActions = true
            }
        }
        .sheet(isPresented: $showingWorkRecords) {
            DayWorkRecordsView(
                date: date,
                workRecords: workRecords,
                onWorkRecordTap: onWorkRecordTap,
                onAddWork: onLongPress
            )
        }
        .sheet(isPresented: $showingQuickActions) {
            QuickActionSheet(
                date: date,
                onAddWork: {
                    showingQuickActions = false
                    onLongPress()
                },
                onViewRecords: {
                    showingQuickActions = false
                    if !workRecords.isEmpty {
                        showingWorkRecords = true
                    }
                },
                onCopyPrevious: {
                    showingQuickActions = false
                    copyPreviousDayWork()
                }
            )
        }
    }

    // MARK: - 计算属性

    private var textColor: Color {
        if !isCurrentMonth {
            return .secondary
        } else if isSelected {
            return .white
        } else if isToday {
            return .blue
        } else {
            return .primary
        }
    }

    private var backgroundColor: Color {
        if isSelected {
            return .blue
        } else if !workRecords.isEmpty && isCurrentMonth {
            return Color.blue.opacity(0.1)
        } else if isCurrentMonth {
            return Color(.systemBackground)
        } else {
            return Color(.systemGray6)
        }
    }

    private var borderColor: Color {
        if isToday && !isSelected {
            return .blue
        } else if isSelected {
            return .blue
        } else {
            return .clear
        }
    }

    private var borderWidth: CGFloat {
        return (isToday || isSelected) ? 2 : 0
    }

    private var workTypeIndicators: [Color] {
        let workTypes = dataManager.fetchWorkTypes()
        let workTypeDict = Dictionary(uniqueKeysWithValues: workTypes.map { ($0.id, $0) })

        let uniqueWorkTypeIds = Set(workRecords.map { $0.workTypeId })
        let colors = uniqueWorkTypeIds.compactMap { id in
            workTypeDict[id]?.themeColor
        }

        return Array(colors.prefix(3)) // 最多显示3个指示器
    }

    private var efficiencyText: String {
        guard let bestRecord = workRecords.max(by: { $0.efficiencyRating.stars < $1.efficiencyRating.stars }) else {
            return ""
        }
        return String(repeating: "★", count: bestRecord.efficiencyRating.stars)
    }

    private var efficiencyColor: Color {
        guard let bestRecord = workRecords.max(by: { $0.efficiencyRating.stars < $1.efficiencyRating.stars }) else {
            return .gray
        }
        return Color(hex: bestRecord.efficiencyRating.color) ?? .gray
    }

    private var statusText: String {
        if let inProgressRecord = workRecords.first(where: { $0.workStatus == .inProgress }) {
            return "进行中"
        } else if workRecords.allSatisfy({ $0.workStatus == .completed }) {
            return "已完成"
        } else {
            return "待开始"
        }
    }

    private var statusColor: Color {
        if workRecords.contains(where: { $0.workStatus == .inProgress }) {
            return .green
        } else if workRecords.allSatisfy({ $0.workStatus == .completed }) {
            return .blue
        } else {
            return .orange
        }
    }

    private var incomeText: String {
        let totalIncome = SalaryCalculator.calculateTotalIncome(from: workRecords)
        if totalIncome > 0 {
            return userPreferences.currencySymbol + String(format: "%.0f", totalIncome)
        }
        return ""
    }

    // MARK: - 方法

    private func copyPreviousDayWork() {
        // 这里实现复制前一天工作安排的逻辑
        // 暂时只是一个占位实现
        print("复制前一天的工作安排到 \(DateHelper.yearMonthDayFormatter.string(from: date))")
    }
}

// MARK: - 快速操作表单
struct QuickActionSheet: View {
    let date: Date
    let onAddWork: () -> Void
    let onViewRecords: () -> Void
    let onCopyPrevious: () -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        VStack(spacing: 0) {
            // 拖拽指示器
            RoundedRectangle(cornerRadius: 2)
                .fill(Color(.systemGray4))
                .frame(width: 40, height: 4)
                .padding(.top, 8)

            // 日期标题
            VStack(spacing: 8) {
                Text(DateHelper.relativeDescription(for: date))
                    .font(.title2)
                    .fontWeight(.bold)

                Text(DateHelper.yearMonthDayFormatter.string(from: date))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.top, 16)

            // 快速操作菜单
            QuickActionMenu(
                date: date,
                onAddWork: onAddWork,
                onViewRecords: onViewRecords,
                onCopyPrevious: onCopyPrevious
            )
            .padding()

            Spacer()
        }
        .presentationDetents([.height(300)])
        .presentationDragIndicator(.hidden)
    }
}

#Preview {
    HomeView()
        .environmentObject(DataManager.shared)
        .environmentObject(UserPreferences.shared)
}
