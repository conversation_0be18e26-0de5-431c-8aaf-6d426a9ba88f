//
//  TimePickerView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 时间选择器视图
struct TimePickerView: View {
    @Binding var activeField: TimeField
    @Binding var startTime: Date
    @Binding var endTime: Date
    let workDate: Date
    
    @Environment(\.dismiss) private var dismiss
    @State private var tempStartTime: Date = Date()
    @State private var tempEndTime: Date = Date()
    @State private var showingPresets = true
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 时间段选择器
                timeFieldSelector
                
                // 预设时间段
                if showingPresets {
                    presetTimeSlotsSection
                }
                
                // 自定义时间选择器
                customTimePickerSection
                
                // 工作时长显示
                workDurationSection
                
                Spacer()
            }
            .navigationTitle("设置工作时间")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确定") {
                        saveTimeChanges()
                        dismiss()
                    }
                    .disabled(!isTimeValid)
                }
            }
            .onAppear {
                setupInitialTimes()
            }
        }
    }
    
    // MARK: - 时间段选择器
    private var timeFieldSelector: some View {
        Picker("时间字段", selection: $activeField) {
            Text("开始时间").tag(TimeField.start)
            Text("结束时间").tag(TimeField.end)
        }
        .pickerStyle(SegmentedPickerStyle())
        .padding()
    }
    
    // MARK: - 预设时间段
    private var presetTimeSlotsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("常用时间段")
                    .font(.headline)
                
                Spacer()
                
                Button {
                    withAnimation {
                        showingPresets.toggle()
                    }
                } label: {
                    Image(systemName: showingPresets ? "chevron.up" : "chevron.down")
                        .foregroundColor(.blue)
                }
            }
            .padding(.horizontal)
            
            if showingPresets {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(commonTimeSlots, id: \.name) { timeSlot in
                            PresetTimeSlotCard(
                                timeSlot: timeSlot,
                                onTap: {
                                    applyTimeSlot(timeSlot)
                                }
                            )
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
    }
    
    // MARK: - 自定义时间选择器
    private var customTimePickerSection: some View {
        VStack(spacing: 16) {
            Text("自定义时间")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal)
            
            VStack(spacing: 20) {
                // 开始时间
                VStack(alignment: .leading, spacing: 8) {
                    Text("开始时间")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    DatePicker(
                        "",
                        selection: $tempStartTime,
                        displayedComponents: [.hourAndMinute]
                    )
                    .datePickerStyle(WheelDatePickerStyle())
                    .labelsHidden()
                    .opacity(activeField == .start ? 1.0 : 0.5)
                    .disabled(activeField != .start)
                }
                
                // 结束时间
                VStack(alignment: .leading, spacing: 8) {
                    Text("结束时间")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    DatePicker(
                        "",
                        selection: $tempEndTime,
                        displayedComponents: [.hourAndMinute]
                    )
                    .datePickerStyle(WheelDatePickerStyle())
                    .labelsHidden()
                    .opacity(activeField == .end ? 1.0 : 0.5)
                    .disabled(activeField != .end)
                }
            }
            .padding(.horizontal)
        }
    }
    
    // MARK: - 工作时长显示
    private var workDurationSection: some View {
        VStack(spacing: 8) {
            Divider()
            
            HStack {
                Label("工作时长", systemImage: "timer")
                    .font(.headline)
                
                Spacer()
                
                Text(formattedDuration)
                    .font(.headline)
                    .foregroundColor(isTimeValid ? .green : .red)
                    .fontWeight(.semibold)
            }
            .padding()
            
            if !isTimeValid {
                Text("结束时间必须晚于开始时间")
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.horizontal)
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var commonTimeSlots: [(name: String, start: Date, end: Date)] {
        return DateHelper.getCommonTimeSlots().map { slot in
            let calendar = Calendar.current
            let startTime = calendar.date(
                bySettingHour: calendar.component(.hour, from: slot.start),
                minute: calendar.component(.minute, from: slot.start),
                second: 0,
                of: workDate
            ) ?? slot.start
            
            let endTime = calendar.date(
                bySettingHour: calendar.component(.hour, from: slot.end),
                minute: calendar.component(.minute, from: slot.end),
                second: 0,
                of: workDate
            ) ?? slot.end
            
            return (slot.name, startTime, endTime)
        }
    }
    
    private var isTimeValid: Bool {
        return tempStartTime < tempEndTime
    }
    
    private var formattedDuration: String {
        let duration = tempEndTime.timeIntervalSince(tempStartTime)
        return DateHelper.workDurationDescription(duration)
    }
    
    // MARK: - 方法
    
    private func setupInitialTimes() {
        tempStartTime = startTime
        tempEndTime = endTime
    }
    
    private func applyTimeSlot(_ timeSlot: (name: String, start: Date, end: Date)) {
        withAnimation(.easeInOut(duration: 0.3)) {
            tempStartTime = timeSlot.start
            tempEndTime = timeSlot.end
        }
    }
    
    private func saveTimeChanges() {
        startTime = tempStartTime
        endTime = tempEndTime
    }
}

// MARK: - 预设时间段卡片
struct PresetTimeSlotCard: View {
    let timeSlot: (name: String, start: Date, end: Date)
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Text(timeSlot.name)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                VStack(spacing: 4) {
                    Text(DateHelper.hourMinuteFormatter.string(from: timeSlot.start))
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("至")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Text(DateHelper.hourMinuteFormatter.string(from: timeSlot.end))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Text(formattedDuration)
                    .font(.caption)
                    .foregroundColor(.blue)
                    .fontWeight(.semibold)
            }
            .padding()
            .frame(width: 100, height: 120)
            .background(Color(.systemGray6))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(.systemGray4), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var formattedDuration: String {
        let duration = timeSlot.end.timeIntervalSince(timeSlot.start)
        return DateHelper.workDurationDescription(duration)
    }
}

// MARK: - 智能时间建议
struct SmartTimeSuggestions: View {
    let workDate: Date
    let onSuggestionTap: (Date, Date) -> Void
    
    @EnvironmentObject private var dataManager: DataManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("智能建议")
                .font(.headline)
                .padding(.horizontal)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(suggestions, id: \.0) { suggestion in
                        SuggestionCard(
                            title: suggestion.0,
                            startTime: suggestion.1,
                            endTime: suggestion.2,
                            onTap: {
                                onSuggestionTap(suggestion.1, suggestion.2)
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    private var suggestions: [(String, Date, Date)] {
        var suggestions: [(String, Date, Date)] = []
        let calendar = Calendar.current
        
        // 基于历史数据的建议
        let recentRecords = dataManager.fetchRecentWorkRecords(limit: 10)
        if let mostCommonTime = findMostCommonWorkTime(from: recentRecords) {
            suggestions.append(("常用时间", mostCommonTime.start, mostCommonTime.end))
        }
        
        // 基于昨天的工作时间
        if let yesterday = calendar.date(byAdding: .day, value: -1, to: workDate) {
            let yesterdayRecords = dataManager.fetchWorkRecords(for: yesterday)
            if let lastRecord = yesterdayRecords.first {
                let startTime = calendar.date(
                    bySettingHour: calendar.component(.hour, from: lastRecord.startTime),
                    minute: calendar.component(.minute, from: lastRecord.startTime),
                    second: 0,
                    of: workDate
                ) ?? lastRecord.startTime
                
                let endTime = calendar.date(
                    bySettingHour: calendar.component(.hour, from: lastRecord.endTime),
                    minute: calendar.component(.minute, from: lastRecord.endTime),
                    second: 0,
                    of: workDate
                ) ?? lastRecord.endTime
                
                suggestions.append(("昨天时间", startTime, endTime))
            }
        }
        
        return suggestions
    }
    
    private func findMostCommonWorkTime(from records: [WorkRecord]) -> (start: Date, end: Date)? {
        // 简化实现：返回最近一次的工作时间
        guard let lastRecord = records.first else { return nil }
        
        let calendar = Calendar.current
        let startTime = calendar.date(
            bySettingHour: calendar.component(.hour, from: lastRecord.startTime),
            minute: calendar.component(.minute, from: lastRecord.startTime),
            second: 0,
            of: workDate
        ) ?? lastRecord.startTime
        
        let endTime = calendar.date(
            bySettingHour: calendar.component(.hour, from: lastRecord.endTime),
            minute: calendar.component(.minute, from: lastRecord.endTime),
            second: 0,
            of: workDate
        ) ?? lastRecord.endTime
        
        return (startTime, endTime)
    }
}

// MARK: - 建议卡片
struct SuggestionCard: View {
    let title: String
    let startTime: Date
    let endTime: Date
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 6) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.blue)
                    .fontWeight(.semibold)
                
                Text("\(DateHelper.hourMinuteFormatter.string(from: startTime)) - \(DateHelper.hourMinuteFormatter.string(from: endTime))")
                    .font(.caption)
                    .foregroundColor(.primary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color.blue.opacity(0.1))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    TimePickerView(
        activeField: .constant(.start),
        startTime: .constant(Date()),
        endTime: .constant(Calendar.current.date(byAdding: .hour, value: 1, to: Date()) ?? Date()),
        workDate: Date()
    )
}
