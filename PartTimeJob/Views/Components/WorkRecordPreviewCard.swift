//
//  WorkRecordPreviewCard.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 工作记录预览卡片
struct WorkRecordPreviewCard: View {
    let workType: WorkType?
    let workDescription: String
    let startTime: Date
    let endTime: Date
    let salaryType: SalaryType
    let salary: Double
    let estimatedSalary: Double
    let workHours: Double
    
    @EnvironmentObject private var userPreferences: UserPreferences
    
    var body: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Image(systemName: "eye")
                    .foregroundColor(.blue)
                
                Text("预览")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            if let workType = workType, !workDescription.isEmpty {
                // 预览内容
                previewContent
            } else {
                // 空状态
                emptyState
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 预览内容
    private var previewContent: some View {
        VStack(spacing: 12) {
            // 工作类型和描述
            HStack(alignment: .top, spacing: 12) {
                if let workType = workType {
                    Image(systemName: workType.iconName)
                        .font(.title2)
                        .foregroundColor(workType.themeColor)
                        .frame(width: 40, height: 40)
                        .background(workType.themeColor.opacity(0.1))
                        .cornerRadius(8)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(workType?.name ?? "")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(workDescription)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .lineLimit(3)
                }
                
                Spacer()
            }
            
            Divider()
            
            // 时间和薪资信息
            VStack(spacing: 8) {
                InfoRow(
                    icon: "clock",
                    title: "工作时间",
                    value: timeRangeText,
                    color: .blue
                )
                
                InfoRow(
                    icon: "timer",
                    title: "工作时长",
                    value: durationText,
                    color: .orange
                )
                
                InfoRow(
                    icon: "dollarsign.circle",
                    title: salaryType == .hourly ? "时薪" : (salaryType == .daily ? "日薪" : "项目薪资"),
                    value: baseSalaryText,
                    color: .green
                )
                
                if salaryType == .hourly {
                    InfoRow(
                        icon: "banknote",
                        title: "预计收入",
                        value: estimatedSalaryText,
                        color: .green,
                        isHighlighted: true
                    )
                }
            }
            
            // 效率评级预览
            if workHours > 0 {
                efficiencyPreview
            }
        }
    }
    
    // MARK: - 空状态
    private var emptyState: some View {
        VStack(spacing: 12) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 40))
                .foregroundColor(.gray)
            
            Text("填写信息后可预览工作记录")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 20)
    }
    
    // MARK: - 效率评级预览
    private var efficiencyPreview: some View {
        VStack(spacing: 8) {
            Divider()
            
            HStack {
                Image(systemName: "star.fill")
                    .foregroundColor(.yellow)
                
                Text("效率评级")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                HStack(spacing: 2) {
                    ForEach(0..<efficiencyRating.stars, id: \.self) { _ in
                        Image(systemName: "star.fill")
                            .font(.caption)
                            .foregroundColor(.yellow)
                    }
                    
                    ForEach(efficiencyRating.stars..<4, id: \.self) { _ in
                        Image(systemName: "star")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
                
                Text(efficiencyRating.displayName)
                    .font(.caption)
                    .foregroundColor(Color(hex: efficiencyRating.color))
                    .fontWeight(.medium)
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var timeRangeText: String {
        return "\(DateHelper.hourMinuteFormatter.string(from: startTime)) - \(DateHelper.hourMinuteFormatter.string(from: endTime))"
    }
    
    private var durationText: String {
        return DateHelper.workDurationDescription(workHours * 3600)
    }
    
    private var baseSalaryText: String {
        return userPreferences.currencySymbol + String(format: "%.2f", salary)
    }
    
    private var estimatedSalaryText: String {
        return userPreferences.currencySymbol + String(format: "%.2f", estimatedSalary)
    }
    
    private var efficiencyRating: EfficiencyRating {
        let hourlyRate = workHours > 0 ? estimatedSalary / workHours : 0
        
        switch hourlyRate {
        case 0..<20:
            return .low
        case 20..<50:
            return .medium
        case 50..<100:
            return .high
        default:
            return .excellent
        }
    }
}

// MARK: - 信息行组件
struct InfoRow: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    var isHighlighted: Bool = false
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(isHighlighted ? .headline : .subheadline)
                .fontWeight(isHighlighted ? .bold : .medium)
                .foregroundColor(isHighlighted ? color : .primary)
        }
    }
}

// MARK: - 工作记录摘要卡片
struct WorkRecordSummaryCard: View {
    let workType: WorkType?
    let workDescription: String
    let timeRange: String
    let estimatedSalary: Double
    let workHours: Double
    
    @EnvironmentObject private var userPreferences: UserPreferences
    
    var body: some View {
        HStack(spacing: 12) {
            // 工作类型图标
            if let workType = workType {
                Image(systemName: workType.iconName)
                    .font(.title3)
                    .foregroundColor(workType.themeColor)
                    .frame(width: 32, height: 32)
                    .background(workType.themeColor.opacity(0.1))
                    .cornerRadius(6)
            }
            
            // 工作信息
            VStack(alignment: .leading, spacing: 2) {
                Text(workType?.name ?? "未选择工作类型")
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Text(workDescription.isEmpty ? "请输入工作内容" : workDescription)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            // 收入和时长
            VStack(alignment: .trailing, spacing: 2) {
                Text(userPreferences.currencySymbol + String(format: "%.0f", estimatedSalary))
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
                
                Text(String(format: "%.1f小时", workHours))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - 快速填写建议
struct QuickFillSuggestions: View {
    let workType: WorkType?
    let onSuggestionTap: (String) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("快速填写")
                .font(.caption)
                .foregroundColor(.secondary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(suggestions, id: \.self) { suggestion in
                        Button(suggestion) {
                            onSuggestionTap(suggestion)
                        }
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color(.systemGray5))
                        .foregroundColor(.primary)
                        .cornerRadius(6)
                    }
                }
            }
        }
    }
    
    private var suggestions: [String] {
        guard let workType = workType else {
            return ["日常工作", "临时任务", "加班工作"]
        }
        
        switch workType.name {
        case "兼职直播":
            return ["直播带货", "才艺表演", "聊天互动", "游戏直播"]
        case "超市发传单":
            return ["商场发传单", "街头宣传", "活动推广", "产品介绍"]
        case "家教辅导":
            return ["数学辅导", "英语教学", "作业指导", "考试复习"]
        case "外卖配送":
            return ["午餐配送", "晚餐配送", "夜宵配送", "特殊配送"]
        default:
            return ["日常工作", "项目任务", "临时工作", "加班任务"]
        }
    }
}

#Preview {
    VStack {
        WorkRecordPreviewCard(
            workType: WorkType.createDefaultWorkTypes().first,
            workDescription: "直播带货，销售护肤品",
            startTime: Date(),
            endTime: Calendar.current.date(byAdding: .hour, value: 3, to: Date()) ?? Date(),
            salaryType: .hourly,
            salary: 50,
            estimatedSalary: 150,
            workHours: 3
        )
        
        WorkRecordSummaryCard(
            workType: WorkType.createDefaultWorkTypes().first,
            workDescription: "直播带货，销售护肤品",
            timeRange: "09:00 - 12:00",
            estimatedSalary: 150,
            workHours: 3
        )
    }
    .padding()
    .environmentObject(UserPreferences.shared)
}
