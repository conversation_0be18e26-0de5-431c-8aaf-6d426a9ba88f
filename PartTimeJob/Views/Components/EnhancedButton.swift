//
//  EnhancedButton.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 增强按钮组件 - 统一的按钮样式和交互
struct EnhancedButton: View {
    
    // MARK: - 按钮样式枚举
    
    enum Style {
        case primary
        case secondary
        case tertiary
        case destructive
        case success
        case warning
        
        var backgroundColor: Color {
            switch self {
            case .primary:
                return DesignSystem.Colors.primary
            case .secondary:
                return DesignSystem.Colors.primaryLight
            case .tertiary:
                return Color.clear
            case .destructive:
                return DesignSystem.Colors.error
            case .success:
                return DesignSystem.Colors.success
            case .warning:
                return DesignSystem.Colors.warning
            }
        }
        
        var foregroundColor: Color {
            switch self {
            case .primary, .destructive, .success, .warning:
                return .white
            case .secondary:
                return DesignSystem.Colors.primary
            case .tertiary:
                return DesignSystem.Colors.primary
            }
        }
        
        var borderColor: Color {
            switch self {
            case .tertiary:
                return DesignSystem.Colors.primary
            default:
                return Color.clear
            }
        }
    }
    
    // MARK: - 按钮尺寸枚举
    
    enum Size {
        case small
        case medium
        case large
        
        var height: CGFloat {
            switch self {
            case .small:
                return ResponsiveLayout.Size.buttonHeight * 0.75
            case .medium:
                return ResponsiveLayout.Size.buttonHeight
            case .large:
                return ResponsiveLayout.Size.buttonHeight * 1.25
            }
        }
        
        var font: Font {
            switch self {
            case .small:
                return ResponsiveLayout.Typography.caption
            case .medium:
                return ResponsiveLayout.Typography.body
            case .large:
                return ResponsiveLayout.Typography.headline
            }
        }
        
        var padding: CGFloat {
            switch self {
            case .small:
                return DesignSystem.Spacing.sm
            case .medium:
                return DesignSystem.Spacing.md
            case .large:
                return DesignSystem.Spacing.lg
            }
        }
    }
    
    // MARK: - 属性
    
    let title: String
    let icon: String?
    let style: Style
    let size: Size
    let isLoading: Bool
    let isDisabled: Bool
    let action: () -> Void
    
    @State private var isPressed = false
    
    // MARK: - 初始化
    
    init(
        _ title: String,
        icon: String? = nil,
        style: Style = .primary,
        size: Size = .medium,
        isLoading: Bool = false,
        isDisabled: Bool = false,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.style = style
        self.size = size
        self.isLoading = isLoading
        self.isDisabled = isDisabled
        self.action = action
    }
    
    // MARK: - 视图
    
    var body: some View {
        Button(action: {
            if !isDisabled && !isLoading {
                CalendarHapticFeedback.lightImpact()
                action()
            }
        }) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .foregroundColor(style.foregroundColor)
                } else if let icon = icon {
                    Image(systemName: icon)
                        .font(size.font)
                }
                
                Text(title)
                    .font(size.font.weight(.medium))
                    .lineLimit(1)
            }
            .foregroundColor(effectiveForegroundColor)
            .frame(height: size.height)
            .frame(maxWidth: .infinity)
            .padding(.horizontal, size.padding)
            .background(effectiveBackgroundColor)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.button)
                    .stroke(style.borderColor, lineWidth: style == .tertiary ? 1 : 0)
            )
            .cornerRadius(DesignSystem.CornerRadius.button)
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .opacity(effectiveOpacity)
            .animation(DesignSystem.Animation.quick, value: isPressed)
            .animation(DesignSystem.Animation.quick, value: isDisabled)
            .animation(DesignSystem.Animation.quick, value: isLoading)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isDisabled || isLoading)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
    
    // MARK: - 计算属性
    
    private var effectiveBackgroundColor: Color {
        if isDisabled {
            return DesignSystem.Colors.secondary.opacity(0.3)
        }
        return style.backgroundColor
    }
    
    private var effectiveForegroundColor: Color {
        if isDisabled {
            return DesignSystem.Colors.secondaryText
        }
        return style.foregroundColor
    }
    
    private var effectiveOpacity: Double {
        if isDisabled {
            return 0.6
        }
        return 1.0
    }
}

// MARK: - 便捷初始化方法

extension EnhancedButton {
    
    static func primary(
        _ title: String,
        icon: String? = nil,
        size: Size = .medium,
        isLoading: Bool = false,
        isDisabled: Bool = false,
        action: @escaping () -> Void
    ) -> EnhancedButton {
        EnhancedButton(
            title,
            icon: icon,
            style: .primary,
            size: size,
            isLoading: isLoading,
            isDisabled: isDisabled,
            action: action
        )
    }
    
    static func secondary(
        _ title: String,
        icon: String? = nil,
        size: Size = .medium,
        isLoading: Bool = false,
        isDisabled: Bool = false,
        action: @escaping () -> Void
    ) -> EnhancedButton {
        EnhancedButton(
            title,
            icon: icon,
            style: .secondary,
            size: size,
            isLoading: isLoading,
            isDisabled: isDisabled,
            action: action
        )
    }
    
    static func destructive(
        _ title: String,
        icon: String? = nil,
        size: Size = .medium,
        isLoading: Bool = false,
        isDisabled: Bool = false,
        action: @escaping () -> Void
    ) -> EnhancedButton {
        EnhancedButton(
            title,
            icon: icon,
            style: .destructive,
            size: size,
            isLoading: isLoading,
            isDisabled: isDisabled,
            action: action
        )
    }
}

// MARK: - 浮动操作按钮

struct FloatingActionButton: View {
    let icon: String
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            CalendarHapticFeedback.mediumImpact()
            action()
        }) {
            Image(systemName: icon)
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(width: 56, height: 56)
                .background(DesignSystem.Colors.primary)
                .clipShape(Circle())
                .shadow(
                    color: DesignSystem.Shadow.floatingButton.color,
                    radius: DesignSystem.Shadow.floatingButton.radius,
                    x: DesignSystem.Shadow.floatingButton.x,
                    y: DesignSystem.Shadow.floatingButton.y
                )
                .scaleEffect(isPressed ? 0.9 : 1.0)
                .animation(DesignSystem.Animation.bouncy, value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - 图标按钮

struct IconButton: View {
    let icon: String
    let size: CGFloat
    let color: Color
    let backgroundColor: Color?
    let action: () -> Void
    
    @State private var isPressed = false
    
    init(
        icon: String,
        size: CGFloat = 24,
        color: Color = DesignSystem.Colors.primary,
        backgroundColor: Color? = nil,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.size = size
        self.color = color
        self.backgroundColor = backgroundColor
        self.action = action
    }
    
    var body: some View {
        Button(action: {
            CalendarHapticFeedback.lightImpact()
            action()
        }) {
            Image(systemName: icon)
                .font(.system(size: size))
                .foregroundColor(color)
                .frame(width: size + 16, height: size + 16)
                .background(backgroundColor ?? Color.clear)
                .clipShape(Circle())
                .scaleEffect(isPressed ? 0.8 : 1.0)
                .animation(DesignSystem.Animation.quick, value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - 预览

#Preview {
    VStack(spacing: 20) {
        EnhancedButton.primary("主要按钮", icon: "plus") { }
        
        EnhancedButton.secondary("次要按钮", icon: "gear") { }
        
        EnhancedButton("加载中", style: .primary, isLoading: true) { }
        
        EnhancedButton("禁用按钮", style: .primary, isDisabled: true) { }
        
        HStack {
            FloatingActionButton(icon: "plus") { }
            
            IconButton(icon: "heart", color: .red) { }
            
            IconButton(icon: "star", backgroundColor: .yellow.opacity(0.2)) { }
        }
    }
    .padding()
}
