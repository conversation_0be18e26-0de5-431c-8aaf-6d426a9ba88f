//
//  StatisticsComponents.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

// MARK: - 统计卡片
struct StatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    let icon: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(subtitle)
                    .font(.caption2)
                    .foregroundColor(color)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - 工作类型收入行
struct WorkTypeIncomeRow: View {
    let workType: WorkType
    let income: Double
    let percentage: Double
    let recordCount: Int
    
    @EnvironmentObject private var userPreferences: UserPreferences
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                // 工作类型信息
                HStack(spacing: 12) {
                    Circle()
                        .fill(workType.themeColor)
                        .frame(width: 12, height: 12)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text(workType.name)
                            .font(.body)
                            .fontWeight(.medium)
                        
                        Text("\(recordCount)次工作")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 收入信息
                VStack(alignment: .trailing, spacing: 2) {
                    Text(formattedIncome)
                        .font(.body)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(formattedPercentage)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(height: 4)
                        .cornerRadius(2)
                    
                    Rectangle()
                        .fill(workType.themeColor)
                        .frame(width: geometry.size.width * (percentage / 100), height: 4)
                        .cornerRadius(2)
                        .animation(.easeInOut(duration: 0.5), value: percentage)
                }
            }
            .frame(height: 4)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private var formattedIncome: String {
        return userPreferences.currencySymbol + String(format: "%.0f", income)
    }
    
    private var formattedPercentage: String {
        return String(format: "%.1f%%", percentage)
    }
}

// MARK: - 效率卡片
struct EfficiencyCard: View {
    let title: String
    let value: String
    let description: String
    let icon: String
    
    var body: some View {
        HStack(spacing: 16) {
            // 图标
            Image(systemName: icon)
                .font(.title)
                .foregroundColor(.blue)
                .frame(width: 40, height: 40)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            
            // 内容
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.blue)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - 趋势指示器
struct TrendIndicator: View {
    let trend: TrendDirection
    let value: Double
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: trendIcon)
                .font(.caption)
                .foregroundColor(trendColor)
            
            Text(formattedValue)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(trendColor)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(trendColor.opacity(0.1))
        .cornerRadius(8)
    }
    
    private var trendIcon: String {
        switch trend {
        case .increasing:
            return "arrow.up.right"
        case .decreasing:
            return "arrow.down.right"
        case .stable:
            return "arrow.right"
        }
    }
    
    private var trendColor: Color {
        return Color(hex: trend.color) ?? .gray
    }
    
    private var formattedValue: String {
        let prefix = trend == .decreasing ? "-" : "+"
        return "\(prefix)\(String(format: "%.1f", abs(value)))%"
    }
}

// MARK: - 统计摘要卡片
struct StatsSummaryCard: View {
    let title: String
    let stats: [(String, String, Color)]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .foregroundColor(.primary)
            
            VStack(spacing: 8) {
                ForEach(Array(stats.enumerated()), id: \.offset) { index, stat in
                    HStack {
                        Text(stat.0)
                            .font(.body)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Text(stat.1)
                            .font(.body)
                            .fontWeight(.semibold)
                            .foregroundColor(stat.2)
                    }
                    
                    if index < stats.count - 1 {
                        Divider()
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - 空状态视图
struct EmptyStatsView: View {
    let message: String
    let actionTitle: String
    let action: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "chart.bar.doc.horizontal")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text("暂无统计数据")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(message)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: action) {
                Text(actionTitle)
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(12)
            }
        }
        .padding(40)
    }
}

// MARK: - 加载视图
struct StatsLoadingView: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("正在加载统计数据...")
                .font(.body)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - 图表占位视图
struct ChartPlaceholderView: View {
    let title: String
    let height: CGFloat
    
    var body: some View {
        VStack(spacing: 12) {
            Text(title)
                .font(.headline)
                .foregroundColor(.primary)
            
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
                .frame(height: height)
                .overlay(
                    VStack(spacing: 8) {
                        Image(systemName: "chart.line.uptrend.xyaxis")
                            .font(.system(size: 40))
                            .foregroundColor(.gray)
                        
                        Text("图表功能")
                            .font(.headline)
                            .foregroundColor(.secondary)
                        
                        Text("将在后续阶段实现")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                )
        }
    }
}
