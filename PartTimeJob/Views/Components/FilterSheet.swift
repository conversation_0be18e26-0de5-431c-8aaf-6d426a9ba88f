//
//  FilterSheet.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 筛选器表单
struct FilterSheet: View {
    @Binding var selectedTimeRange: TimeRange
    @Binding var selectedWorkType: WorkType?
    @Binding var sortOption: SortOption
    
    @EnvironmentObject private var dataManager: DataManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var tempTimeRange: TimeRange
    @State private var tempWorkType: WorkType?
    @State private var tempSortOption: SortOption
    @State private var showingCustomDateRange = false
    @State private var customStartDate = Date()
    @State private var customEndDate = Date()
    
    init(
        selectedTimeRange: Binding<TimeRange>,
        selectedWorkType: Binding<WorkType?>,
        sortOption: Binding<SortOption>
    ) {
        self._selectedTimeRange = selectedTimeRange
        self._selectedWorkType = selectedWorkType
        self._sortOption = sortOption
        
        self._tempTimeRange = State(initialValue: selectedTimeRange.wrappedValue)
        self._tempWorkType = State(initialValue: selectedWorkType.wrappedValue)
        self._tempSortOption = State(initialValue: sortOption.wrappedValue)
    }
    
    var body: some View {
        NavigationView {
            Form {
                // 时间范围部分
                timeRangeSection
                
                // 工作类型部分
                workTypeSection
                
                // 排序选项部分
                sortSection
                
                // 快速筛选部分
                quickFiltersSection
            }
            .navigationTitle("筛选和排序")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("应用") {
                        applyFilters()
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 时间范围部分
    private var timeRangeSection: some View {
        Section("时间范围") {
            ForEach(TimeRange.allCases, id: \.self) { range in
                HStack {
                    Text(range.displayName)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    if tempTimeRange == range {
                        Image(systemName: "checkmark")
                            .foregroundColor(.blue)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    tempTimeRange = range
                }
            }
            
            // 自定义日期范围
            Button {
                showingCustomDateRange = true
            } label: {
                HStack {
                    Text("自定义范围")
                        .foregroundColor(.blue)
                    
                    Spacer()
                    
                    Image(systemName: "calendar.badge.plus")
                        .foregroundColor(.blue)
                }
            }
        }
    }
    
    // MARK: - 工作类型部分
    private var workTypeSection: some View {
        Section("工作类型") {
            // 全部类型选项
            HStack {
                Text("全部类型")
                    .foregroundColor(.primary)
                
                Spacer()
                
                if tempWorkType == nil {
                    Image(systemName: "checkmark")
                        .foregroundColor(.blue)
                }
            }
            .contentShape(Rectangle())
            .onTapGesture {
                tempWorkType = nil
            }
            
            // 具体工作类型
            ForEach(workTypes, id: \.id) { workType in
                HStack {
                    Image(systemName: workType.iconName)
                        .foregroundColor(workType.themeColor)
                        .frame(width: 20)
                    
                    Text(workType.name)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    if tempWorkType?.id == workType.id {
                        Image(systemName: "checkmark")
                            .foregroundColor(.blue)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    tempWorkType = workType
                }
            }
        }
    }
    
    // MARK: - 排序选项部分
    private var sortSection: some View {
        Section("排序方式") {
            ForEach(SortOption.allCases, id: \.self) { option in
                HStack {
                    Text(option.displayName)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    if tempSortOption == option {
                        Image(systemName: "checkmark")
                            .foregroundColor(.blue)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    tempSortOption = option
                }
            }
        }
    }
    
    // MARK: - 快速筛选部分
    private var quickFiltersSection: some View {
        Section("快速筛选") {
            QuickFilterButton(
                title: "今日工作",
                subtitle: "查看今天的工作记录",
                icon: "calendar.badge.clock"
            ) {
                tempTimeRange = .thisWeek
                tempSortOption = .dateDescending
            }
            
            QuickFilterButton(
                title: "高收入工作",
                subtitle: "按收入从高到低排序",
                icon: "dollarsign.circle.fill"
            ) {
                tempSortOption = .incomeDescending
            }
            
            QuickFilterButton(
                title: "长时间工作",
                subtitle: "按工作时长从长到短排序",
                icon: "clock.fill"
            ) {
                tempSortOption = .durationDescending
            }
            
            QuickFilterButton(
                title: "重置筛选",
                subtitle: "清除所有筛选条件",
                icon: "arrow.clockwise"
            ) {
                resetFilters()
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var workTypes: [WorkType] {
        return dataManager.fetchWorkTypes()
    }
    
    // MARK: - 方法
    
    private func applyFilters() {
        selectedTimeRange = tempTimeRange
        selectedWorkType = tempWorkType
        sortOption = tempSortOption
    }
    
    private func resetFilters() {
        tempTimeRange = .thisMonth
        tempWorkType = nil
        tempSortOption = .dateDescending
    }
}

// MARK: - 快速筛选按钮
struct QuickFilterButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.blue)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 自定义日期范围选择器
struct CustomDateRangeView: View {
    @Binding var startDate: Date
    @Binding var endDate: Date
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            Form {
                Section("选择日期范围") {
                    DatePicker(
                        "开始日期",
                        selection: $startDate,
                        displayedComponents: [.date]
                    )
                    
                    DatePicker(
                        "结束日期",
                        selection: $endDate,
                        displayedComponents: [.date]
                    )
                }
                
                Section {
                    HStack {
                        Text("天数")
                        Spacer()
                        Text("\(daysBetween)天")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("自定义范围")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确定") {
                        dismiss()
                    }
                    .disabled(startDate > endDate)
                }
            }
        }
    }
    
    private var daysBetween: Int {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: startDate, to: endDate)
        return max(0, components.day ?? 0) + 1
    }
}

// MARK: - 筛选器状态指示器
struct FilterStatusIndicator: View {
    let hasActiveFilters: Bool
    let filterCount: Int
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: hasActiveFilters ? "line.3.horizontal.decrease.circle.fill" : "line.3.horizontal.decrease.circle")
                .foregroundColor(hasActiveFilters ? .blue : .secondary)
            
            if hasActiveFilters {
                Text("\(filterCount)")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .frame(width: 16, height: 16)
                    .background(Color.red)
                    .clipShape(Circle())
            }
        }
    }
}

#Preview {
    FilterSheet(
        selectedTimeRange: .constant(.thisMonth),
        selectedWorkType: .constant(nil),
        sortOption: .constant(.dateDescending)
    )
    .environmentObject(DataManager.shared)
}
