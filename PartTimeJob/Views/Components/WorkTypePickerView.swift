//
//  WorkTypePickerView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 工作类型选择器视图
struct WorkTypePickerView: View {
    @Binding var selectedWorkType: WorkType?
    let workTypes: [WorkType]
    
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var showingAddWorkType = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                searchBar
                
                // 工作类型列表
                workTypesList
            }
            .navigationTitle("选择工作类型")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingAddWorkType = true
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingAddWorkType) {
                AddWorkTypeView()
            }
        }
    }
    
    // MARK: - 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索工作类型", text: $searchText)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
        .padding()
    }
    
    // MARK: - 工作类型列表
    private var workTypesList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(filteredWorkTypes, id: \.id) { workType in
                    WorkTypeCard(
                        workType: workType,
                        isSelected: selectedWorkType?.id == workType.id,
                        onTap: {
                            selectedWorkType = workType
                            dismiss()
                        }
                    )
                }
                
                // 空状态
                if filteredWorkTypes.isEmpty {
                    EmptyWorkTypesView(
                        searchText: searchText,
                        onAddWorkType: {
                            showingAddWorkType = true
                        }
                    )
                }
            }
            .padding()
        }
    }
    
    // MARK: - 计算属性
    private var filteredWorkTypes: [WorkType] {
        if searchText.isEmpty {
            return workTypes
        } else {
            return workTypes.filter { workType in
                workType.name.localizedCaseInsensitiveContains(searchText) ||
                workType.workDescription.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
}

// MARK: - 工作类型卡片
struct WorkTypeCard: View {
    let workType: WorkType
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // 图标
                Image(systemName: workType.iconName)
                    .font(.title2)
                    .foregroundColor(workType.themeColor)
                    .frame(width: 40, height: 40)
                    .background(workType.themeColor.opacity(0.1))
                    .cornerRadius(8)
                
                // 信息
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(workType.name)
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        if workType.isHighPaying {
                            Text("高薪")
                                .font(.caption)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.orange.opacity(0.2))
                                .foregroundColor(.orange)
                                .cornerRadius(4)
                        }
                    }
                    
                    Text(workType.formattedDefaultSalary)
                        .font(.subheadline)
                        .foregroundColor(.green)
                        .fontWeight(.semibold)
                    
                    if !workType.workDescription.isEmpty {
                        Text(workType.workDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                    
                    // 标签
                    HStack {
                        ForEach(workType.tags.prefix(3), id: \.self) { tag in
                            Text(tag)
                                .font(.caption2)
                                .padding(.horizontal, 4)
                                .padding(.vertical, 2)
                                .background(Color(.systemGray5))
                                .foregroundColor(.secondary)
                                .cornerRadius(4)
                        }
                    }
                }
                
                // 选择指示器
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.blue : Color(.systemGray4), lineWidth: isSelected ? 2 : 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 空状态视图
struct EmptyWorkTypesView: View {
    let searchText: String
    let onAddWorkType: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: searchText.isEmpty ? "folder.badge.plus" : "magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text(searchText.isEmpty ? "暂无工作类型" : "未找到匹配的工作类型")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(searchText.isEmpty ? "创建您的第一个工作类型" : "尝试其他搜索词或创建新的工作类型")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: onAddWorkType) {
                HStack {
                    Image(systemName: "plus")
                    Text("添加工作类型")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
            }
        }
        .padding(40)
    }
}

// MARK: - 添加工作类型视图（占位）
struct AddWorkTypeView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("添加工作类型")
                    .font(.title)
                
                Spacer()
                
                Text("此功能将在后续阶段实现")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .navigationTitle("新建工作类型")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    WorkTypePickerView(
        selectedWorkType: .constant(nil),
        workTypes: WorkType.createDefaultWorkTypes()
    )
}
