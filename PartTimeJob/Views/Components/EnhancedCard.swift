//
//  EnhancedCard.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 增强卡片组件 - 统一的卡片样式和交互
struct EnhancedCard<Content: View>: View {
    
    // MARK: - 卡片样式枚举
    
    enum Style {
        case standard
        case elevated
        case outlined
        case filled
        
        var backgroundColor: Color {
            switch self {
            case .standard, .elevated:
                return DesignSystem.Colors.background
            case .outlined:
                return Color.clear
            case .filled:
                return DesignSystem.Colors.secondaryBackground
            }
        }
        
        var shadowRadius: CGFloat {
            switch self {
            case .standard:
                return DesignSystem.Shadow.light.radius
            case .elevated:
                return DesignSystem.Shadow.medium.radius
            case .outlined, .filled:
                return 0
            }
        }
        
        var borderWidth: CGFloat {
            switch self {
            case .outlined:
                return 1
            default:
                return 0
            }
        }
        
        var borderColor: Color {
            switch self {
            case .outlined:
                return DesignSystem.Colors.separator
            default:
                return Color.clear
            }
        }
    }
    
    // MARK: - 属性
    
    let content: Content
    let style: Style
    let padding: CGFloat
    let cornerRadius: CGFloat
    let isInteractive: Bool
    let onTap: (() -> Void)?
    
    @State private var isPressed = false
    
    // MARK: - 初始化
    
    init(
        style: Style = .standard,
        padding: CGFloat = DesignSystem.Spacing.cardPadding,
        cornerRadius: CGFloat = DesignSystem.CornerRadius.card,
        isInteractive: Bool = false,
        onTap: (() -> Void)? = nil,
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.style = style
        self.padding = padding
        self.cornerRadius = cornerRadius
        self.isInteractive = isInteractive
        self.onTap = onTap
    }
    
    // MARK: - 视图
    
    var body: some View {
        Group {
            if isInteractive || onTap != nil {
                Button(action: {
                    CalendarHapticFeedback.lightImpact()
                    onTap?()
                }) {
                    cardContent
                }
                .buttonStyle(PlainButtonStyle())
                .scaleEffect(isPressed ? 0.98 : 1.0)
                .animation(DesignSystem.Animation.quick, value: isPressed)
                .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                    if isInteractive {
                        isPressed = pressing
                    }
                }, perform: {})
            } else {
                cardContent
            }
        }
    }
    
    private var cardContent: some View {
        content
            .padding(padding)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(style.backgroundColor)
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(style.borderColor, lineWidth: style.borderWidth)
            )
            .cornerRadius(cornerRadius)
            .shadow(
                color: DesignSystem.Shadow.light.color,
                radius: style.shadowRadius,
                x: 0,
                y: style.shadowRadius > 0 ? 2 : 0
            )
    }
}

// MARK: - 统计卡片

struct StatisticCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    let trend: TrendDirection?
    let trendValue: String?
    
    enum TrendDirection {
        case up, down, stable
        
        var icon: String {
            switch self {
            case .up: return "arrow.up"
            case .down: return "arrow.down"
            case .stable: return "minus"
            }
        }
        
        var color: Color {
            switch self {
            case .up: return DesignSystem.Colors.success
            case .down: return DesignSystem.Colors.error
            case .stable: return DesignSystem.Colors.secondary
            }
        }
    }
    
    var body: some View {
        EnhancedCard(style: .elevated) {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                HStack {
                    Image(systemName: icon)
                        .font(.title2)
                        .foregroundColor(color)
                    
                    Spacer()
                    
                    if let trend = trend, let trendValue = trendValue {
                        HStack(spacing: 4) {
                            Image(systemName: trend.icon)
                                .font(.caption)
                            Text(trendValue)
                                .font(ResponsiveLayout.Typography.caption)
                        }
                        .foregroundColor(trend.color)
                    }
                }
                
                Text(value)
                    .font(ResponsiveLayout.Typography.title)
                    .fontWeight(.bold)
                    .foregroundColor(DesignSystem.Colors.primaryText)
                
                Text(title)
                    .font(ResponsiveLayout.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.secondaryText)
            }
        }
    }
}

// MARK: - 工作记录卡片

struct WorkRecordCard: View {
    let workRecord: WorkRecord
    let workType: WorkType?
    let onTap: () -> Void
    
    var body: some View {
        EnhancedCard(style: .standard, isInteractive: true, onTap: onTap) {
            HStack(spacing: DesignSystem.Spacing.md) {
                // 工作类型图标
                ZStack {
                    Circle()
                        .fill(workType?.themeColor ?? DesignSystem.Colors.primary)
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: workType?.iconName ?? "briefcase.fill")
                        .font(.system(size: 18))
                        .foregroundColor(.white)
                }
                
                // 工作信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(workRecord.workDescription)
                        .font(ResponsiveLayout.Typography.headline)
                        .foregroundColor(DesignSystem.Colors.primaryText)
                        .lineLimit(1)
                    
                    HStack {
                        Text(DateHelper.hourMinuteFormatter.string(from: workRecord.startTime))
                        Text("-")
                        Text(DateHelper.hourMinuteFormatter.string(from: workRecord.endTime))
                    }
                    .font(ResponsiveLayout.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.secondaryText)
                    
                    Text("\(workRecord.workHours, specifier: "%.1f")小时")
                        .font(ResponsiveLayout.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.tertiaryText)
                }
                
                Spacer()
                
                // 收入信息
                VStack(alignment: .trailing, spacing: 4) {
                    Text("¥\(workRecord.actualSalary, specifier: "%.0f")")
                        .font(ResponsiveLayout.Typography.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(DesignSystem.Colors.success)
                    
                    Text(workRecord.salaryType.displayName)
                        .font(ResponsiveLayout.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.secondaryText)
                }
            }
        }
    }
}

// MARK: - 快速操作卡片

struct QuickActionCard: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        EnhancedCard(style: .filled, isInteractive: true, onTap: action) {
            VStack(spacing: DesignSystem.Spacing.sm) {
                Image(systemName: icon)
                    .font(.title)
                    .foregroundColor(color)
                    .frame(height: 32)
                
                VStack(spacing: 2) {
                    Text(title)
                        .font(ResponsiveLayout.Typography.headline)
                        .foregroundColor(DesignSystem.Colors.primaryText)
                        .multilineTextAlignment(.center)
                    
                    Text(subtitle)
                        .font(ResponsiveLayout.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.secondaryText)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 100)
        }
    }
}

// MARK: - 信息卡片

struct InfoCard: View {
    let title: String
    let message: String
    let type: InfoType
    
    enum InfoType {
        case info, success, warning, error
        
        var color: Color {
            switch self {
            case .info: return DesignSystem.Colors.primary
            case .success: return DesignSystem.Colors.success
            case .warning: return DesignSystem.Colors.warning
            case .error: return DesignSystem.Colors.error
            }
        }
        
        var icon: String {
            switch self {
            case .info: return "info.circle.fill"
            case .success: return "checkmark.circle.fill"
            case .warning: return "exclamationmark.triangle.fill"
            case .error: return "xmark.circle.fill"
            }
        }
        
        var backgroundColor: Color {
            color.opacity(0.1)
        }
    }
    
    var body: some View {
        EnhancedCard(style: .outlined) {
            HStack(spacing: DesignSystem.Spacing.md) {
                Image(systemName: type.icon)
                    .font(.title2)
                    .foregroundColor(type.color)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(ResponsiveLayout.Typography.headline)
                        .foregroundColor(DesignSystem.Colors.primaryText)
                    
                    Text(message)
                        .font(ResponsiveLayout.Typography.body)
                        .foregroundColor(DesignSystem.Colors.secondaryText)
                        .fixedSize(horizontal: false, vertical: true)
                }
                
                Spacer()
            }
        }
        .background(type.backgroundColor)
        .cornerRadius(DesignSystem.CornerRadius.card)
    }
}

// MARK: - 预览

#Preview {
    ScrollView {
        VStack(spacing: 20) {
            StatisticCard(
                title: "本月收入",
                value: "¥2,580",
                icon: "yensign.circle.fill",
                color: .green,
                trend: .up,
                trendValue: "+12%"
            )
            
            WorkRecordCard(
                workRecord: WorkRecord.sampleData[0],
                workType: WorkType.createDefaultWorkTypes()[0]
            ) { }
            
            LazyVGrid(columns: ResponsiveLayout.Grid.gridItems(count: 2), spacing: 16) {
                QuickActionCard(
                    title: "添加记录",
                    subtitle: "快速添加",
                    icon: "plus.circle.fill",
                    color: .blue
                ) { }
                
                QuickActionCard(
                    title: "查看统计",
                    subtitle: "数据分析",
                    icon: "chart.bar.fill",
                    color: .orange
                ) { }
            }
            
            InfoCard(
                title: "提示",
                message: "这是一条信息提示，用于向用户展示重要信息。",
                type: .info
            )
        }
        .padding()
    }
}
