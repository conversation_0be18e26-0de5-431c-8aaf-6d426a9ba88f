//
//  WorkTypeRowView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 工作类型行视图
struct WorkTypeRowView: View {
    let workType: WorkType
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var usageStats: WorkTypeUsageStats?
    
    var body: some View {
        HStack(spacing: 16) {
            // 工作类型图标和颜色
            workTypeIcon
            
            // 工作类型信息
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(workType.name)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    // 使用频率标识
                    if let stats = usageStats, stats.totalRecords > 0 {
                        usageIndicator
                    }
                }
                
                // 薪资信息
                salaryInfo
                
                // 使用统计
                if let stats = usageStats {
                    usageStatsView(stats)
                }
            }
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
            Button("删除", role: .destructive) {
                onDelete()
            }
            
            But<PERSON>("编辑") {
                onEdit()
            }
            .tint(.blue)
        }
        .onTapGesture {
            onEdit()
        }
        .onAppear {
            loadUsageStats()
        }
    }
    
    // MARK: - 工作类型图标
    private var workTypeIcon: some View {
        ZStack {
            Circle()
                .fill(workType.themeColor)
                .frame(width: 50, height: 50)
            
            Image(systemName: workType.iconName)
                .font(.title2)
                .foregroundColor(.white)
        }
    }
    
    // MARK: - 薪资信息
    private var salaryInfo: some View {
        HStack {
            Text(workType.salaryType.displayName)
                .font(.caption)
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(Color.blue.opacity(0.1))
                .foregroundColor(.blue)
                .cornerRadius(4)
            
            Text("¥\(workType.defaultSalary, specifier: "%.0f")")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.green)
            
            Spacer()
        }
    }
    
    // MARK: - 使用频率指示器
    private var usageIndicator: some View {
        HStack(spacing: 2) {
            ForEach(0..<5, id: \.self) { index in
                Circle()
                    .fill(index < usageLevel ? Color.orange : Color.gray.opacity(0.3))
                    .frame(width: 6, height: 6)
            }
        }
    }
    
    private var usageLevel: Int {
        guard let stats = usageStats else { return 0 }
        
        // 根据使用次数计算等级（1-5星）
        switch stats.totalRecords {
        case 0:
            return 0
        case 1...5:
            return 1
        case 6...15:
            return 2
        case 16...30:
            return 3
        case 31...50:
            return 4
        default:
            return 5
        }
    }
    
    // MARK: - 使用统计视图
    private func usageStatsView(_ stats: WorkTypeUsageStats) -> some View {
        HStack {
            Label("\(stats.totalRecords)次", systemImage: "clock")
                .font(.caption2)
                .foregroundColor(.secondary)
            
            if stats.totalIncome > 0 {
                Label("¥\(stats.totalIncome, specifier: "%.0f")", systemImage: "yensign.circle")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            if let lastUsed = stats.lastUsedDate {
                Label(DateHelper.timeAgoDescription(from: lastUsed), systemImage: "calendar")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
    
    // MARK: - 数据加载
    private func loadUsageStats() {
        // 计算使用统计
        let records = dataManager.fetchWorkRecords().filter { $0.workTypeId == workType.id }
        
        let totalRecords = records.count
        let totalIncome = records.reduce(0) { $0 + $1.actualSalary }
        let totalHours = records.reduce(0) { $0 + $1.workHours }
        let lastUsedDate = records.map { $0.date }.max()
        
        usageStats = WorkTypeUsageStats(
            totalRecords: totalRecords,
            totalIncome: totalIncome,
            totalHours: totalHours,
            lastUsedDate: lastUsedDate,
            averageIncome: totalRecords > 0 ? totalIncome / Double(totalRecords) : 0
        )
    }
}

// MARK: - 使用统计数据结构

struct WorkTypeUsageStats {
    let totalRecords: Int
    let totalIncome: Double
    let totalHours: Double
    let lastUsedDate: Date?
    let averageIncome: Double
}

// MARK: - 预览

#Preview {
    List {
        WorkTypeRowView(
            workType: WorkType.createDefaultWorkTypes()[0],
            onEdit: { },
            onDelete: { }
        )
        
        WorkTypeRowView(
            workType: WorkType.createDefaultWorkTypes()[1],
            onEdit: { },
            onDelete: { }
        )
    }
    .environmentObject(DataManager.shared)
}
