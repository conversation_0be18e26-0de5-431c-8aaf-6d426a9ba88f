//
//  IconPickerView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 图标选择器视图
struct IconPickerView: View {
    @Binding var selectedIcon: String
    @Environment(\.dismiss) private var dismiss
    
    // 预定义的图标分类
    private let iconCategories: [IconCategory] = [
        IconCategory(
            name: "工作相关",
            icons: [
                "briefcase.fill", "bag.fill", "case.fill",
                "hammer.fill", "wrench.fill", "screwdriver.fill",
                "paintbrush.fill", "pencil", "book.fill",
                "graduationcap.fill", "building.2.fill", "house.fill"
            ]
        ),
        IconCategory(
            name: "服务行业",
            icons: [
                "fork.knife", "cup.and.saucer.fill", "wineglass.fill",
                "cart.fill", "basket.fill", "gift.fill",
                "phone.fill", "headphones", "mic.fill",
                "camera.fill", "video.fill", "music.note"
            ]
        ),
        IconCategory(
            name: "运动健身",
            icons: [
                "figure.walk", "figure.run", "bicycle",
                "car.fill", "bus.fill", "airplane",
                "dumbbell.fill", "sportscourt.fill", "football.fill",
                "basketball.fill", "tennis.racket", "figure.swimming"
            ]
        ),
        IconCategory(
            name: "技术相关",
            icons: [
                "laptopcomputer", "desktopcomputer", "iphone",
                "gear", "cpu", "memorychip",
                "wifi", "antenna.radiowaves.left.and.right", "network",
                "code", "terminal.fill", "server.rack"
            ]
        ),
        IconCategory(
            name: "创意设计",
            icons: [
                "paintpalette.fill", "photo.fill", "camera.aperture",
                "scissors", "ruler.fill", "pencil.and.ruler.fill",
                "square.and.pencil", "highlighter", "eraser.fill",
                "eyedropper.full", "slider.horizontal.3", "wand.and.rays"
            ]
        ),
        IconCategory(
            name: "其他",
            icons: [
                "star.fill", "heart.fill", "flame.fill",
                "bolt.fill", "leaf.fill", "drop.fill",
                "sun.max.fill", "moon.fill", "cloud.fill",
                "snowflake", "thermometer", "location.fill"
            ]
        )
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    ForEach(iconCategories, id: \.name) { category in
                        iconCategorySection(category)
                    }
                }
                .padding()
            }
            .navigationTitle("选择图标")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 图标分类部分
    private func iconCategorySection(_ category: IconCategory) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(category.name)
                .font(.headline)
                .foregroundColor(.primary)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 6), spacing: 12) {
                ForEach(category.icons, id: \.self) { iconName in
                    iconButton(iconName)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 图标按钮
    private func iconButton(_ iconName: String) -> some View {
        Button {
            selectedIcon = iconName
            CalendarHapticFeedback.selectionChanged()
        } label: {
            ZStack {
                Circle()
                    .fill(selectedIcon == iconName ? Color.blue : Color.clear)
                    .frame(width: 44, height: 44)
                
                Circle()
                    .stroke(selectedIcon == iconName ? Color.blue : Color.gray.opacity(0.3), lineWidth: 2)
                    .frame(width: 44, height: 44)
                
                Image(systemName: iconName)
                    .font(.title3)
                    .foregroundColor(selectedIcon == iconName ? .white : .primary)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 图标分类数据结构

struct IconCategory {
    let name: String
    let icons: [String]
}

// MARK: - 颜色选择器视图

struct ColorPickerView: View {
    @Binding var selectedColor: Color
    @Environment(\.dismiss) private var dismiss
    
    // 预定义颜色
    private let predefinedColors: [Color] = [
        .red, .orange, .yellow, .green, .mint, .teal,
        .cyan, .blue, .indigo, .purple, .pink, .brown,
        .gray, .black,
        // 自定义颜色
        Color(red: 1.0, green: 0.3, blue: 0.3),  // 亮红
        Color(red: 1.0, green: 0.6, blue: 0.0),  // 橙色
        Color(red: 0.2, green: 0.8, blue: 0.2),  // 亮绿
        Color(red: 0.3, green: 0.7, blue: 1.0),  // 天蓝
        Color(red: 0.6, green: 0.3, blue: 1.0),  // 紫色
        Color(red: 1.0, green: 0.4, blue: 0.8)   // 粉红
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 当前选择的颜色预览
                VStack(spacing: 12) {
                    Text("当前选择")
                        .font(.headline)
                    
                    Circle()
                        .fill(selectedColor)
                        .frame(width: 80, height: 80)
                        .overlay(
                            Circle()
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                }
                
                // 预定义颜色网格
                VStack(alignment: .leading, spacing: 16) {
                    Text("预设颜色")
                        .font(.headline)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 12) {
                        ForEach(predefinedColors.indices, id: \.self) { index in
                            colorButton(predefinedColors[index])
                        }
                    }
                }
                
                // 系统颜色选择器
                VStack(alignment: .leading, spacing: 16) {
                    Text("自定义颜色")
                        .font(.headline)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    ColorPicker("选择颜色", selection: $selectedColor, supportsOpacity: false)
                        .labelsHidden()
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("选择颜色")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 颜色按钮
    private func colorButton(_ color: Color) -> some View {
        Button {
            selectedColor = color
            CalendarHapticFeedback.selectionChanged()
        } label: {
            ZStack {
                Circle()
                    .fill(color)
                    .frame(width: 40, height: 40)
                
                Circle()
                    .stroke(selectedColor == color ? Color.primary : Color.clear, lineWidth: 3)
                    .frame(width: 44, height: 44)
                
                if selectedColor == color {
                    Image(systemName: "checkmark")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview("IconPicker") {
    IconPickerView(selectedIcon: .constant("briefcase.fill"))
}

#Preview("ColorPicker") {
    ColorPickerView(selectedColor: .constant(.blue))
}
