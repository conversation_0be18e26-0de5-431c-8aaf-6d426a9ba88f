//
//  ExportOptionsView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 导出选项视图
struct ExportOptionsView: View {
    @EnvironmentObject private var dataManager: DataManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedExportType: ExportType = .csv
    @State private var selectedTimeRange: TimeRange = .thisMonth
    @State private var includeWorkTypes = true
    @State private var includeStatistics = true
    @State private var isExporting = false
    @State private var showingShareSheet = false
    @State private var exportedFileURL: URL?
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                // 导出格式选择
                exportFormatSection
                
                // 时间范围选择
                timeRangeSection
                
                // 导出内容选择
                exportContentSection
                
                // 预览信息
                previewSection
                
                // 导出按钮
                exportButtonSection
            }
            .navigationTitle("导出数据")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
            .alert("导出结果", isPresented: $showingAlert) {
                Button("确定") { }
            } message: {
                Text(alertMessage)
            }
            .sheet(isPresented: $showingShareSheet) {
                if let url = exportedFileURL {
                    ShareSheet(activityItems: [url])
                }
            }
        }
    }
    
    // MARK: - 导出格式选择
    private var exportFormatSection: some View {
        Section("导出格式") {
            Picker("格式", selection: $selectedExportType) {
                ForEach(ExportType.allCases, id: \.self) { type in
                    HStack {
                        Image(systemName: type.iconName)
                        Text(type.displayName)
                    }
                    .tag(type)
                }
            }
            .pickerStyle(InlinePickerStyle())
        }
    }
    
    // MARK: - 时间范围选择
    private var timeRangeSection: some View {
        Section("时间范围") {
            Picker("范围", selection: $selectedTimeRange) {
                ForEach(TimeRange.allCases, id: \.self) { range in
                    Text(range.displayName).tag(range)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
    
    // MARK: - 导出内容选择
    private var exportContentSection: some View {
        Section("导出内容") {
            Toggle("包含工作类型信息", isOn: $includeWorkTypes)
            Toggle("包含统计分析", isOn: $includeStatistics)
        }
    }
    
    // MARK: - 预览信息
    private var previewSection: some View {
        Section("预览信息") {
            let records = getFilteredRecords()
            
            HStack {
                Text("工作记录数量")
                Spacer()
                Text("\(records.count)条")
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Text("时间范围")
                Spacer()
                Text(timeRangeDescription)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Text("预计文件大小")
                Spacer()
                Text(estimatedFileSize)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - 导出按钮
    private var exportButtonSection: some View {
        Section {
            Button {
                exportData()
            } label: {
                HStack {
                    if isExporting {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "square.and.arrow.up")
                    }
                    
                    Text(isExporting ? "导出中..." : "开始导出")
                        .fontWeight(.medium)
                }
                .frame(maxWidth: .infinity)
                .foregroundColor(.white)
                .padding()
                .background(isExporting ? Color.gray : Color.blue)
                .cornerRadius(12)
            }
            .disabled(isExporting)
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - 计算属性
    
    private var timeRangeDescription: String {
        let (startDate, endDate) = selectedTimeRange.dateRange
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return "\(formatter.string(from: startDate)) - \(formatter.string(from: endDate))"
    }
    
    private var estimatedFileSize: String {
        let records = getFilteredRecords()
        let baseSize = records.count * 200 // 每条记录约200字节
        let totalSize = baseSize + (includeWorkTypes ? 1000 : 0) + (includeStatistics ? 2000 : 0)
        
        if totalSize < 1024 {
            return "\(totalSize)B"
        } else if totalSize < 1024 * 1024 {
            return String(format: "%.1fKB", Double(totalSize) / 1024)
        } else {
            return String(format: "%.1fMB", Double(totalSize) / (1024 * 1024))
        }
    }
    
    // MARK: - 数据处理
    
    private func getFilteredRecords() -> [WorkRecord] {
        let allRecords = dataManager.fetchWorkRecords()
        let (startDate, endDate) = selectedTimeRange.dateRange
        
        return allRecords.filter { record in
            record.date >= startDate && record.date <= endDate
        }
    }
    
    private func exportData() {
        isExporting = true
        
        Task {
            do {
                let records = getFilteredRecords()
                let workTypes = includeWorkTypes ? dataManager.fetchWorkTypes() : []
                
                let url = try await DataExporter.exportData(
                    records: records,
                    workTypes: workTypes,
                    format: selectedExportType,
                    includeStatistics: includeStatistics,
                    timeRange: selectedTimeRange
                )
                
                await MainActor.run {
                    exportedFileURL = url
                    showingShareSheet = true
                    isExporting = false
                    CalendarHapticFeedback.successAction()
                }
                
            } catch {
                await MainActor.run {
                    alertMessage = "导出失败：\(error.localizedDescription)"
                    showingAlert = true
                    isExporting = false
                    CalendarHapticFeedback.errorAction()
                }
            }
        }
    }
}

// MARK: - 导出类型枚举

enum ExportType: CaseIterable {
    case csv
    case excel
    case json
    case pdf
    
    var displayName: String {
        switch self {
        case .csv:
            return "CSV 文件"
        case .excel:
            return "Excel 文件"
        case .json:
            return "JSON 文件"
        case .pdf:
            return "PDF 报告"
        }
    }
    
    var iconName: String {
        switch self {
        case .csv:
            return "doc.text"
        case .excel:
            return "tablecells"
        case .json:
            return "doc.plaintext"
        case .pdf:
            return "doc.richtext"
        }
    }
    
    var fileExtension: String {
        switch self {
        case .csv:
            return "csv"
        case .excel:
            return "xlsx"
        case .json:
            return "json"
        case .pdf:
            return "pdf"
        }
    }
}

// MARK: - 分享视图

struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: nil
        )
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // 不需要更新
    }
}

#Preview {
    ExportOptionsView()
        .environmentObject(DataManager.shared)
}
