//
//  CalendarComponents.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

// MARK: - 日历工具栏
struct CalendarToolbar: View {
    @Binding var selectedDate: Date
    @Binding var calendarViewType: CalendarViewType
    let onDatePickerTap: () -> Void
    let onTodayTap: () -> Void
    let onAddWorkTap: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            // 顶部工具栏
            HStack {
                // 日期选择按钮
                Button(action: onDatePickerTap) {
                    HStack(spacing: 6) {
                        Image(systemName: "calendar.badge.clock")
                            .font(.title3)
                        
                        Text(formattedCurrentDate)
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.blue)
                }
                
                Spacer()
                
                // 今天按钮
                if !DateHelper.isToday(selectedDate) {
                    Button("今天", action: onTodayTap)
                        .font(.body)
                        .foregroundColor(.blue)
                }
                
                // 添加按钮
                Button(action: onAddWorkTap) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                }
            }
            .padding(.horizontal)
            
            // 视图类型选择器
            Picker("视图类型", selection: $calendarViewType) {
                ForEach(CalendarViewType.allCases, id: \.self) { type in
                    Text(type.displayName).tag(type)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding(.horizontal)
        }
    }
    
    private var formattedCurrentDate: String {
        let formatter = DateFormatter()
        
        switch calendarViewType {
        case .month:
            formatter.dateFormat = "yyyy年MM月"
        case .week:
            let startOfWeek = DateHelper.startOfWeek(for: selectedDate)
            let endOfWeek = DateHelper.endOfWeek(for: selectedDate)
            
            if Calendar.current.isDate(startOfWeek, equalTo: endOfWeek, toGranularity: .month) {
                formatter.dateFormat = "MM月dd日"
                let startDay = formatter.string(from: startOfWeek)
                let endDay = Calendar.current.component(.day, from: endOfWeek)
                return "\(startDay) - \(endDay)日"
            } else {
                formatter.dateFormat = "MM月dd日"
                return "\(formatter.string(from: startOfWeek)) - \(formatter.string(from: endOfWeek))"
            }
        case .list:
            formatter.dateFormat = "yyyy年MM月"
        }
        
        return formatter.string(from: selectedDate)
    }
}

// MARK: - 月份导航控件
struct MonthNavigationView: View {
    @Binding var selectedDate: Date
    let onPreviousMonth: () -> Void
    let onNextMonth: () -> Void
    
    var body: some View {
        HStack {
            Button(action: onPreviousMonth) {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
            
            Spacer()
            
            Text(monthYearString)
                .font(.title2)
                .fontWeight(.semibold)
                .onTapGesture {
                    // 可以添加快速跳转到今天的功能
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedDate = Date()
                    }
                }
            
            Spacer()
            
            Button(action: onNextMonth) {
                Image(systemName: "chevron.right")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 10)
    }
    
    private var monthYearString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月"
        return formatter.string(from: selectedDate)
    }
}

// MARK: - 星期标题视图
struct WeekdayHeaderView: View {
    let weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(weekdays, id: \.self) { weekday in
                Text(weekday)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
            }
        }
        .background(Color(.systemGray6))
    }
}

// MARK: - 日历网格容器
struct CalendarGridContainer<Content: View>: View {
    let content: Content
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    var body: some View {
        VStack(spacing: 1) {
            WeekdayHeaderView()
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 1) {
                content
            }
            .padding(.horizontal, 1)
        }
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
}

// MARK: - 工作密度指示器
struct WorkDensityIndicator: View {
    let workRecords: [WorkRecord]
    let workTypes: [WorkType]
    let maxHoursInMonth: Double

    @EnvironmentObject private var userPreferences: UserPreferences

    var body: some View {
        if !workRecords.isEmpty {
            ZStack {
                // 主要工作类型颜色背景
                Circle()
                    .fill(primaryWorkTypeColor)
                    .frame(width: indicatorSize, height: indicatorSize)
                    .opacity(densityOpacity)

                // 多工作类型时的混合指示
                if hasMultipleWorkTypes {
                    Circle()
                        .stroke(primaryWorkTypeColor, lineWidth: 1.5)
                        .frame(width: indicatorSize + 3, height: indicatorSize + 3)
                        .opacity(0.8)
                }

                // 高密度工作的特殊标识
                if workDensity > 0.8 {
                    Circle()
                        .fill(Color.white)
                        .frame(width: 3, height: 3)
                }

                // 收入等级指示
                if userPreferences.showIncomePreview && totalIncome > 0 {
                    incomeIndicator
                }
            }
        }
    }

    // MARK: - 计算属性

    private var totalWorkHours: Double {
        return workRecords.reduce(0) { $0 + $1.workHours }
    }

    private var totalIncome: Double {
        return SalaryCalculator.calculateTotalIncome(from: workRecords)
    }

    private var workDensity: Double {
        guard maxHoursInMonth > 0 else { return 0 }
        return min(totalWorkHours / maxHoursInMonth, 1.0)
    }

    private var indicatorSize: CGFloat {
        let baseSize: CGFloat = 8
        let maxSize: CGFloat = 16
        return baseSize + (workDensity * (maxSize - baseSize))
    }

    private var densityOpacity: Double {
        return 0.5 + (workDensity * 0.5) // 0.5-1.0的范围
    }

    private var primaryWorkTypeColor: Color {
        // 按工作时长找出主要工作类型
        let workTypeHours = Dictionary(grouping: workRecords) { $0.workTypeId }
            .mapValues { records in records.reduce(0) { $0 + $1.workHours } }

        guard let primaryWorkTypeId = workTypeHours.max(by: { $0.value < $1.value })?.key,
              let workType = workTypes.first(where: { $0.id == primaryWorkTypeId }) else {
            return .blue
        }

        return workType.themeColor
    }

    private var hasMultipleWorkTypes: Bool {
        let uniqueWorkTypes = Set(workRecords.map { $0.workTypeId })
        return uniqueWorkTypes.count > 1
    }

    private var incomeIndicator: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                Circle()
                    .fill(incomeColor)
                    .frame(width: 4, height: 4)
            }
        }
        .frame(width: indicatorSize, height: indicatorSize)
    }

    private var incomeColor: Color {
        let avgIncome = totalIncome / Double(workRecords.count)
        switch avgIncome {
        case 0..<100:
            return .yellow
        case 100..<300:
            return .orange
        default:
            return .green
        }
    }
}

// MARK: - 工作时间轴视图
struct WorkTimelineView: View {
    let workRecords: [WorkRecord]
    let onWorkRecordTap: (WorkRecord) -> Void
    
    @EnvironmentObject private var dataManager: DataManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ForEach(sortedWorkRecords, id: \.id) { record in
                WorkTimelineItem(
                    record: record,
                    workType: workType(for: record),
                    onTap: { onWorkRecordTap(record) }
                )
            }
        }
    }
    
    private var sortedWorkRecords: [WorkRecord] {
        return workRecords.sorted { $0.startTime < $1.startTime }
    }
    
    private func workType(for record: WorkRecord) -> WorkType? {
        return dataManager.fetchWorkType(by: record.workTypeId)
    }
}

// MARK: - 工作时间轴项目
struct WorkTimelineItem: View {
    let record: WorkRecord
    let workType: WorkType?
    let onTap: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 时间轴线条
            VStack {
                Circle()
                    .fill(workTypeColor)
                    .frame(width: 8, height: 8)
                
                if !isLastItem {
                    Rectangle()
                        .fill(workTypeColor.opacity(0.3))
                        .frame(width: 2)
                        .frame(maxHeight: .infinity)
                }
            }
            .frame(width: 8)
            
            // 工作信息
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(record.formattedTimeRange)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(record.formattedSalary)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
                
                Text(record.workDescription)
                    .font(.body)
                    .foregroundColor(.primary)
                    .lineLimit(2)
                
                if let workType = workType {
                    Text(workType.name)
                        .font(.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(workTypeColor.opacity(0.2))
                        .foregroundColor(workTypeColor)
                        .cornerRadius(4)
                }
            }
        }
        .padding(.vertical, 4)
        .onTapGesture {
            onTap()
        }
    }
    
    private var workTypeColor: Color {
        return workType?.themeColor ?? .gray
    }
    
    private var isLastItem: Bool {
        // 这里需要从父视图传入信息，暂时返回false
        return false
    }
}

// MARK: - 快速操作菜单
struct QuickActionMenu: View {
    let date: Date
    let onAddWork: () -> Void
    let onViewRecords: () -> Void
    let onCopyPrevious: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            Text("快速操作")
                .font(.headline)
                .padding(.top)
            
            VStack(spacing: 8) {
                QuickActionButton(
                    title: "添加工作记录",
                    icon: "plus.circle.fill",
                    color: .blue,
                    action: onAddWork
                )
                
                QuickActionButton(
                    title: "查看当日记录",
                    icon: "list.bullet",
                    color: .green,
                    action: onViewRecords
                )
                
                QuickActionButton(
                    title: "复制昨日安排",
                    icon: "doc.on.doc",
                    color: .orange,
                    action: onCopyPrevious
                )
            }
            .padding(.bottom)
        }
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
}

// MARK: - 快速操作按钮
struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                    .frame(width: 24)
                
                Text(title)
                    .font(.body)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
