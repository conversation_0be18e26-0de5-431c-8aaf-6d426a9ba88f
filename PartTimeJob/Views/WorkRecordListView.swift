//
//  WorkRecordListView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 工作记录列表视图
struct WorkRecordListView: View {
    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences
    
    @State private var selectedTimeRange: TimeRange = .thisMonth
    @State private var searchText = ""
    @State private var selectedWorkType: WorkType?
    @State private var sortOption: SortOption = .dateDescending
    @State private var showingFilters = false
    @State private var showingAddRecord = false
    @State private var selectedRecord: WorkRecord?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索和筛选栏
                searchAndFilterBar
                
                // 统计摘要
                statisticsSummary
                
                // 工作记录列表
                recordsList
            }
            .navigationTitle("工作记录")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button {
                        showingFilters = true
                    } label: {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingAddRecord = true
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingFilters) {
                FilterSheet(
                    selectedTimeRange: $selectedTimeRange,
                    selectedWorkType: $selectedWorkType,
                    sortOption: $sortOption
                )
            }
            .sheet(isPresented: $showingAddRecord) {
                AddWorkRecordView(selectedDate: Date())
            }
            .sheet(item: $selectedRecord) { record in
                WorkRecordDetailView(workRecord: record)
            }
        }
    }
    
    // MARK: - 搜索和筛选栏
    private var searchAndFilterBar: some View {
        VStack(spacing: 12) {
            // 搜索栏
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("搜索工作记录", text: $searchText)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button("清除") {
                        searchText = ""
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            
            // 时间范围选择器
            Picker("时间范围", selection: $selectedTimeRange) {
                ForEach(TimeRange.allCases, id: \.self) { range in
                    Text(range.displayName).tag(range)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
        .padding()
    }
    
    // MARK: - 统计摘要
    private var statisticsSummary: some View {
        HStack(spacing: 20) {
            StatisticItem(
                title: "记录数",
                value: "\(filteredRecords.count)",
                color: .blue
            )
            
            StatisticItem(
                title: "总收入",
                value: userPreferences.currencySymbol + String(format: "%.0f", totalIncome),
                color: .green
            )
            
            StatisticItem(
                title: "总工时",
                value: String(format: "%.1f小时", totalHours),
                color: .orange
            )
            
            StatisticItem(
                title: "平均时薪",
                value: userPreferences.currencySymbol + String(format: "%.0f", averageHourlyRate),
                color: .purple
            )
        }
        .padding()
        .background(Color(.systemGray6))
    }
    
    // MARK: - 工作记录列表
    private var recordsList: some View {
        Group {
            if filteredRecords.isEmpty {
                emptyStateView
            } else {
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(groupedRecords.keys.sorted(by: >), id: \.self) { date in
                            VStack(alignment: .leading, spacing: 8) {
                                // 日期标题
                                HStack {
                                    Text(DateHelper.relativeDescription(for: date))
                                        .font(.headline)
                                        .foregroundColor(.primary)
                                    
                                    Spacer()
                                    
                                    Text(DateHelper.yearMonthDayFormatter.string(from: date))
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    
                                    Text("共\(groupedRecords[date]?.count ?? 0)条")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                // 当日工作记录
                                ForEach(groupedRecords[date] ?? [], id: \.id) { record in
                                    WorkRecordRow(record: record) {
                                        selectedRecord = record
                                    }
                                }
                            }
                            .padding()
                            .background(Color(.systemBackground))
                            .cornerRadius(12)
                            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                        }
                    }
                    .padding()
                }
            }
        }
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text(searchText.isEmpty ? "暂无工作记录" : "未找到匹配的记录")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(searchText.isEmpty ? "开始记录您的第一份工作" : "尝试调整搜索条件或筛选器")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button {
                if searchText.isEmpty {
                    showingAddRecord = true
                } else {
                    searchText = ""
                }
            } label: {
                Text(searchText.isEmpty ? "添加工作记录" : "清除搜索")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(12)
            }
        }
        .padding(40)
    }
    
    // MARK: - 计算属性
    
    private var dateRange: (start: Date, end: Date) {
        return selectedTimeRange.dateRange
    }
    
    private var allRecords: [WorkRecord] {
        return dataManager.fetchWorkRecords(from: dateRange.start, to: dateRange.end)
    }
    
    private var filteredRecords: [WorkRecord] {
        var records = allRecords
        
        // 按工作类型筛选
        if let workType = selectedWorkType {
            records = records.filter { $0.workTypeId == workType.id }
        }
        
        // 按搜索文本筛选
        if !searchText.isEmpty {
            records = records.filter { record in
                record.workDescription.localizedCaseInsensitiveContains(searchText) ||
                record.notes.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // 排序
        switch sortOption {
        case .dateAscending:
            records.sort { $0.date < $1.date }
        case .dateDescending:
            records.sort { $0.date > $1.date }
        case .incomeAscending:
            records.sort { $0.calculateActualSalary() < $1.calculateActualSalary() }
        case .incomeDescending:
            records.sort { $0.calculateActualSalary() > $1.calculateActualSalary() }
        case .durationAscending:
            records.sort { $0.workHours < $1.workHours }
        case .durationDescending:
            records.sort { $0.workHours > $1.workHours }
        }
        
        return records
    }
    
    private var groupedRecords: [Date: [WorkRecord]] {
        return Dictionary(grouping: filteredRecords) { record in
            DateHelper.startOfDay(for: record.date)
        }
    }
    
    private var totalIncome: Double {
        return SalaryCalculator.calculateTotalIncome(from: filteredRecords)
    }
    
    private var totalHours: Double {
        return SalaryCalculator.calculateTotalWorkHours(from: filteredRecords)
    }
    
    private var averageHourlyRate: Double {
        return SalaryCalculator.calculateAverageHourlyRate(from: filteredRecords)
    }
}

// MARK: - 统计项组件
struct StatisticItem: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - 工作记录行组件
struct WorkRecordRow: View {
    let record: WorkRecord
    let onTap: () -> Void
    
    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // 工作类型指示器
                VStack {
                    Circle()
                        .fill(workTypeColor)
                        .frame(width: 12, height: 12)
                    
                    Rectangle()
                        .fill(workTypeColor.opacity(0.3))
                        .frame(width: 2)
                        .frame(maxHeight: .infinity)
                }
                .frame(width: 12)
                
                // 工作信息
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(workTypeName)
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        Text(record.formattedSalary)
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.green)
                    }
                    
                    Text(record.workDescription)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    HStack {
                        Text(record.formattedTimeRange)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        // 效率评级
                        if userPreferences.showEfficiencyRating {
                            HStack(spacing: 2) {
                                ForEach(0..<record.efficiencyRating.stars, id: \.self) { _ in
                                    Image(systemName: "star.fill")
                                        .font(.caption2)
                                        .foregroundColor(.yellow)
                                }
                            }
                        }
                        
                        // 工作状态
                        if userPreferences.showWorkStatus {
                            Text(record.workStatus.displayName)
                                .font(.caption2)
                                .padding(.horizontal, 4)
                                .padding(.vertical, 2)
                                .background(Color(hex: record.workStatus.color)?.opacity(0.2))
                                .foregroundColor(Color(hex: record.workStatus.color))
                                .cornerRadius(4)
                        }
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var workTypeName: String {
        return dataManager.fetchWorkType(by: record.workTypeId)?.name ?? "未知类型"
    }
    
    private var workTypeColor: Color {
        return dataManager.fetchWorkType(by: record.workTypeId)?.themeColor ?? .gray
    }
}

// MARK: - 排序选项枚举
enum SortOption: String, CaseIterable {
    case dateDescending = "dateDescending"
    case dateAscending = "dateAscending"
    case incomeDescending = "incomeDescending"
    case incomeAscending = "incomeAscending"
    case durationDescending = "durationDescending"
    case durationAscending = "durationAscending"
    
    var displayName: String {
        switch self {
        case .dateDescending:
            return "日期（新到旧）"
        case .dateAscending:
            return "日期（旧到新）"
        case .incomeDescending:
            return "收入（高到低）"
        case .incomeAscending:
            return "收入（低到高）"
        case .durationDescending:
            return "时长（长到短）"
        case .durationAscending:
            return "时长（短到长）"
        }
    }
}

#Preview {
    WorkRecordListView()
        .environmentObject(DataManager.shared)
        .environmentObject(UserPreferences.shared)
}
