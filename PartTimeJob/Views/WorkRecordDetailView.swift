//
//  WorkRecordDetailView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 工作记录详情视图
struct WorkRecordDetailView: View {
    let workRecord: WorkRecord
    
    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences
    @Environment(\.dismiss) private var dismiss
    
    @State private var showingEditView = false
    @State private var showingDeleteAlert = false
    @State private var showingShareSheet = false
    @State private var isDeleted = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 工作记录卡片
                    workRecordCard
                    
                    // 详细信息
                    detailsSection
                    
                    // 统计信息
                    statisticsSection
                    
                    // 操作按钮
                    actionButtonsSection
                }
                .padding()
            }
            .navigationTitle("工作详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button {
                            showingEditView = true
                        } label: {
                            Label("编辑", systemImage: "pencil")
                        }
                        
                        Button {
                            showingShareSheet = true
                        } label: {
                            Label("分享", systemImage: "square.and.arrow.up")
                        }
                        
                        Divider()
                        
                        Button(role: .destructive) {
                            showingDeleteAlert = true
                        } label: {
                            Label("删除", systemImage: "trash")
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            .sheet(isPresented: $showingEditView) {
                EditWorkRecordView(workRecord: workRecord)
            }
            .alert("删除工作记录", isPresented: $showingDeleteAlert) {
                Button("取消", role: .cancel) { }
                Button("删除", role: .destructive) {
                    deleteWorkRecord()
                }
            } message: {
                Text("确定要删除这条工作记录吗？此操作无法撤销。")
            }
            .sheet(isPresented: $showingShareSheet) {
                ShareSheet(items: [generateShareText()])
            }
        }
    }
    
    // MARK: - 工作记录卡片
    private var workRecordCard: some View {
        VStack(spacing: 16) {
            // 工作类型和状态
            HStack {
                if let workType = workType {
                    HStack(spacing: 12) {
                        Image(systemName: workType.iconName)
                            .font(.title2)
                            .foregroundColor(workType.themeColor)
                            .frame(width: 40, height: 40)
                            .background(workType.themeColor.opacity(0.1))
                            .cornerRadius(8)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(workType.name)
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Text(workRecord.workDescription)
                                .font(.body)
                                .foregroundColor(.secondary)
                                .lineLimit(3)
                        }
                    }
                }
                
                Spacer()
                
                // 工作状态标签
                Text(workRecord.workStatus.displayName)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(hex: workRecord.workStatus.color)?.opacity(0.2))
                    .foregroundColor(Color(hex: workRecord.workStatus.color))
                    .cornerRadius(8)
            }
            
            Divider()
            
            // 时间和收入信息
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    InfoItem(
                        icon: "calendar",
                        title: "日期",
                        value: DateHelper.yearMonthDayFormatter.string(from: workRecord.date)
                    )
                    
                    InfoItem(
                        icon: "clock",
                        title: "时间",
                        value: workRecord.formattedTimeRange
                    )
                    
                    InfoItem(
                        icon: "timer",
                        title: "时长",
                        value: DateHelper.workDurationDescription(workRecord.workHours * 3600)
                    )
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 8) {
                    Text(workRecord.formattedSalary)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    
                    Text(workRecord.salaryType.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if workRecord.salaryType == .hourly {
                        Text("时薪: \(userPreferences.currencySymbol)\(String(format: "%.0f", workRecord.salary))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 详细信息部分
    private var detailsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("详细信息")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                DetailRow(
                    title: "效率评级",
                    content: AnyView(
                        HStack {
                            ForEach(0..<workRecord.efficiencyRating.stars, id: \.self) { _ in
                                Image(systemName: "star.fill")
                                    .foregroundColor(.yellow)
                                    .font(.caption)
                            }
                            
                            Text(workRecord.efficiencyRating.displayName)
                                .font(.caption)
                                .foregroundColor(Color(hex: workRecord.efficiencyRating.color))
                        }
                    )
                )
                
                DetailRow(
                    title: "时长等级",
                    content: AnyView(
                        Text(workRecord.durationLevel.displayName)
                            .font(.body)
                            .foregroundColor(.primary)
                    )
                )
                
                if !workRecord.notes.isEmpty {
                    DetailRow(
                        title: "备注",
                        content: AnyView(
                            Text(workRecord.notes)
                                .font(.body)
                                .foregroundColor(.primary)
                        )
                    )
                }
                
                DetailRow(
                    title: "创建时间",
                    content: AnyView(
                        Text(DateHelper.timeAgoDescription(from: workRecord.createdAt))
                            .font(.body)
                            .foregroundColor(.secondary)
                    )
                )
                
                if workRecord.updatedAt != workRecord.createdAt {
                    DetailRow(
                        title: "更新时间",
                        content: AnyView(
                            Text(DateHelper.timeAgoDescription(from: workRecord.updatedAt))
                                .font(.body)
                                .foregroundColor(.secondary)
                        )
                    )
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 统计信息部分
    private var statisticsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("统计分析")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                StatisticCard(
                    title: "时薪",
                    value: String(format: "%.0f", workRecord.calculateActualSalary() / workRecord.workHours),
                    unit: userPreferences.currencySymbol + "/时",
                    color: .blue
                )
                
                StatisticCard(
                    title: "效率分数",
                    value: String(workRecord.efficiencyRating.stars * 25),
                    unit: "分",
                    color: .orange
                )
                
                StatisticCard(
                    title: "工作强度",
                    value: workRecord.durationLevel == .short ? "轻松" : 
                           workRecord.durationLevel == .medium ? "适中" : 
                           workRecord.durationLevel == .long ? "繁忙" : "高强度",
                    unit: "",
                    color: .purple
                )
                
                StatisticCard(
                    title: "相对时间",
                    value: DateHelper.relativeDescription(for: workRecord.date),
                    unit: "",
                    color: .green
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 操作按钮部分
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            Button {
                showingEditView = true
            } label: {
                HStack {
                    Image(systemName: "pencil")
                    Text("编辑记录")
                }
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
            }
            
            HStack(spacing: 12) {
                Button {
                    duplicateWorkRecord()
                } label: {
                    HStack {
                        Image(systemName: "doc.on.doc")
                        Text("复制")
                    }
                    .font(.body)
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
                }
                
                Button {
                    showingShareSheet = true
                } label: {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                        Text("分享")
                    }
                    .font(.body)
                    .foregroundColor(.green)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                }
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var workType: WorkType? {
        return dataManager.fetchWorkType(by: workRecord.workTypeId)
    }
    
    // MARK: - 方法
    
    private func deleteWorkRecord() {
        dataManager.deleteWorkRecord(workRecord)
        isDeleted = true
        dismiss()
    }
    
    private func duplicateWorkRecord() {
        let newRecord = WorkRecord(
            date: Date(),
            startTime: workRecord.startTime,
            endTime: workRecord.endTime,
            workTypeId: workRecord.workTypeId,
            workDescription: workRecord.workDescription,
            salary: workRecord.salary,
            salaryType: workRecord.salaryType,
            notes: workRecord.notes + " (复制)"
        )
        
        dataManager.addWorkRecord(newRecord)
        CalendarHapticFeedback.successAction()
    }
    
    private func generateShareText() -> String {
        let workTypeName = workType?.name ?? "未知工作"
        let dateString = DateHelper.yearMonthDayFormatter.string(from: workRecord.date)
        let timeString = workRecord.formattedTimeRange
        let salaryString = workRecord.formattedSalary
        
        return """
        📝 工作记录分享
        
        🏷️ 工作类型：\(workTypeName)
        📅 工作日期：\(dateString)
        ⏰ 工作时间：\(timeString)
        💰 工作收入：\(salaryString)
        ⭐ 效率评级：\(workRecord.efficiencyRating.displayName)
        
        📋 工作内容：\(workRecord.workDescription)
        
        来自兼职记录助手
        """
    }
}

#Preview {
    WorkRecordDetailView(
        workRecord: WorkRecord(
            date: Date(),
            startTime: Date(),
            endTime: Calendar.current.date(byAdding: .hour, value: 3, to: Date()) ?? Date(),
            workTypeId: UUID(),
            workDescription: "直播带货，销售护肤品",
            salary: 50,
            salaryType: .hourly,
            notes: "今天表现不错"
        )
    )
    .environmentObject(DataManager.shared)
    .environmentObject(UserPreferences.shared)
}
