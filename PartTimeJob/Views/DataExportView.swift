//
//  DataExportView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 数据导出视图
struct DataExportView: View {
    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedExportFormat: ExportFormat = .csv
    @State private var selectedDateRange: TimeRange = .thisMonth
    @State private var includeWorkTypes = true
    @State private var includeStatistics = true
    @State private var includeSettings = false
    @State private var isExporting = false
    @State private var exportProgress: Double = 0
    @State private var showingShareSheet = false
    @State private var exportedFileURL: URL?
    @State private var showingCustomDateRange = false
    @State private var customStartDate = Date()
    @State private var customEndDate = Date()
    
    var body: some View {
        NavigationView {
            List {
                // 导出格式部分
                exportFormatSection
                
                // 时间范围部分
                dateRangeSection
                
                // 导出内容部分
                exportContentSection
                
                // 数据预览部分
                dataPreviewSection
                
                // 导出操作部分
                exportActionSection
            }
            .navigationTitle("导出数据")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingShareSheet) {
                if let fileURL = exportedFileURL {
                    ShareSheet(items: [fileURL])
                }
            }
            .sheet(isPresented: $showingCustomDateRange) {
                CustomDateRangeView(
                    startDate: $customStartDate,
                    endDate: $customEndDate
                )
            }
        }
    }
    
    // MARK: - 导出格式部分
    private var exportFormatSection: some View {
        Section("导出格式") {
            ForEach(ExportFormat.allCases, id: \.self) { format in
                Button {
                    selectedExportFormat = format
                } label: {
                    HStack {
                        Image(systemName: format.icon)
                            .foregroundColor(format.color)
                            .frame(width: 24)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text(format.displayName)
                                .font(.body)
                                .foregroundColor(.primary)
                            
                            Text(format.description)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if selectedExportFormat == format {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.blue)
                        }
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    // MARK: - 时间范围部分
    private var dateRangeSection: some View {
        Section("时间范围") {
            ForEach(TimeRange.allCases, id: \.self) { range in
                Button {
                    selectedDateRange = range
                } label: {
                    HStack {
                        Text(range.displayName)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        if selectedDateRange == range {
                            Image(systemName: "checkmark")
                                .foregroundColor(.blue)
                        }
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            Button {
                showingCustomDateRange = true
            } label: {
                HStack {
                    Text("自定义范围")
                        .foregroundColor(.blue)
                    
                    Spacer()
                    
                    Image(systemName: "calendar.badge.plus")
                        .foregroundColor(.blue)
                }
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - 导出内容部分
    private var exportContentSection: some View {
        Section("导出内容") {
            Toggle(isOn: $includeWorkTypes) {
                HStack {
                    Image(systemName: "folder.fill")
                        .foregroundColor(.orange)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("工作类型")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Text("包含所有工作类型定义")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Toggle(isOn: $includeStatistics) {
                HStack {
                    Image(systemName: "chart.bar.fill")
                        .foregroundColor(.green)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("统计数据")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Text("包含计算的统计信息")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Toggle(isOn: $includeSettings) {
                HStack {
                    Image(systemName: "gearshape.fill")
                        .foregroundColor(.gray)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("应用设置")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Text("包含个人偏好设置")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
    
    // MARK: - 数据预览部分
    private var dataPreviewSection: some View {
        Section("数据预览") {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("将要导出的数据")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Spacer()
                }
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    DataPreviewCard(
                        title: "工作记录",
                        count: workRecordsCount,
                        icon: "doc.text.fill",
                        color: .blue
                    )
                    
                    if includeWorkTypes {
                        DataPreviewCard(
                            title: "工作类型",
                            count: workTypesCount,
                            icon: "folder.fill",
                            color: .orange
                        )
                    }
                    
                    if includeStatistics {
                        DataPreviewCard(
                            title: "统计项",
                            count: statisticsCount,
                            icon: "chart.bar.fill",
                            color: .green
                        )
                    }
                    
                    if includeSettings {
                        DataPreviewCard(
                            title: "设置项",
                            count: settingsCount,
                            icon: "gearshape.fill",
                            color: .gray
                        )
                    }
                }
                
                HStack {
                    Text("预计文件大小")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(estimatedFileSize)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                }
            }
        }
    }
    
    // MARK: - 导出操作部分
    private var exportActionSection: some View {
        Section {
            if isExporting {
                VStack(spacing: 12) {
                    ProgressView(value: exportProgress)
                        .progressViewStyle(LinearProgressViewStyle())
                    
                    Text("正在导出数据... \(Int(exportProgress * 100))%")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.vertical, 8)
            } else {
                Button {
                    exportData()
                } label: {
                    HStack {
                        Image(systemName: "square.and.arrow.up.fill")
                            .foregroundColor(.white)
                        
                        Text("开始导出")
                            .font(.headline)
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(12)
                }
                .buttonStyle(PlainButtonStyle())
                .disabled(workRecordsCount == 0)
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var dateRange: (start: Date, end: Date) {
        return selectedDateRange.dateRange
    }
    
    private var workRecordsCount: Int {
        let records = dataManager.fetchWorkRecords(from: dateRange.start, to: dateRange.end)
        return records.count
    }
    
    private var workTypesCount: Int {
        return dataManager.fetchWorkTypes().count
    }
    
    private var statisticsCount: Int {
        return 10 // 预设统计项数量
    }
    
    private var settingsCount: Int {
        return 15 // 预设设置项数量
    }
    
    private var estimatedFileSize: String {
        let baseSize = workRecordsCount * 200 // 每条记录约200字节
        let additionalSize = (includeWorkTypes ? 1000 : 0) +
                           (includeStatistics ? 2000 : 0) +
                           (includeSettings ? 500 : 0)
        
        let totalBytes = baseSize + additionalSize
        
        if totalBytes < 1024 {
            return "\(totalBytes) B"
        } else if totalBytes < 1024 * 1024 {
            return String(format: "%.1f KB", Double(totalBytes) / 1024)
        } else {
            return String(format: "%.1f MB", Double(totalBytes) / (1024 * 1024))
        }
    }
    
    // MARK: - 方法
    
    private func exportData() {
        isExporting = true
        exportProgress = 0
        
        Task {
            do {
                // 模拟导出进度
                for i in 1...10 {
                    try await Task.sleep(nanoseconds: 200_000_000) // 0.2秒
                    await MainActor.run {
                        exportProgress = Double(i) / 10.0
                    }
                }
                
                // 执行实际导出
                let fileURL = try await DataExporter.exportData(
                    format: selectedExportFormat,
                    dateRange: dateRange,
                    includeWorkTypes: includeWorkTypes,
                    includeStatistics: includeStatistics,
                    includeSettings: includeSettings,
                    dataManager: dataManager,
                    userPreferences: userPreferences
                )
                
                await MainActor.run {
                    exportedFileURL = fileURL
                    isExporting = false
                    showingShareSheet = true
                    CalendarHapticFeedback.successAction()
                }
                
            } catch {
                await MainActor.run {
                    isExporting = false
                    // 显示错误提示
                    CalendarHapticFeedback.errorOccurred()
                }
            }
        }
    }
}

// MARK: - 导出格式枚举
enum ExportFormat: String, CaseIterable {
    case csv = "csv"
    case json = "json"
    case excel = "excel"
    case pdf = "pdf"
    
    var displayName: String {
        switch self {
        case .csv:
            return "CSV 文件"
        case .json:
            return "JSON 文件"
        case .excel:
            return "Excel 文件"
        case .pdf:
            return "PDF 报告"
        }
    }
    
    var description: String {
        switch self {
        case .csv:
            return "逗号分隔值，适合Excel打开"
        case .json:
            return "结构化数据，适合程序处理"
        case .excel:
            return "Excel工作簿，包含图表"
        case .pdf:
            return "可打印的报告文档"
        }
    }
    
    var icon: String {
        switch self {
        case .csv:
            return "tablecells.fill"
        case .json:
            return "doc.text.fill"
        case .excel:
            return "chart.bar.doc.horizontal.fill"
        case .pdf:
            return "doc.richtext.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .csv:
            return .green
        case .json:
            return .blue
        case .excel:
            return .orange
        case .pdf:
            return .red
        }
    }
    
    var fileExtension: String {
        switch self {
        case .csv:
            return "csv"
        case .json:
            return "json"
        case .excel:
            return "xlsx"
        case .pdf:
            return "pdf"
        }
    }
}

// MARK: - 数据预览卡片
struct DataPreviewCard: View {
    let title: String
    let count: Int
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text("\(count)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

#Preview {
    DataExportView()
        .environmentObject(DataManager.shared)
        .environmentObject(UserPreferences.shared)
}
