//
//  ProfileEditView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 个人资料编辑视图
struct ProfileEditView: View {
    @EnvironmentObject private var userPreferences: UserPreferences
    @Environment(\.dismiss) private var dismiss
    
    @State private var userName: String = ""
    @State private var userTitle: String = ""
    @State private var userEmail: String = ""
    @State private var userPhone: String = ""
    @State private var userBio: String = ""
    @State private var selectedAvatar: AvatarStyle = .gradient1
    @State private var defaultSalaryType: SalaryType = .hourly
    @State private var hasChanges = false
    
    var body: some View {
        NavigationView {
            Form {
                // 头像选择部分
                avatarSection
                
                // 基本信息部分
                basicInfoSection
                
                // 联系信息部分
                contactInfoSection
                
                // 工作偏好部分
                workPreferencesSection
                
                // 个人简介部分
                bioSection
            }
            .navigationTitle("编辑资料")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveProfile()
                        dismiss()
                    }
                    .disabled(!hasChanges)
                }
            }
            .onAppear {
                loadCurrentProfile()
            }
            .onChange(of: userName) { _ in checkForChanges() }
            .onChange(of: userTitle) { _ in checkForChanges() }
            .onChange(of: userEmail) { _ in checkForChanges() }
            .onChange(of: userPhone) { _ in checkForChanges() }
            .onChange(of: userBio) { _ in checkForChanges() }
            .onChange(of: selectedAvatar) { _ in checkForChanges() }
            .onChange(of: defaultSalaryType) { _ in checkForChanges() }
        }
    }
    
    // MARK: - 头像选择部分
    private var avatarSection: some View {
        Section("头像") {
            VStack(spacing: 16) {
                // 当前头像预览
                AvatarView(
                    style: selectedAvatar,
                    name: userName.isEmpty ? "用户" : userName,
                    size: 80
                )
                
                // 头像样式选择
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(AvatarStyle.allCases, id: \.self) { style in
                            Button {
                                selectedAvatar = style
                            } label: {
                                AvatarView(
                                    style: style,
                                    name: userName.isEmpty ? "用户" : userName,
                                    size: 50
                                )
                                .overlay(
                                    Circle()
                                        .stroke(selectedAvatar == style ? Color.blue : Color.clear, lineWidth: 3)
                                )
                            }
                        }
                    }
                    .padding(.horizontal)
                }
            }
            .frame(maxWidth: .infinity)
        }
    }
    
    // MARK: - 基本信息部分
    private var basicInfoSection: some View {
        Section("基本信息") {
            HStack {
                Image(systemName: "person.fill")
                    .foregroundColor(.blue)
                    .frame(width: 20)
                
                TextField("姓名", text: $userName)
                    .textContentType(.name)
            }
            
            HStack {
                Image(systemName: "briefcase.fill")
                    .foregroundColor(.orange)
                    .frame(width: 20)
                
                TextField("职业/标题", text: $userTitle)
                    .textContentType(.jobTitle)
            }
        }
    }
    
    // MARK: - 联系信息部分
    private var contactInfoSection: some View {
        Section("联系信息") {
            HStack {
                Image(systemName: "envelope.fill")
                    .foregroundColor(.red)
                    .frame(width: 20)
                
                TextField("邮箱", text: $userEmail)
                    .textContentType(.emailAddress)
                    .keyboardType(.emailAddress)
                    .autocapitalization(.none)
            }
            
            HStack {
                Image(systemName: "phone.fill")
                    .foregroundColor(.green)
                    .frame(width: 20)
                
                TextField("电话", text: $userPhone)
                    .textContentType(.telephoneNumber)
                    .keyboardType(.phonePad)
            }
        }
    }
    
    // MARK: - 工作偏好部分
    private var workPreferencesSection: some View {
        Section("工作偏好") {
            HStack {
                Image(systemName: "dollarsign.circle.fill")
                    .foregroundColor(.green)
                    .frame(width: 20)
                
                Text("默认薪资类型")
                
                Spacer()
                
                Picker("薪资类型", selection: $defaultSalaryType) {
                    ForEach(SalaryType.allCases, id: \.self) { type in
                        Text(type.displayName).tag(type)
                    }
                }
                .pickerStyle(MenuPickerStyle())
            }
        }
    }
    
    // MARK: - 个人简介部分
    private var bioSection: some View {
        Section("个人简介") {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: "text.alignleft")
                        .foregroundColor(.purple)
                        .frame(width: 20)
                    
                    Text("关于我")
                        .font(.subheadline)
                        .foregroundColor(.primary)
                }
                
                TextField("介绍一下自己...", text: $userBio, axis: .vertical)
                    .lineLimit(3...6)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                
                Text("\(userBio.count)/200")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .trailing)
            }
        }
    }
    
    // MARK: - 方法
    
    private func loadCurrentProfile() {
        userName = userPreferences.userName
        userTitle = userPreferences.userTitle
        userEmail = userPreferences.userEmail
        userPhone = userPreferences.userPhone
        userBio = userPreferences.userBio
        selectedAvatar = userPreferences.avatarStyle
        defaultSalaryType = userPreferences.defaultSalaryType
    }
    
    private func checkForChanges() {
        hasChanges = userName != userPreferences.userName ||
                    userTitle != userPreferences.userTitle ||
                    userEmail != userPreferences.userEmail ||
                    userPhone != userPreferences.userPhone ||
                    userBio != userPreferences.userBio ||
                    selectedAvatar != userPreferences.avatarStyle ||
                    defaultSalaryType != userPreferences.defaultSalaryType
    }
    
    private func saveProfile() {
        userPreferences.userName = userName.trimmingCharacters(in: .whitespacesAndNewlines)
        userPreferences.userTitle = userTitle.trimmingCharacters(in: .whitespacesAndNewlines)
        userPreferences.userEmail = userEmail.trimmingCharacters(in: .whitespacesAndNewlines)
        userPreferences.userPhone = userPhone.trimmingCharacters(in: .whitespacesAndNewlines)
        userPreferences.userBio = String(userBio.trimmingCharacters(in: .whitespacesAndNewlines).prefix(200))
        userPreferences.avatarStyle = selectedAvatar
        userPreferences.defaultSalaryType = defaultSalaryType
        
        CalendarHapticFeedback.successAction()
    }
}

// MARK: - 头像样式枚举
enum AvatarStyle: String, CaseIterable {
    case gradient1 = "gradient1"
    case gradient2 = "gradient2"
    case gradient3 = "gradient3"
    case gradient4 = "gradient4"
    case gradient5 = "gradient5"
    case solid1 = "solid1"
    case solid2 = "solid2"
    case solid3 = "solid3"
    
    var gradient: LinearGradient {
        switch self {
        case .gradient1:
            return LinearGradient(colors: [.blue, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .gradient2:
            return LinearGradient(colors: [.pink, .orange], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .gradient3:
            return LinearGradient(colors: [.green, .blue], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .gradient4:
            return LinearGradient(colors: [.red, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .gradient5:
            return LinearGradient(colors: [.yellow, .orange], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .solid1:
            return LinearGradient(colors: [.blue], startPoint: .top, endPoint: .bottom)
        case .solid2:
            return LinearGradient(colors: [.green], startPoint: .top, endPoint: .bottom)
        case .solid3:
            return LinearGradient(colors: [.purple], startPoint: .top, endPoint: .bottom)
        }
    }
}

// MARK: - 头像视图组件
struct AvatarView: View {
    let style: AvatarStyle
    let name: String
    let size: CGFloat
    
    var body: some View {
        Circle()
            .fill(style.gradient)
            .frame(width: size, height: size)
            .overlay(
                Text(name.prefix(1).uppercased())
                    .font(.system(size: size * 0.4, weight: .bold))
                    .foregroundColor(.white)
            )
    }
}

// MARK: - 个人资料统计卡片
struct ProfileStatsCard: View {
    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences
    
    var body: some View {
        VStack(spacing: 16) {
            Text("我的统计")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                StatCard(
                    title: "总工作天数",
                    value: "\(totalWorkDays)",
                    icon: "calendar",
                    color: .blue
                )
                
                StatCard(
                    title: "总收入",
                    value: userPreferences.currencySymbol + String(format: "%.0f", totalIncome),
                    icon: "banknote",
                    color: .green
                )
                
                StatCard(
                    title: "总工时",
                    value: String(format: "%.1f小时", totalHours),
                    icon: "clock",
                    color: .orange
                )
                
                StatCard(
                    title: "平均时薪",
                    value: userPreferences.currencySymbol + String(format: "%.0f", averageHourlyRate),
                    icon: "chart.line.uptrend.xyaxis",
                    color: .purple
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var totalWorkDays: Int {
        let allRecords = dataManager.fetchAllWorkRecords()
        let uniqueDates = Set(allRecords.map { DateHelper.startOfDay(for: $0.date) })
        return uniqueDates.count
    }
    
    private var totalIncome: Double {
        let allRecords = dataManager.fetchAllWorkRecords()
        return SalaryCalculator.calculateTotalIncome(from: allRecords)
    }
    
    private var totalHours: Double {
        let allRecords = dataManager.fetchAllWorkRecords()
        return SalaryCalculator.calculateTotalWorkHours(from: allRecords)
    }
    
    private var averageHourlyRate: Double {
        let allRecords = dataManager.fetchAllWorkRecords()
        return SalaryCalculator.calculateAverageHourlyRate(from: allRecords)
    }
}

// MARK: - 统计卡片组件
struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    ProfileEditView()
        .environmentObject(UserPreferences.shared)
}
