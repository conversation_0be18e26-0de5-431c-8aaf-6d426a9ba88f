//
//  MainTabView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 主标签页视图
struct MainTabView: View {
    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences
    @StateObject private var notificationManager = NotificationManager.shared

    @State private var selectedTab = 0
    @State private var showingAddWork = false
    @State private var badgeCount = 0

    var body: some View {
        TabView(selection: $selectedTab) {
            // 首页 - 日历视图
            HomeView()
                .tabItem {
                    Image(systemName: selectedTab == 0 ? "calendar" : "calendar")
                    Text("首页")
                }
                .tag(0)

            // 统计分析
            StatisticsView()
                .tabItem {
                    Image(systemName: selectedTab == 1 ? "chart.bar.fill" : "chart.bar")
                    Text("统计")
                }
                .badge(badgeCount > 0 ? badgeCount : nil)
                .tag(1)

            // 快速添加（中间按钮）
            QuickAddWorkView()
                .tabItem {
                    Image(systemName: "plus.circle.fill")
                    Text("添加")
                }
                .tag(2)

            // 工作类型管理
            WorkTypesView()
                .tabItem {
                    Image(systemName: selectedTab == 3 ? "folder.fill" : "folder")
                    Text("类型")
                }
                .tag(3)

            // 个人设置
            SettingsView()
                .tabItem {
                    Image(systemName: selectedTab == 4 ? "gearshape.fill" : "gearshape")
                    Text("设置")
                }
                .tag(4)
        }
        .accentColor(userPreferences.themeColor)
        .preferredColorScheme(userPreferences.appTheme.colorScheme)
        .onAppear {
            setupNotifications()
            updateBadgeCount()
        }
        .onReceive(NotificationCenter.default.publisher(for: .workReminderTapped)) { _ in
            selectedTab = 0 // 跳转到首页
        }
        .onReceive(NotificationCenter.default.publisher(for: .recordReminderTapped)) { _ in
            selectedTab = 2 // 跳转到添加页面
        }
        .onReceive(NotificationCenter.default.publisher(for: .statsReminderTapped)) { _ in
            selectedTab = 1 // 跳转到统计页面
        }
    }

    // MARK: - 私有方法

    /// 设置通知
    private func setupNotifications() {
        Task {
            // 请求通知权限
            if notificationManager.authorizationStatus == .notDetermined {
                await notificationManager.requestAuthorization()
            }

            // 设置每日记录提醒
            if userPreferences.recordReminderEnabled {
                await notificationManager.scheduleDailyRecordReminder()
            }

            // 设置周度统计提醒
            await notificationManager.scheduleWeeklyStatsReminder()
        }
    }

    /// 更新徽章数量
    private func updateBadgeCount() {
        // 这里可以根据需要设置徽章数量
        // 例如：未查看的统计数据、待处理的工作等
        let todayRecords = dataManager.fetchWorkRecords(for: Date())
        badgeCount = todayRecords.isEmpty ? 1 : 0 // 如果今天没有记录，显示提醒徽章
    }
}

// MARK: - 占位视图（将在后续阶段实现）

/// 工作类型管理视图
struct WorkTypesView: View {
    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences

    @State private var workTypes: [WorkType] = []
    @State private var showingAddWorkType = false
    @State private var selectedWorkType: WorkType?
    @State private var showingEditWorkType = false
    @State private var showingDeleteAlert = false
    @State private var workTypeToDelete: WorkType?
    @State private var isLoading = false

    var body: some View {
        NavigationView {
            VStack {
                if workTypes.isEmpty && !isLoading {
                    emptyStateView
                } else {
                    workTypesList
                }
            }
            .navigationTitle("工作类型")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingAddWorkType = true
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingAddWorkType) {
                AddWorkTypeView()
            }
            .sheet(isPresented: $showingEditWorkType) {
                if let workType = selectedWorkType {
                    EditWorkTypeView(workType: workType)
                }
            }
            .alert("删除工作类型", isPresented: $showingDeleteAlert) {
                Button("取消", role: .cancel) { }
                Button("删除", role: .destructive) {
                    if let workType = workTypeToDelete {
                        deleteWorkType(workType)
                    }
                }
            } message: {
                Text("确定要删除这个工作类型吗？相关的工作记录不会被删除。")
            }
            .onAppear {
                loadWorkTypes()
            }
            .refreshable {
                loadWorkTypes()
            }
        }
    }

    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "folder.badge.plus")
                .font(.system(size: 60))
                .foregroundColor(.orange)

            VStack(spacing: 8) {
                Text("还没有工作类型")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("创建第一个工作类型来开始记录你的兼职工作")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Button {
                showingAddWorkType = true
            } label: {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("创建工作类型")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
            }

            Spacer()
        }
        .padding()
    }

    // MARK: - 工作类型列表
    private var workTypesList: some View {
        List {
            ForEach(workTypes, id: \.id) { workType in
                WorkTypeRowView(
                    workType: workType,
                    onEdit: {
                        selectedWorkType = workType
                        showingEditWorkType = true
                    },
                    onDelete: {
                        workTypeToDelete = workType
                        showingDeleteAlert = true
                    }
                )
            }
            .onMove(perform: moveWorkTypes)
        }
        .listStyle(InsetGroupedListStyle())
    }

    // MARK: - 数据操作
    private func loadWorkTypes() {
        isLoading = true
        workTypes = dataManager.fetchWorkTypes()
        isLoading = false
    }

    private func moveWorkTypes(from source: IndexSet, to destination: Int) {
        workTypes.move(fromOffsets: source, toOffset: destination)
        // 这里可以保存新的排序到数据库
        updateWorkTypeOrder()
    }

    private func updateWorkTypeOrder() {
        for (index, workType) in workTypes.enumerated() {
            // 更新排序字段（如果WorkType模型有sortOrder字段）
            // dataManager.updateWorkTypeOrder(workType.id, order: index)
        }
    }

    private func deleteWorkType(_ workType: WorkType) {
        dataManager.deleteWorkType(workType)
        loadWorkTypes()
        CalendarHapticFeedback.deleteAction()
    }
}

#Preview {
    MainTabView()
        .environmentObject(DataManager.shared)
        .environmentObject(UserPreferences.shared)
}
