//
//  AddWorkTypeView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 添加工作类型视图
struct AddWorkTypeView: View {
    @EnvironmentObject private var dataManager: DataManager
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 表单状态
    @State private var name = ""
    @State private var selectedIcon = "briefcase.fill"
    @State private var selectedColor = Color.blue
    @State private var defaultSalary: Double = 0
    @State private var salaryType: SalaryType = .hourly
    @State private var workDescription = ""
    @State private var isActive = true
    
    // MARK: - UI状态
    @State private var showingIconPicker = false
    @State private var showingColorPicker = false
    @State private var showingValidationAlert = false
    @State private var validationMessage = ""
    @State private var isLoading = false
    
    var body: some View {
        NavigationView {
            Form {
                // 基本信息部分
                basicInfoSection
                
                // 外观设置部分
                appearanceSection
                
                // 薪资设置部分
                salarySection
                
                // 其他设置部分
                otherSettingsSection
                
                // 预览部分
                previewSection
            }
            .navigationTitle("新建工作类型")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveWorkType()
                    }
                    .disabled(!isFormValid || isLoading)
                }
            }
            .alert("验证失败", isPresented: $showingValidationAlert) {
                Button("确定") { }
            } message: {
                Text(validationMessage)
            }
            .sheet(isPresented: $showingIconPicker) {
                IconPickerView(selectedIcon: $selectedIcon)
            }
            .sheet(isPresented: $showingColorPicker) {
                ColorPickerView(selectedColor: $selectedColor)
            }
        }
    }
    
    // MARK: - 基本信息部分
    private var basicInfoSection: some View {
        Section("基本信息") {
            TextField("工作类型名称", text: $name)
                .textFieldStyle(RoundedBorderTextFieldStyle())
            
            TextField("工作描述（可选）", text: $workDescription, axis: .vertical)
                .lineLimit(2...4)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
    }
    
    // MARK: - 外观设置部分
    private var appearanceSection: some View {
        Section("外观设置") {
            // 图标选择
            HStack {
                Text("图标")
                Spacer()
                Button {
                    showingIconPicker = true
                } label: {
                    HStack {
                        Image(systemName: selectedIcon)
                            .foregroundColor(selectedColor)
                        Text("选择图标")
                            .foregroundColor(.blue)
                    }
                }
            }
            
            // 颜色选择
            HStack {
                Text("颜色")
                Spacer()
                Button {
                    showingColorPicker = true
                } label: {
                    HStack {
                        Circle()
                            .fill(selectedColor)
                            .frame(width: 24, height: 24)
                        Text("选择颜色")
                            .foregroundColor(.blue)
                    }
                }
            }
        }
    }
    
    // MARK: - 薪资设置部分
    private var salarySection: some View {
        Section("薪资设置") {
            Picker("薪资类型", selection: $salaryType) {
                ForEach(SalaryType.allCases, id: \.self) { type in
                    Text(type.displayName).tag(type)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            
            HStack {
                Text("默认薪资")
                Spacer()
                TextField("0", value: $defaultSalary, format: .number)
                    .keyboardType(.decimalPad)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(width: 100)
                Text(salaryType.unit)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - 其他设置部分
    private var otherSettingsSection: some View {
        Section("其他设置") {
            Toggle("启用此工作类型", isOn: $isActive)
        }
    }
    
    // MARK: - 预览部分
    private var previewSection: some View {
        Section("预览") {
            HStack(spacing: 16) {
                // 图标预览
                ZStack {
                    Circle()
                        .fill(selectedColor)
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: selectedIcon)
                        .font(.title2)
                        .foregroundColor(.white)
                }
                
                // 信息预览
                VStack(alignment: .leading, spacing: 4) {
                    Text(name.isEmpty ? "工作类型名称" : name)
                        .font(.headline)
                        .foregroundColor(name.isEmpty ? .secondary : .primary)
                    
                    HStack {
                        Text(salaryType.displayName)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.blue.opacity(0.1))
                            .foregroundColor(.blue)
                            .cornerRadius(4)
                        
                        Text("¥\(defaultSalary, specifier: "%.0f")")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.green)
                        
                        Spacer()
                    }
                    
                    if !workDescription.isEmpty {
                        Text(workDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                }
                
                Spacer()
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - 计算属性
    private var isFormValid: Bool {
        return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               defaultSalary >= 0
    }
    
    // MARK: - 保存方法
    private func saveWorkType() {
        guard isFormValid else {
            validationMessage = "请填写完整的工作类型信息"
            showingValidationAlert = true
            return
        }
        
        isLoading = true
        
        // 检查名称是否重复
        let existingWorkTypes = dataManager.fetchWorkTypes()
        if existingWorkTypes.contains(where: { $0.name == name.trimmingCharacters(in: .whitespacesAndNewlines) }) {
            validationMessage = "工作类型名称已存在，请使用其他名称"
            showingValidationAlert = true
            isLoading = false
            return
        }
        
        // 创建新的工作类型
        let newWorkType = WorkType(
            name: name.trimmingCharacters(in: .whitespacesAndNewlines),
            iconName: selectedIcon,
            colorHex: selectedColor.toHex(),
            defaultSalary: defaultSalary,
            salaryType: salaryType,
            workDescription: workDescription.trimmingCharacters(in: .whitespacesAndNewlines),
            isActive: isActive
        )
        
        // 保存到数据库
        dataManager.addWorkType(newWorkType)
        
        isLoading = false
        CalendarHapticFeedback.successAction()
        dismiss()
    }
}

// MARK: - Color扩展

extension Color {
    func toHex() -> String {
        let uiColor = UIColor(self)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        let rgb = Int(red * 255) << 16 | Int(green * 255) << 8 | Int(blue * 255)
        return String(format: "#%06x", rgb)
    }
}

#Preview {
    AddWorkTypeView()
        .environmentObject(DataManager.shared)
}
