//
//  AddWorkRecordView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI

/// 添加工作记录视图
struct AddWorkRecordView: View {
    let selectedDate: Date
    
    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 表单状态
    @State private var workDate = Date()
    @State private var endDate = Date()
    @State private var isMultiDayMode = false
    @State private var startTime = Date()
    @State private var endTime = Date()
    @State private var selectedWorkType: WorkType?
    @State private var workDescription = ""
    @State private var salary: Double = 0
    @State private var salaryType: SalaryType = .hourly
    @State private var notes = ""
    
    // MARK: - UI状态
    @State private var showingWorkTypePicker = false
    @State private var showingTimePicker = false
    @State private var showingValidationAlert = false
    @State private var validationMessage = ""
    @State private var isLoading = false
    @State private var activeTimeField: TimeField = .start
    
    // MARK: - 计算属性
    private var workTypes: [WorkType] {
        return dataManager.fetchWorkTypes()
    }
    
    private var calculatedWorkHours: Double {
        return max(0, endTime.timeIntervalSince(startTime) / 3600)
    }
    
    private var estimatedSalary: Double {
        return SalaryCalculator.calculateActualSalary(
            salaryType: salaryType,
            baseSalary: salary,
            workHours: calculatedWorkHours
        )
    }

    private var daysBetween: Int {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: workDate, to: endDate)
        return max(1, (components.day ?? 0) + 1)
    }
    
    var body: some View {
        NavigationView {
            Form {
                // 基本信息部分
                basicInfoSection
                
                // 时间设置部分
                timeSettingsSection
                
                // 工作类型部分
                workTypeSection
                
                // 薪资设置部分
                salarySection
                
                // 工作描述部分
                descriptionSection
                
                // 预览部分
                previewSection
            }
            .navigationTitle("添加工作记录")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveWorkRecord()
                    }
                    .disabled(!isFormValid || isLoading)
                }
            }
            .onAppear {
                setupInitialValues()
            }
            .sheet(isPresented: $showingWorkTypePicker) {
                WorkTypePickerView(
                    selectedWorkType: $selectedWorkType,
                    workTypes: workTypes
                )
            }
            .sheet(isPresented: $showingTimePicker) {
                TimePickerView(
                    activeField: $activeTimeField,
                    startTime: $startTime,
                    endTime: $endTime,
                    workDate: workDate
                )
            }
            .alert("验证错误", isPresented: $showingValidationAlert) {
                Button("确定", role: .cancel) { }
            } message: {
                Text(validationMessage)
            }
        }
    }
    
    // MARK: - 基本信息部分
    private var basicInfoSection: some View {
        Section("基本信息") {
            // 多日模式切换
            Toggle("批量添加多天", isOn: $isMultiDayMode)
                .onChange(of: isMultiDayMode) { newValue in
                    if newValue {
                        endDate = Calendar.current.date(byAdding: .day, value: 6, to: workDate) ?? workDate
                    }
                }

            // 开始日期
            DatePicker(
                isMultiDayMode ? "开始日期" : "工作日期",
                selection: $workDate,
                displayedComponents: [.date]
            )
            .datePickerStyle(CompactDatePickerStyle())

            // 结束日期（多日模式时显示）
            if isMultiDayMode {
                DatePicker(
                    "结束日期",
                    selection: $endDate,
                    in: workDate...,
                    displayedComponents: [.date]
                )
                .datePickerStyle(CompactDatePickerStyle())

                // 显示天数
                HStack {
                    Text("总天数")
                    Spacer()
                    Text("\(daysBetween)天")
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    // MARK: - 时间设置部分
    private var timeSettingsSection: some View {
        Section("工作时间") {
            HStack {
                Label("开始时间", systemImage: "clock")
                Spacer()
                Button(DateHelper.hourMinuteFormatter.string(from: startTime)) {
                    activeTimeField = .start
                    showingTimePicker = true
                }
                .foregroundColor(.blue)
            }
            
            HStack {
                Label("结束时间", systemImage: "clock.fill")
                Spacer()
                Button(DateHelper.hourMinuteFormatter.string(from: endTime)) {
                    activeTimeField = .end
                    showingTimePicker = true
                }
                .foregroundColor(.blue)
            }
            
            HStack {
                Label("工作时长", systemImage: "timer")
                Spacer()
                Text(DateHelper.workDurationDescription(calculatedWorkHours * 3600))
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - 工作类型部分
    private var workTypeSection: some View {
        Section("工作类型") {
            Button {
                showingWorkTypePicker = true
            } label: {
                HStack {
                    if let workType = selectedWorkType {
                        Image(systemName: workType.iconName)
                            .foregroundColor(workType.themeColor)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text(workType.name)
                                .foregroundColor(.primary)
                            
                            Text(workType.formattedDefaultSalary)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    } else {
                        Image(systemName: "folder")
                            .foregroundColor(.gray)
                        
                        Text("选择工作类型")
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - 薪资设置部分
    private var salarySection: some View {
        Section("薪资设置") {
            Picker("薪资类型", selection: $salaryType) {
                ForEach(SalaryType.allCases, id: \.self) { type in
                    Text(type.displayName).tag(type)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            
            HStack {
                Text("薪资金额")
                Spacer()
                TextField("0", value: $salary, format: .number)
                    .keyboardType(.decimalPad)
                    .multilineTextAlignment(.trailing)
                    .frame(width: 100)
                Text(userPreferences.currencySymbol)
                    .foregroundColor(.secondary)
            }
            
            if salaryType == .hourly {
                HStack {
                    Text("预计收入")
                    Spacer()
                    Text(userPreferences.currencySymbol + String(format: "%.2f", estimatedSalary))
                        .foregroundColor(.green)
                        .fontWeight(.semibold)
                }
            }
        }
    }
    
    // MARK: - 工作描述部分
    private var descriptionSection: some View {
        Section("工作描述") {
            TextField("请输入工作内容", text: $workDescription, axis: .vertical)
                .lineLimit(3...6)
            
            TextField("备注（可选）", text: $notes, axis: .vertical)
                .lineLimit(2...4)
        }
    }
    
    // MARK: - 预览部分
    private var previewSection: some View {
        Section("预览") {
            WorkRecordPreviewCard(
                workType: selectedWorkType,
                workDescription: workDescription,
                startTime: startTime,
                endTime: endTime,
                salaryType: salaryType,
                salary: salary,
                estimatedSalary: estimatedSalary,
                workHours: calculatedWorkHours
            )
        }
    }
    
    // MARK: - 计算属性
    private var isFormValid: Bool {
        return selectedWorkType != nil &&
               !workDescription.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               salary > 0 &&
               startTime < endTime &&
               workDate <= Date() // 不能添加未来的工作记录
    }

    private var formValidationErrors: [String] {
        var errors: [String] = []

        if selectedWorkType == nil {
            errors.append("请选择工作类型")
        }

        if workDescription.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append("请输入工作描述")
        }

        if salary <= 0 {
            errors.append("薪资必须大于0")
        }

        if startTime >= endTime {
            errors.append("结束时间必须晚于开始时间")
        }

        if workDate > Date() {
            errors.append("不能添加未来的工作记录")
        }

        let workHours = endTime.timeIntervalSince(startTime) / 3600
        if workHours > 24 {
            errors.append("工作时长不能超过24小时")
        }

        if workHours < 0.1 {
            errors.append("工作时长至少6分钟")
        }

        return errors
    }
    
    // MARK: - 方法
    
    private func setupInitialValues() {
        workDate = selectedDate
        
        // 设置默认时间
        let calendar = Calendar.current
        let now = Date()
        
        if calendar.isDate(selectedDate, inSameDayAs: now) {
            // 如果是今天，使用当前时间
            startTime = now
            endTime = calendar.date(byAdding: .hour, value: 1, to: now) ?? now
        } else {
            // 如果是其他日期，使用默认时间
            startTime = calendar.date(bySettingHour: 9, minute: 0, second: 0, of: selectedDate) ?? selectedDate
            endTime = calendar.date(bySettingHour: 17, minute: 0, second: 0, of: selectedDate) ?? selectedDate
        }
        
        // 设置默认薪资类型
        salaryType = userPreferences.defaultSalaryType
        
        // 如果只有一个工作类型，自动选择
        if workTypes.count == 1 {
            selectedWorkType = workTypes.first
            salary = selectedWorkType?.defaultSalary ?? 0
        }
    }
    
    private func saveWorkRecord() {
        guard let workType = selectedWorkType else { return }

        isLoading = true

        if isMultiDayMode {
            saveMultiDayWorkRecords(workType: workType)
        } else {
            saveSingleWorkRecord(workType: workType)
        }
    }

    private func saveSingleWorkRecord(workType: WorkType) {
        // 验证数据
        let validationResult = ValidationService.validateWorkRecord(
            date: workDate,
            startTime: startTime,
            endTime: endTime,
            workTypeId: workType.id,
            workDescription: workDescription,
            salary: salary,
            salaryType: salaryType
        )

        if !validationResult.isValid {
            validationMessage = validationResult.errorMessage
            showingValidationAlert = true
            isLoading = false
            return
        }

        // 检查时间冲突
        let existingRecords = dataManager.fetchWorkRecords(for: workDate)
        let conflictResult = ValidationService.validateTimeConflict(
            date: workDate,
            startTime: startTime,
            endTime: endTime,
            existingRecords: existingRecords
        )

        if !conflictResult.isValid {
            validationMessage = conflictResult.errorMessage
            showingValidationAlert = true
            isLoading = false
            return
        }

        // 创建工作记录
        let workRecord = WorkRecord(
            date: workDate,
            startTime: startTime,
            endTime: endTime,
            workTypeId: workType.id,
            workDescription: ValidationService.sanitizeWorkDescription(workDescription),
            salary: salary,
            salaryType: salaryType,
            notes: notes.trimmingCharacters(in: .whitespacesAndNewlines)
        )

        // 保存到数据库
        dataManager.addWorkRecord(workRecord)

        // 设置工作提醒
        Task {
            await NotificationManager.shared.scheduleWorkReminder(for: workRecord)
        }

        isLoading = false
        dismiss()
    }

    private func saveMultiDayWorkRecords(workType: WorkType) {
        let calendar = Calendar.current
        var currentDate = workDate
        var successCount = 0
        var conflictDates: [Date] = []

        while currentDate <= endDate {
            // 验证当前日期的数据
            let validationResult = ValidationService.validateWorkRecord(
                date: currentDate,
                startTime: startTime,
                endTime: endTime,
                workTypeId: workType.id,
                workDescription: workDescription,
                salary: salary,
                salaryType: salaryType
            )

            if validationResult.isValid {
                // 检查时间冲突
                let existingRecords = dataManager.fetchWorkRecords(for: currentDate)
                let conflictResult = ValidationService.validateTimeConflict(
                    date: currentDate,
                    startTime: startTime,
                    endTime: endTime,
                    existingRecords: existingRecords
                )

                if conflictResult.isValid {
                    // 创建工作记录
                    let workRecord = WorkRecord(
                        date: currentDate,
                        startTime: startTime,
                        endTime: endTime,
                        workTypeId: workType.id,
                        workDescription: ValidationService.sanitizeWorkDescription(workDescription),
                        salary: salary,
                        salaryType: salaryType,
                        notes: notes.trimmingCharacters(in: .whitespacesAndNewlines)
                    )

                    dataManager.addWorkRecord(workRecord)

                    // 设置工作提醒
                    Task {
                        await NotificationManager.shared.scheduleWorkReminder(for: workRecord)
                    }

                    successCount += 1
                } else {
                    conflictDates.append(currentDate)
                }
            }

            // 移动到下一天
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
            if currentDate > endDate { break }
        }

        isLoading = false

        // 显示结果
        if conflictDates.isEmpty {
            // 全部成功
            CalendarHapticFeedback.successAction()
            dismiss()
        } else {
            // 部分成功，显示冲突信息
            let conflictDatesString = conflictDates.map { DateHelper.yearMonthDayFormatter.string(from: $0) }.joined(separator: ", ")
            validationMessage = "成功添加\(successCount)条记录。以下日期存在时间冲突：\(conflictDatesString)"
            showingValidationAlert = true
        }
    }
}

// MARK: - 时间字段枚举
enum TimeField {
    case start
    case end
}

#Preview {
    AddWorkRecordView(selectedDate: Date())
        .environmentObject(DataManager.shared)
        .environmentObject(UserPreferences.shared)
}
