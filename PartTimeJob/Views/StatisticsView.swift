//
//  StatisticsView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI
import Charts

/// 统计分析视图
struct StatisticsView: View {
    @EnvironmentObject private var dataManager: DataManager
    @EnvironmentObject private var userPreferences: UserPreferences
    
    @State private var selectedTimeRange: TimeRange = .thisMonth
    @State private var showingExportOptions = false
    @State private var isLoading = false
    @State private var selectedChartType: ChartType = .income
    @State private var incomeChartData: [IncomeChartData] = []
    @State private var workTypeChartData: [WorkTypeChartData] = []
    @State private var trendChartData: [TrendChartData] = []
    @State private var isLoadingCharts = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // 时间范围选择器
                    timeRangeSelector
                    
                    // 核心统计卡片
                    coreStatsSection

                    // 图表选择器
                    chartTypeSelector

                    // 图表展示
                    chartSection

                    // 收入趋势图表
                    incomeTrendSection
                    
                    // 工作类型分布
                    workTypeDistributionSection
                    
                    // 效率分析
                    efficiencyAnalysisSection
                }
                .padding()
            }
            .navigationTitle("统计分析")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingExportOptions = true
                    } label: {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
            }
            .sheet(isPresented: $showingExportOptions) {
                ExportOptionsView()
            }
            .refreshable {
                await refreshData()
            }
        }
    }
    
    // MARK: - 时间范围选择器
    private var timeRangeSelector: some View {
        Picker("时间范围", selection: $selectedTimeRange) {
            ForEach(TimeRange.allCases, id: \.self) { range in
                Text(range.displayName).tag(range)
            }
        }
        .pickerStyle(SegmentedPickerStyle())
        .padding(.horizontal)
    }
    
    // MARK: - 核心统计卡片
    private var coreStatsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("核心数据")
                .font(.headline)
                .padding(.horizontal)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                StatCard(
                    title: "总收入",
                    value: totalIncomeText,
                    subtitle: "较上期 +12.5%",
                    color: .green,
                    icon: "dollarsign.circle.fill"
                )
                
                StatCard(
                    title: "总工时",
                    value: totalHoursText,
                    subtitle: "较上期 +8.3%",
                    color: .blue,
                    icon: "clock.fill"
                )
                
                StatCard(
                    title: "平均时薪",
                    value: averageHourlyRateText,
                    subtitle: "较上期 +3.8%",
                    color: .orange,
                    icon: "chart.line.uptrend.xyaxis"
                )
                
                StatCard(
                    title: "工作天数",
                    value: workDaysText,
                    subtitle: "本期记录",
                    color: .purple,
                    icon: "calendar.badge.checkmark"
                )
            }
            .padding(.horizontal)
        }
    }
    
    // MARK: - 收入趋势图表
    private var incomeTrendSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("收入趋势")
                .font(.headline)
                .padding(.horizontal)
            
            VStack {
                // 这里将在后续阶段实现图表
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
                    .frame(height: 200)
                    .overlay(
                        VStack {
                            Image(systemName: "chart.line.uptrend.xyaxis")
                                .font(.system(size: 40))
                                .foregroundColor(.gray)
                            
                            Text("收入趋势图表")
                                .font(.headline)
                                .foregroundColor(.secondary)
                            
                            Text("将在后续阶段实现")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    )
            }
            .padding(.horizontal)
        }
    }
    
    // MARK: - 工作类型分布
    private var workTypeDistributionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("工作类型分布")
                .font(.headline)
                .padding(.horizontal)
            
            VStack(spacing: 8) {
                ForEach(workTypeIncomes, id: \.workType.id) { typeIncome in
                    WorkTypeIncomeRow(
                        workType: typeIncome.workType,
                        income: typeIncome.totalIncome,
                        percentage: typeIncome.totalIncome / totalIncome * 100,
                        recordCount: typeIncome.recordCount
                    )
                }
            }
            .padding(.horizontal)
        }
    }
    
    // MARK: - 效率分析
    private var efficiencyAnalysisSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("效率分析")
                .font(.headline)
                .padding(.horizontal)
            
            VStack(spacing: 12) {
                EfficiencyCard(
                    title: "最佳工作时段",
                    value: bestWorkingHour,
                    description: "收入最高的工作时间",
                    icon: "clock.badge.checkmark"
                )
                
                EfficiencyCard(
                    title: "效率趋势",
                    value: efficiencyTrend,
                    description: "相比上期的效率变化",
                    icon: "chart.line.uptrend.xyaxis"
                )
                
                EfficiencyCard(
                    title: "预测收入",
                    value: predictedIncome,
                    description: "基于历史数据预测下月收入",
                    icon: "crystal.ball"
                )
            }
            .padding(.horizontal)
        }
    }
    
    // MARK: - 计算属性
    
    private var dateRange: (start: Date, end: Date) {
        return selectedTimeRange.dateRange
    }
    
    private var workRecords: [WorkRecord] {
        return dataManager.fetchWorkRecords(from: dateRange.start, to: dateRange.end)
    }
    
    private var totalIncome: Double {
        return SalaryCalculator.calculateTotalIncome(from: workRecords)
    }
    
    private var totalIncomeText: String {
        return userPreferences.currencySymbol + String(format: "%.0f", totalIncome)
    }
    
    private var totalHoursText: String {
        let hours = SalaryCalculator.calculateTotalWorkHours(from: workRecords)
        return String(format: "%.1f小时", hours)
    }
    
    private var averageHourlyRateText: String {
        let rate = SalaryCalculator.calculateAverageHourlyRate(from: workRecords)
        return userPreferences.currencySymbol + String(format: "%.0f/时", rate)
    }
    
    private var workDaysText: String {
        let days = dataManager.getWorkDaysCount(from: dateRange.start, to: dateRange.end)
        return "\(days)天"
    }
    
    private var workTypeIncomes: [WorkTypeIncome] {
        let workTypes = dataManager.fetchWorkTypes()
        return SalaryCalculator.calculateIncomeByWorkType(from: workRecords, workTypes: workTypes)
    }
    
    private var bestWorkingHour: String {
        let optimal = SalaryCalculator.calculateOptimalWorkingHours(from: workRecords)
        return "\(optimal.bestPayingHour):00"
    }
    
    private var efficiencyTrend: String {
        let trend = SalaryCalculator.calculateEfficiencyTrend(from: workRecords)
        return trend.description
    }
    
    private var predictedIncome: String {
        let projection = SalaryCalculator.predictFutureIncome(from: workRecords, daysToPredict: 30)
        return userPreferences.currencySymbol + String(format: "%.0f", projection.projectedIncome)
    }
    
    // MARK: - 方法
    
    private func refreshData() async {
        isLoading = true
        // 这里可以添加数据刷新逻辑
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 模拟加载
        isLoading = false
    }
}

// MARK: - 时间范围枚举
enum TimeRange: CaseIterable {
    case thisWeek
    case thisMonth
    case thisYear
    case last30Days
    case last90Days
    case allTime
    
    var displayName: String {
        switch self {
        case .thisWeek:
            return "本周"
        case .thisMonth:
            return "本月"
        case .thisYear:
            return "今年"
        case .last30Days:
            return "30天"
        case .last90Days:
            return "90天"
        case .allTime:
            return "全部"
        }
    }
    
    var dateRange: (start: Date, end: Date) {
        let now = Date()
        let calendar = Calendar.current
        
        switch self {
        case .thisWeek:
            return (DateHelper.startOfWeek(for: now), DateHelper.endOfWeek(for: now))
        case .thisMonth:
            return (DateHelper.startOfMonth(for: now), DateHelper.endOfMonth(for: now))
        case .thisYear:
            return (DateHelper.startOfYear(for: now), DateHelper.endOfYear(for: now))
        case .last30Days:
            let start = calendar.date(byAdding: .day, value: -30, to: now) ?? now
            return (start, now)
        case .last90Days:
            let start = calendar.date(byAdding: .day, value: -90, to: now) ?? now
            return (start, now)
        case .allTime:
            let start = calendar.date(byAdding: .year, value: -10, to: now) ?? now
            return (start, now)
        }
    }
}

// MARK: - 占位视图
struct ExportOptionsView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("数据导出")
                    .font(.title)
                
                Spacer()
                
                Text("此功能将在后续阶段实现")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .navigationTitle("导出选项")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
    }

    // MARK: - 图表选择器
    private var chartTypeSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("数据图表")
                .font(.headline)
                .foregroundColor(.primary)

            Picker("图表类型", selection: $selectedChartType) {
                ForEach(ChartType.allCases, id: \.self) { type in
                    Text(type.displayName).tag(type)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .onChange(of: selectedChartType) { _ in
                loadChartData()
            }
        }
        .responsiveCardPadding()
        .lightCardStyle()
    }

    // MARK: - 图表展示
    private var chartSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            switch selectedChartType {
            case .income:
                incomeChart
            case .workType:
                workTypeChart
            case .trend:
                trendChart
            }
        }
        .responsiveCardPadding()
        .lightCardStyle()
    }

    // MARK: - 收入图表
    private var incomeChart: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("收入统计")
                .font(.headline)
                .foregroundColor(.primary)

            if incomeChartData.isEmpty {
                Text("暂无数据")
                    .foregroundColor(.secondary)
                    .frame(height: 200)
                    .frame(maxWidth: .infinity)
            } else {
                Chart(incomeChartData, id: \.date) { data in
                    BarMark(
                        x: .value("日期", data.date),
                        y: .value("收入", data.income)
                    )
                    .foregroundStyle(.blue)
                }
                .frame(height: 200)
                .chartXAxis {
                    AxisMarks(values: .stride(by: .day, count: 7)) { value in
                        AxisGridLine()
                        AxisValueLabel(format: .dateTime.month().day())
                    }
                }
                .chartYAxis {
                    AxisMarks { value in
                        AxisGridLine()
                        AxisValueLabel()
                    }
                }
            }
        }
    }

    // MARK: - 工作类型分布图表
    private var workTypeChart: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("工作类型分布")
                .font(.headline)
                .foregroundColor(.primary)

            if workTypeChartData.isEmpty {
                Text("暂无数据")
                    .foregroundColor(.secondary)
                    .frame(height: 200)
                    .frame(maxWidth: .infinity)
            } else {
                Chart(workTypeChartData, id: \.workTypeName) { data in
                    SectorMark(
                        angle: .value("收入", data.income),
                        innerRadius: .ratio(0.5),
                        angularInset: 2
                    )
                    .foregroundStyle(data.color)
                    .opacity(0.8)
                }
                .frame(height: 200)
                .chartLegend(position: .bottom, alignment: .center)
            }
        }
    }

    // MARK: - 趋势图表
    private var trendChart: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("收入趋势")
                .font(.headline)
                .foregroundColor(.primary)

            if trendChartData.isEmpty {
                Text("暂无数据")
                    .foregroundColor(.secondary)
                    .frame(height: 200)
                    .frame(maxWidth: .infinity)
            } else {
                Chart(trendChartData, id: \.date) { data in
                    LineMark(
                        x: .value("日期", data.date),
                        y: .value("收入", data.income)
                    )
                    .foregroundStyle(.green)
                    .lineStyle(StrokeStyle(lineWidth: 3))

                    AreaMark(
                        x: .value("日期", data.date),
                        y: .value("收入", data.income)
                    )
                    .foregroundStyle(.green.opacity(0.2))
                }
                .frame(height: 200)
                .chartXAxis {
                    AxisMarks(values: .stride(by: .day, count: 7)) { value in
                        AxisGridLine()
                        AxisValueLabel(format: .dateTime.month().day())
                    }
                }
                .chartYAxis {
                    AxisMarks { value in
                        AxisGridLine()
                        AxisValueLabel()
                    }
                }
            }
        }
    }

    // MARK: - 图表数据加载
    private func loadChartData() {
        isLoadingCharts = true

        Task {
            await MainActor.run {
                switch selectedChartType {
                case .income:
                    loadIncomeChartData()
                case .workType:
                    loadWorkTypeChartData()
                case .trend:
                    loadTrendChartData()
                }
                isLoadingCharts = false
            }
        }
    }

    private func loadIncomeChartData() {
        let records = getFilteredRecords()
        let calendar = Calendar.current

        // 按日期分组计算收入
        let groupedRecords = Dictionary(grouping: records) { record in
            calendar.startOfDay(for: record.date)
        }

        incomeChartData = groupedRecords.map { date, records in
            let totalIncome = records.reduce(0) { $0 + $1.actualSalary }
            return IncomeChartData(date: date, income: totalIncome)
        }.sorted { $0.date < $1.date }
    }

    private func loadWorkTypeChartData() {
        let records = getFilteredRecords()
        let workTypes = dataManager.fetchWorkTypes()

        // 按工作类型分组计算收入
        let groupedRecords = Dictionary(grouping: records) { $0.workTypeId }

        workTypeChartData = groupedRecords.compactMap { workTypeId, records in
            guard let workType = workTypes.first(where: { $0.id == workTypeId }) else { return nil }
            let totalIncome = records.reduce(0) { $0 + $1.actualSalary }
            return WorkTypeChartData(
                workTypeName: workType.name,
                income: totalIncome,
                color: workType.themeColor
            )
        }.sorted { $0.income > $1.income }
    }

    private func loadTrendChartData() {
        let records = getFilteredRecords()
        let calendar = Calendar.current

        // 按周分组计算收入趋势
        let groupedRecords = Dictionary(grouping: records) { record in
            calendar.dateInterval(of: .weekOfYear, for: record.date)?.start ?? record.date
        }

        trendChartData = groupedRecords.map { date, records in
            let totalIncome = records.reduce(0) { $0 + $1.actualSalary }
            return TrendChartData(date: date, income: totalIncome)
        }.sorted { $0.date < $1.date }
    }
}

// MARK: - 图表数据结构

enum ChartType: CaseIterable {
    case income
    case workType
    case trend

    var displayName: String {
        switch self {
        case .income:
            return "收入"
        case .workType:
            return "类型"
        case .trend:
            return "趋势"
        }
    }
}

struct IncomeChartData {
    let date: Date
    let income: Double
}

struct WorkTypeChartData {
    let workTypeName: String
    let income: Double
    let color: Color
}

struct TrendChartData {
    let date: Date
    let income: Double
}

#Preview {
    StatisticsView()
        .environmentObject(DataManager.shared)
        .environmentObject(UserPreferences.shared)
}
