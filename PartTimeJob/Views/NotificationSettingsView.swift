//
//  NotificationSettingsView.swift
//  PartTimeJob
//
//  Created by iOS Expert on 2025/8/29.
//

import SwiftUI
import UserNotifications

/// 通知设置视图
struct NotificationSettingsView: View {
    @EnvironmentObject private var userPreferences: UserPreferences
    @Environment(\.dismiss) private var dismiss
    
    @State private var notificationPermissionStatus: UNAuthorizationStatus = .notDetermined
    @State private var showingPermissionAlert = false
    @State private var enableWorkReminders = false
    @State private var enableDailyReports = false
    @State private var enableWeeklyReports = false
    @State private var reminderTime = Date()
    @State private var reportTime = Date()
    @State private var selectedReminderTypes: Set<ReminderType> = []
    
    var body: some View {
        NavigationView {
            List {
                // 权限状态部分
                permissionStatusSection
                
                // 工作提醒部分
                if notificationPermissionStatus == .authorized {
                    workRemindersSection
                    
                    // 报告通知部分
                    reportsSection
                    
                    // 提醒类型部分
                    reminderTypesSection
                    
                    // 测试通知部分
                    testNotificationSection
                }
            }
            .navigationTitle("通知设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        saveNotificationSettings()
                        dismiss()
                    }
                }
            }
            .onAppear {
                loadNotificationSettings()
                checkNotificationPermission()
            }
            .alert("需要通知权限", isPresented: $showingPermissionAlert) {
                Button("去设置") {
                    openAppSettings()
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("请在设置中允许通知权限，以便接收工作提醒。")
            }
        }
    }
    
    // MARK: - 权限状态部分
    private var permissionStatusSection: some View {
        Section("通知权限") {
            HStack {
                Image(systemName: permissionStatusIcon)
                    .foregroundColor(permissionStatusColor)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("通知状态")
                        .font(.body)
                        .foregroundColor(.primary)
                    
                    Text(permissionStatusText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if notificationPermissionStatus == .denied {
                    Button("设置") {
                        openAppSettings()
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                } else if notificationPermissionStatus == .notDetermined {
                    Button("请求权限") {
                        requestNotificationPermission()
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
        }
    }
    
    // MARK: - 工作提醒部分
    private var workRemindersSection: some View {
        Section("工作提醒") {
            Toggle(isOn: $enableWorkReminders) {
                HStack {
                    Image(systemName: "alarm.fill")
                        .foregroundColor(.orange)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("工作开始提醒")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Text("在工作开始前提醒")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            if enableWorkReminders {
                HStack {
                    Image(systemName: "clock.fill")
                        .foregroundColor(.blue)
                        .frame(width: 24)
                    
                    Text("提前提醒时间")
                    
                    Spacer()
                    
                    Picker("提醒时间", selection: $userPreferences.notificationMinutesBefore) {
                        Text("5分钟").tag(5)
                        Text("10分钟").tag(10)
                        Text("15分钟").tag(15)
                        Text("30分钟").tag(30)
                        Text("60分钟").tag(60)
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                HStack {
                    Image(systemName: "speaker.wave.2.fill")
                        .foregroundColor(.green)
                        .frame(width: 24)
                    
                    Text("提醒声音")
                    
                    Spacer()
                    
                    Picker("声音", selection: $userPreferences.notificationSound) {
                        Text("默认").tag("default")
                        Text("铃声").tag("bell")
                        Text("提示音").tag("chime")
                        Text("静音").tag("silent")
                    }
                    .pickerStyle(MenuPickerStyle())
                }
            }
        }
    }
    
    // MARK: - 报告通知部分
    private var reportsSection: some View {
        Section("定期报告") {
            Toggle(isOn: $enableDailyReports) {
                HStack {
                    Image(systemName: "doc.text.fill")
                        .foregroundColor(.purple)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("每日工作报告")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Text("每天晚上发送工作总结")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            if enableDailyReports {
                DatePicker(
                    "报告时间",
                    selection: $reportTime,
                    displayedComponents: [.hourAndMinute]
                )
                .datePickerStyle(CompactDatePickerStyle())
            }
            
            Toggle(isOn: $enableWeeklyReports) {
                HStack {
                    Image(systemName: "chart.bar.fill")
                        .foregroundColor(.teal)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("每周工作报告")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Text("每周日发送工作统计")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
    
    // MARK: - 提醒类型部分
    private var reminderTypesSection: some View {
        Section("提醒类型") {
            ForEach(ReminderType.allCases, id: \.self) { type in
                Toggle(isOn: Binding(
                    get: { selectedReminderTypes.contains(type) },
                    set: { isSelected in
                        if isSelected {
                            selectedReminderTypes.insert(type)
                        } else {
                            selectedReminderTypes.remove(type)
                        }
                    }
                )) {
                    HStack {
                        Image(systemName: type.icon)
                            .foregroundColor(type.color)
                            .frame(width: 24)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text(type.title)
                                .font(.body)
                                .foregroundColor(.primary)
                            
                            Text(type.description)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - 测试通知部分
    private var testNotificationSection: some View {
        Section("测试") {
            Button {
                sendTestNotification()
            } label: {
                HStack {
                    Image(systemName: "bell.badge.fill")
                        .foregroundColor(.blue)
                        .frame(width: 24)
                    
                    Text("发送测试通知")
                        .font(.body)
                        .foregroundColor(.blue)
                    
                    Spacer()
                }
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - 计算属性
    
    private var permissionStatusIcon: String {
        switch notificationPermissionStatus {
        case .authorized:
            return "checkmark.circle.fill"
        case .denied:
            return "xmark.circle.fill"
        case .notDetermined:
            return "questionmark.circle.fill"
        case .provisional:
            return "clock.circle.fill"
        case .ephemeral:
            return "timer.circle.fill"
        @unknown default:
            return "questionmark.circle.fill"
        }
    }
    
    private var permissionStatusColor: Color {
        switch notificationPermissionStatus {
        case .authorized:
            return .green
        case .denied:
            return .red
        case .notDetermined:
            return .orange
        case .provisional:
            return .yellow
        case .ephemeral:
            return .blue
        @unknown default:
            return .gray
        }
    }
    
    private var permissionStatusText: String {
        switch notificationPermissionStatus {
        case .authorized:
            return "已授权，可以接收通知"
        case .denied:
            return "已拒绝，请在设置中开启"
        case .notDetermined:
            return "未设置，点击请求权限"
        case .provisional:
            return "临时授权"
        case .ephemeral:
            return "临时权限"
        @unknown default:
            return "未知状态"
        }
    }
    
    // MARK: - 方法
    
    private func loadNotificationSettings() {
        enableWorkReminders = userPreferences.enableNotifications
        enableDailyReports = userPreferences.enableDailyReports
        enableWeeklyReports = userPreferences.enableWeeklyReports
        
        // 设置默认时间
        let calendar = Calendar.current
        reportTime = calendar.date(bySettingHour: 21, minute: 0, second: 0, of: Date()) ?? Date()
        
        // 加载提醒类型
        selectedReminderTypes = Set(userPreferences.enabledReminderTypes.compactMap { ReminderType(rawValue: $0) })
    }
    
    private func checkNotificationPermission() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                notificationPermissionStatus = settings.authorizationStatus
            }
        }
    }
    
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                if granted {
                    notificationPermissionStatus = .authorized
                } else {
                    notificationPermissionStatus = .denied
                    showingPermissionAlert = true
                }
            }
        }
    }
    
    private func openAppSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
    
    private func saveNotificationSettings() {
        userPreferences.enableNotifications = enableWorkReminders
        userPreferences.enableDailyReports = enableDailyReports
        userPreferences.enableWeeklyReports = enableWeeklyReports
        userPreferences.enabledReminderTypes = Array(selectedReminderTypes.map { $0.rawValue })
        
        // 更新通知管理器设置
        Task {
            await NotificationManager.shared.updateNotificationSettings()
        }
        
        CalendarHapticFeedback.successAction()
    }
    
    private func sendTestNotification() {
        Task {
            await NotificationManager.shared.sendTestNotification()
        }
        CalendarHapticFeedback.successAction()
    }
}

// MARK: - 提醒类型枚举
enum ReminderType: String, CaseIterable {
    case workStart = "workStart"
    case workEnd = "workEnd"
    case breakTime = "breakTime"
    case dailySummary = "dailySummary"
    case weeklyGoal = "weeklyGoal"
    
    var title: String {
        switch self {
        case .workStart:
            return "工作开始提醒"
        case .workEnd:
            return "工作结束提醒"
        case .breakTime:
            return "休息时间提醒"
        case .dailySummary:
            return "每日总结"
        case .weeklyGoal:
            return "周目标提醒"
        }
    }
    
    var description: String {
        switch self {
        case .workStart:
            return "在工作开始前提醒"
        case .workEnd:
            return "在工作结束时提醒"
        case .breakTime:
            return "提醒适当休息"
        case .dailySummary:
            return "每日工作总结通知"
        case .weeklyGoal:
            return "每周目标达成情况"
        }
    }
    
    var icon: String {
        switch self {
        case .workStart:
            return "play.circle.fill"
        case .workEnd:
            return "stop.circle.fill"
        case .breakTime:
            return "pause.circle.fill"
        case .dailySummary:
            return "doc.text.fill"
        case .weeklyGoal:
            return "target"
        }
    }
    
    var color: Color {
        switch self {
        case .workStart:
            return .green
        case .workEnd:
            return .red
        case .breakTime:
            return .orange
        case .dailySummary:
            return .blue
        case .weeklyGoal:
            return .purple
        }
    }
}

#Preview {
    NotificationSettingsView()
        .environmentObject(UserPreferences.shared)
}
