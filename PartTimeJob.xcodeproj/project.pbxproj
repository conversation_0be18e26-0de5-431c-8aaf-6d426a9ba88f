// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		C9AE225D2E61588900E1D2D2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C9AE22442E61588700E1D2D2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C9AE224B2E61588700E1D2D2;
			remoteInfo = PartTimeJob;
		};
		C9AE22672E61588900E1D2D2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C9AE22442E61588700E1D2D2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C9AE224B2E61588700E1D2D2;
			remoteInfo = PartTimeJob;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		C9AE224C2E61588700E1D2D2 /* PartTimeJob.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PartTimeJob.app; sourceTree = BUILT_PRODUCTS_DIR; };
		C9AE225C2E61588900E1D2D2 /* PartTimeJobTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PartTimeJobTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		C9AE22662E61588900E1D2D2 /* PartTimeJobUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PartTimeJobUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		C9AE224E2E61588700E1D2D2 /* PartTimeJob */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PartTimeJob;
			sourceTree = "<group>";
		};
		C9AE225F2E61588900E1D2D2 /* PartTimeJobTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PartTimeJobTests;
			sourceTree = "<group>";
		};
		C9AE22692E61588900E1D2D2 /* PartTimeJobUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PartTimeJobUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		C9AE22492E61588700E1D2D2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9AE22592E61588900E1D2D2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9AE22632E61588900E1D2D2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		C9AE22432E61588700E1D2D2 = {
			isa = PBXGroup;
			children = (
				C9AE224E2E61588700E1D2D2 /* PartTimeJob */,
				C9AE225F2E61588900E1D2D2 /* PartTimeJobTests */,
				C9AE22692E61588900E1D2D2 /* PartTimeJobUITests */,
				C9AE224D2E61588700E1D2D2 /* Products */,
			);
			sourceTree = "<group>";
		};
		C9AE224D2E61588700E1D2D2 /* Products */ = {
			isa = PBXGroup;
			children = (
				C9AE224C2E61588700E1D2D2 /* PartTimeJob.app */,
				C9AE225C2E61588900E1D2D2 /* PartTimeJobTests.xctest */,
				C9AE22662E61588900E1D2D2 /* PartTimeJobUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		C9AE224B2E61588700E1D2D2 /* PartTimeJob */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C9AE22702E61588900E1D2D2 /* Build configuration list for PBXNativeTarget "PartTimeJob" */;
			buildPhases = (
				C9AE22482E61588700E1D2D2 /* Sources */,
				C9AE22492E61588700E1D2D2 /* Frameworks */,
				C9AE224A2E61588700E1D2D2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				C9AE224E2E61588700E1D2D2 /* PartTimeJob */,
			);
			name = PartTimeJob;
			packageProductDependencies = (
			);
			productName = PartTimeJob;
			productReference = C9AE224C2E61588700E1D2D2 /* PartTimeJob.app */;
			productType = "com.apple.product-type.application";
		};
		C9AE225B2E61588900E1D2D2 /* PartTimeJobTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C9AE22732E61588900E1D2D2 /* Build configuration list for PBXNativeTarget "PartTimeJobTests" */;
			buildPhases = (
				C9AE22582E61588900E1D2D2 /* Sources */,
				C9AE22592E61588900E1D2D2 /* Frameworks */,
				C9AE225A2E61588900E1D2D2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C9AE225E2E61588900E1D2D2 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				C9AE225F2E61588900E1D2D2 /* PartTimeJobTests */,
			);
			name = PartTimeJobTests;
			packageProductDependencies = (
			);
			productName = PartTimeJobTests;
			productReference = C9AE225C2E61588900E1D2D2 /* PartTimeJobTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		C9AE22652E61588900E1D2D2 /* PartTimeJobUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C9AE22762E61588900E1D2D2 /* Build configuration list for PBXNativeTarget "PartTimeJobUITests" */;
			buildPhases = (
				C9AE22622E61588900E1D2D2 /* Sources */,
				C9AE22632E61588900E1D2D2 /* Frameworks */,
				C9AE22642E61588900E1D2D2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C9AE22682E61588900E1D2D2 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				C9AE22692E61588900E1D2D2 /* PartTimeJobUITests */,
			);
			name = PartTimeJobUITests;
			packageProductDependencies = (
			);
			productName = PartTimeJobUITests;
			productReference = C9AE22662E61588900E1D2D2 /* PartTimeJobUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		C9AE22442E61588700E1D2D2 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
				TargetAttributes = {
					C9AE224B2E61588700E1D2D2 = {
						CreatedOnToolsVersion = 16.0;
					};
					C9AE225B2E61588900E1D2D2 = {
						CreatedOnToolsVersion = 16.0;
						TestTargetID = C9AE224B2E61588700E1D2D2;
					};
					C9AE22652E61588900E1D2D2 = {
						CreatedOnToolsVersion = 16.0;
						TestTargetID = C9AE224B2E61588700E1D2D2;
					};
				};
			};
			buildConfigurationList = C9AE22472E61588700E1D2D2 /* Build configuration list for PBXProject "PartTimeJob" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = C9AE22432E61588700E1D2D2;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = C9AE224D2E61588700E1D2D2 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				C9AE224B2E61588700E1D2D2 /* PartTimeJob */,
				C9AE225B2E61588900E1D2D2 /* PartTimeJobTests */,
				C9AE22652E61588900E1D2D2 /* PartTimeJobUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		C9AE224A2E61588700E1D2D2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9AE225A2E61588900E1D2D2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9AE22642E61588900E1D2D2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		C9AE22482E61588700E1D2D2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9AE22582E61588900E1D2D2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9AE22622E61588900E1D2D2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		C9AE225E2E61588900E1D2D2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C9AE224B2E61588700E1D2D2 /* PartTimeJob */;
			targetProxy = C9AE225D2E61588900E1D2D2 /* PBXContainerItemProxy */;
		};
		C9AE22682E61588900E1D2D2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C9AE224B2E61588700E1D2D2 /* PartTimeJob */;
			targetProxy = C9AE22672E61588900E1D2D2 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		C9AE226E2E61588900E1D2D2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		C9AE226F2E61588900E1D2D2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		C9AE22712E61588900E1D2D2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"PartTimeJob/Preview Content\"";
				DEVELOPMENT_TEAM = DW8HSJN94S;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.knight4.PartTimeJob;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		C9AE22722E61588900E1D2D2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"PartTimeJob/Preview Content\"";
				DEVELOPMENT_TEAM = DW8HSJN94S;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.knight4.PartTimeJob;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		C9AE22742E61588900E1D2D2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DW8HSJN94S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.knight4.PartTimeJobTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PartTimeJob.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PartTimeJob";
			};
			name = Debug;
		};
		C9AE22752E61588900E1D2D2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DW8HSJN94S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.knight4.PartTimeJobTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PartTimeJob.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PartTimeJob";
			};
			name = Release;
		};
		C9AE22772E61588900E1D2D2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DW8HSJN94S;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.knight4.PartTimeJobUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PartTimeJob;
			};
			name = Debug;
		};
		C9AE22782E61588900E1D2D2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DW8HSJN94S;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.knight4.PartTimeJobUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PartTimeJob;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C9AE22472E61588700E1D2D2 /* Build configuration list for PBXProject "PartTimeJob" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C9AE226E2E61588900E1D2D2 /* Debug */,
				C9AE226F2E61588900E1D2D2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C9AE22702E61588900E1D2D2 /* Build configuration list for PBXNativeTarget "PartTimeJob" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C9AE22712E61588900E1D2D2 /* Debug */,
				C9AE22722E61588900E1D2D2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C9AE22732E61588900E1D2D2 /* Build configuration list for PBXNativeTarget "PartTimeJobTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C9AE22742E61588900E1D2D2 /* Debug */,
				C9AE22752E61588900E1D2D2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C9AE22762E61588900E1D2D2 /* Build configuration list for PBXNativeTarget "PartTimeJobUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C9AE22772E61588900E1D2D2 /* Debug */,
				C9AE22782E61588900E1D2D2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = C9AE22442E61588700E1D2D2 /* Project object */;
}
