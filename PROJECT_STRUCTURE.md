# 兼职工作管理 iOS APP - 项目架构说明

## � 项目概述

一个专为兼职工作者设计的 iOS 应用，帮助用户记录、管理和分析兼职工作情况。支持多种工作类型管理、智能统计分析、数据导出等功能。

## �📁 项目文件结构

```
PartTimeJob/
├── PartTimeJobApp.swift          # 应用入口，配置SwiftData
├── ContentView.swift             # 主内容视图
├── Models/                       # 数据模型层
│   ├── WorkRecord.swift         # 工作记录数据模型
│   ├── WorkType.swift           # 工作类型数据模型
│   └── UserPreferences.swift    # 用户偏好设置模型
├── Views/                        # 视图层
│   ├── MainTabView.swift        # 主标签页导航
│   ├── HomeView.swift           # 首页日历视图
│   ├── StatisticsView.swift     # 统计分析视图
│   ├── AddWorkRecordView.swift  # 添加工作记录视图
│   ├── QuickAddWorkView.swift   # 快速添加工作视图
│   ├── WorkRecordDetailView.swift # 工作记录详情视图
│   ├── EditWorkRecordView.swift # 编辑工作记录视图
│   ├── WorkRecordListView.swift # 工作记录列表视图
│   ├── SettingsView.swift       # 设置主视图
│   ├── ProfileEditView.swift    # 个人资料编辑视图
│   ├── ThemeSettingsView.swift  # 主题设置视图
│   ├── NotificationSettingsView.swift # 通知设置视图
│   ├── DataExportView.swift     # 数据导出视图
│   ├── AboutView.swift          # 关于应用视图
│   └── Components/              # 可复用组件
│       ├── StatisticsComponents.swift    # 统计组件库
│       ├── CalendarComponents.swift      # 日历组件库
│       ├── WorkTypePickerView.swift      # 工作类型选择器
│       ├── TimePickerView.swift          # 时间选择器
│       ├── WorkRecordPreviewCard.swift   # 工作记录预览卡片
│       ├── FilterSheet.swift             # 筛选器表单
│       └── CommonComponents.swift        # 通用组件库
├── Services/                     # 服务层
│   ├── DataManager.swift        # 数据管理器（SwiftData）
│   ├── ValidationService.swift  # 数据验证服务
│   ├── DataSyncService.swift    # 数据同步和备份服务
│   ├── CacheService.swift       # 数据缓存服务
│   ├── MigrationService.swift   # 数据库迁移服务
│   └── CalendarDataService.swift # 日历数据预加载服务
├── Utils/                        # 工具类
│   ├── DateHelper.swift         # 日期处理工具
│   ├── SalaryCalculator.swift   # 薪资计算工具
│   ├── NotificationManager.swift # 通知管理工具
│   ├── DataExporter.swift       # 数据导出工具
│   ├── CalendarGestureHandler.swift # 日历手势处理器
│   ├── PerformanceMonitor.swift # 性能监控工具
│   ├── ErrorHandler.swift       # 错误处理系统
│   ├── AppConfiguration.swift   # 应用配置管理
│   └── AppInitializer.swift     # 应用初始化管理
└── Assets.xcassets/             # 资源文件
```

## 🏗️ 架构设计

### MVVM 架构模式

- **Model**: 数据模型（WorkRecord, WorkType, UserPreferences）
- **View**: SwiftUI 视图组件
- **ViewModel**: 通过@EnvironmentObject 注入的 DataManager
- **Service**: 数据访问和业务逻辑服务

### 分层设计

1. **数据层**: SwiftData + SQLite 持久化
2. **服务层**: DataManager 统一数据访问，集成缓存和验证
3. **业务层**: 工具类处理业务逻辑和计算
4. **表现层**: SwiftUI 视图组件和可复用组件库

### 核心服务组件

- **DataManager**: 统一数据访问接口，CRUD 操作
- **ValidationService**: 数据验证、清理、格式化
- **CacheService**: 智能缓存策略，提升性能
- **DataSyncService**: 数据备份和恢复功能
- **MigrationService**: 数据库版本管理和迁移

## 📊 数据模型设计

### WorkRecord（工作记录）

```swift
- id: UUID                    // 唯一标识
- date: Date                  // 工作日期
- startTime: Date             // 开始时间
- endTime: Date               // 结束时间
- workTypeId: UUID            // 工作类型ID
- workDescription: String     // 工作描述
- salary: Double              // 薪资金额
- salaryType: SalaryType      // 薪资类型（时薪/日薪/项目）
- workHours: Double           // 工作时长
- notes: String               // 备注
- createdAt: Date             // 创建时间
- updatedAt: Date             // 更新时间
- isDeleted: Bool             // 软删除标记
```

### WorkType（工作类型）

```swift
- id: UUID                    // 唯一标识
- name: String                // 类型名称
- iconName: String            // 图标名称（SF Symbols）
- colorHex: String            // 主题颜色
- defaultSalary: Double       // 默认薪资
- defaultSalaryType: SalaryType // 默认薪资类型
- description: String         // 描述
- sortOrder: Int              // 排序
- isEnabled: Bool             // 是否启用
- createdAt: Date             // 创建时间
- updatedAt: Date             // 更新时间
- isDeleted: Bool             // 软删除标记
```

### UserPreferences（用户偏好）

```swift
- id: UUID                    // 唯一标识
- userName: String            // 用户姓名
- userEmail: String           // 用户邮箱
- defaultCurrency: String     // 默认货币符号
- weekStartsOnMonday: Bool    // 周开始日期设置
- enableNotifications: Bool   // 通知开关
- themeMode: ThemeMode        // 主题模式
- accentColor: String         // 强调色
```

### SalaryType（薪资类型）

```swift
enum SalaryType: String, CaseIterable {
    case hourly = "hourly"     // 时薪
    case daily = "daily"       // 日薪
    case project = "project"   // 项目薪资
}
```

## 🎨 UI 设计规范

### 色彩体系

- **主色调**: #007AFF（系统蓝）
- **工作类型色彩**:
  - 🔴 兼职直播: #FF3B30
  - 🟡 超市发传单: #FF9500
  - 🟢 家教辅导: #34C759
  - 🔵 外卖配送: #007AFF
  - 🟣 其他工作: #AF52DE

### 组件设计

- **日历网格**: 7x6 网格布局，支持月份切换
- **工作指示器**: 小圆点显示工作类型和密度
- **标签导航**: 首页、统计、添加、类型、设置五个主要功能

### 设计系统

- **响应式布局**: 支持不同设备尺寸的自适应布局
- **统一组件**: 标准化的按钮、卡片、表单组件
- **动画效果**: 流畅的页面切换和交互动画
- **主题支持**: 浅色/深色主题，自定义强调色

## 🔧 核心功能

### 主要功能模块

- **工作记录管理**: 添加、编辑、删除、查看工作记录
- **工作类型管理**: 自定义工作类型，设置图标和颜色
- **日历视图**: 月视图、周视图、列表视图展示工作记录
- **统计分析**: 收入统计、工时分析、效率评估
- **数据导出**: 支持 CSV、Excel、JSON 等格式导出
- **个人设置**: 主题设置、通知设置、个人资料管理
- **数据同步**: 本地数据备份和恢复功能

## 🛠️ 技术栈

- **开发语言**: Swift 5+
- **UI 框架**: SwiftUI
- **数据持久化**: SwiftData + SQLite
- **架构模式**: MVVM
- **最低版本**: iOS 16+
- **图表库**: Swift Charts
- **缓存系统**: 内存缓存 + 智能过期机制
- **数据验证**: 自定义验证服务
- **数据同步**: JSON 备份和恢复
- **版本管理**: 自动数据库迁移

## 📝 开发规范

### 代码规范

- 遵循 Swift 官方编码标准
- 使用清晰的命名规范
- 添加详细的注释说明
- 保持代码结构清晰

### 文件组织

- 按功能模块分组
- 使用 MARK 注释分隔代码段
- 扩展功能使用 Extension
- 预览代码使用 #Preview

### 数据安全

- 使用软删除避免数据丢失
- 记录创建和更新时间
- 数据验证和错误处理
- 事务性操作保证数据一致性

### 性能优化

- 智能缓存策略减少数据库查询
- 懒加载和虚拟化提升列表性能
- 响应式设计适配不同设备
- 内存管理和泄漏检测
