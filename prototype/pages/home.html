<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>首页 - 日历视图</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      /* 自定义样式 */
      .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
        background: #e5e5e5;
      }

      .calendar-cell {
        background: white;
        aspect-ratio: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding: 4px 2px;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
      }

      .calendar-cell:hover {
        background: #f0f9ff;
      }

      .calendar-cell.today {
        background: #dbeafe;
        border: 2px solid #3b82f6;
      }

      .calendar-cell.has-work {
        background: #fef3c7;
      }

      .work-indicator {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin: 1px;
      }

      .work-indicators {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 1px;
        margin-top: 2px;
      }

      /* 工作类型颜色 */
      .work-live {
        background: #ef4444;
      }
      .work-flyer {
        background: #f59e0b;
      }
      .work-tutor {
        background: #10b981;
      }
      .work-delivery {
        background: #3b82f6;
      }
      .work-other {
        background: #8b5cf6;
      }

      /* 底部导航栏 */
      .bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #e5e5e5;
        padding: 8px 0 20px 0;
      }

      .nav-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px;
        color: #9ca3af;
        transition: color 0.2s ease;
      }

      .nav-item.active {
        color: #3b82f6;
      }

      .nav-item i {
        font-size: 20px;
        margin-bottom: 4px;
      }

      .nav-item span {
        font-size: 10px;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <!-- 主容器 -->
    <div class="min-h-screen pb-20">
      <!-- 顶部导航栏 -->
      <div class="bg-white border-b border-gray-200 px-4 py-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <h1 class="text-lg font-semibold text-gray-900">兼职工作记录</h1>
          </div>
          <div class="flex items-center">
            <img
              src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face"
              alt="头像"
              class="w-8 h-8 rounded-full"
            />
          </div>
        </div>
      </div>

      <!-- 月份切换区域 -->
      <div class="bg-white border-b border-gray-200 px-4 py-4">
        <div class="flex items-center justify-between">
          <button class="p-2 rounded-full hover:bg-gray-100 transition-colors">
            <i class="fas fa-chevron-left text-gray-600"></i>
          </button>
          <h2 class="text-xl font-bold text-gray-900">2025年8月</h2>
          <button class="p-2 rounded-full hover:bg-gray-100 transition-colors">
            <i class="fas fa-chevron-right text-gray-600"></i>
          </button>
        </div>
      </div>

      <!-- 星期标题行 -->
      <div class="bg-gray-50 border-b border-gray-200">
        <div class="grid grid-cols-7 text-center py-2">
          <div class="text-sm font-medium text-gray-500">日</div>
          <div class="text-sm font-medium text-gray-500">一</div>
          <div class="text-sm font-medium text-gray-500">二</div>
          <div class="text-sm font-medium text-gray-500">三</div>
          <div class="text-sm font-medium text-gray-500">四</div>
          <div class="text-sm font-medium text-gray-500">五</div>
          <div class="text-sm font-medium text-gray-500">六</div>
        </div>
      </div>

      <!-- 日历主体 -->
      <div class="calendar-grid">
        <!-- 第一周 -->
        <div class="calendar-cell text-gray-400">28</div>
        <div class="calendar-cell text-gray-400">29</div>
        <div class="calendar-cell text-gray-400">30</div>
        <div class="calendar-cell text-gray-400">31</div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">1</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">2</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">3</span>
        </div>

        <!-- 第二周 -->
        <div class="calendar-cell">
          <span class="text-sm font-medium">4</span>
        </div>
        <div class="calendar-cell has-work">
          <span class="text-sm font-medium">5</span>
          <div class="work-indicators">
            <div class="work-indicator work-tutor"></div>
          </div>
          <span class="text-xs text-gray-500 mt-1">家教</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">6</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">7</span>
        </div>
        <div class="calendar-cell has-work">
          <span class="text-sm font-medium">8</span>
          <div class="work-indicators">
            <div class="work-indicator work-delivery"></div>
          </div>
          <span class="text-xs text-gray-500 mt-1">外卖</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">9</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">10</span>
        </div>

        <!-- 第三周 -->
        <div class="calendar-cell">
          <span class="text-sm font-medium">11</span>
        </div>
        <div class="calendar-cell has-work">
          <span class="text-sm font-medium">12</span>
          <div class="work-indicators">
            <div class="work-indicator work-tutor"></div>
          </div>
          <span class="text-xs text-gray-500 mt-1">家教</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">13</span>
        </div>
        <div class="calendar-cell has-work">
          <span class="text-sm font-medium">14</span>
          <div class="work-indicators">
            <div class="work-indicator work-flyer"></div>
          </div>
          <span class="text-xs text-gray-500 mt-1">传单</span>
        </div>
        <div class="calendar-cell has-work">
          <span class="text-sm font-medium">15</span>
          <div class="work-indicators">
            <div class="work-indicator work-live"></div>
            <div class="work-indicator work-delivery"></div>
          </div>
          <span class="text-xs text-gray-500 mt-1">多项</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">16</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">17</span>
        </div>

        <!-- 第四周 -->
        <div class="calendar-cell">
          <span class="text-sm font-medium">18</span>
        </div>
        <div class="calendar-cell has-work">
          <span class="text-sm font-medium">19</span>
          <div class="work-indicators">
            <div class="work-indicator work-tutor"></div>
          </div>
          <span class="text-xs text-gray-500 mt-1">家教</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">20</span>
        </div>
        <div class="calendar-cell has-work">
          <span class="text-sm font-medium">21</span>
          <div class="work-indicators">
            <div class="work-indicator work-live"></div>
          </div>
          <span class="text-xs text-gray-500 mt-1">直播</span>
        </div>
        <div class="calendar-cell has-work">
          <span class="text-sm font-medium">22</span>
          <div class="work-indicators">
            <div class="work-indicator work-live"></div>
          </div>
          <span class="text-xs text-gray-500 mt-1">直播</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">23</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">24</span>
        </div>

        <!-- 第五周 -->
        <div class="calendar-cell">
          <span class="text-sm font-medium">25</span>
        </div>
        <div class="calendar-cell has-work">
          <span class="text-sm font-medium">26</span>
          <div class="work-indicators">
            <div class="work-indicator work-tutor"></div>
          </div>
          <span class="text-xs text-gray-500 mt-1">家教</span>
        </div>
        <div class="calendar-cell today">
          <span class="text-sm font-medium">27</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">28</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">29</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">30</span>
        </div>
        <div class="calendar-cell">
          <span class="text-sm font-medium">31</span>
        </div>
      </div>

      <!-- 快速操作区域 -->
      <div class="bg-white border-t border-gray-200 p-4 mt-4">
        <div class="flex gap-3">
          <button
            class="flex-1 bg-blue-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-600 transition-colors"
          >
            <i class="fas fa-plus mr-2"></i>快速添加工作
          </button>
        </div>
      </div>

      <!-- 工作类型说明 -->
      <div class="bg-white border-t border-gray-200 p-4">
        <h3 class="text-sm font-medium text-gray-700 mb-3">工作类型</h3>
        <div class="flex flex-wrap gap-3">
          <div class="flex items-center">
            <div class="w-3 h-3 rounded-full work-live mr-2"></div>
            <span class="text-xs text-gray-600">兼职直播</span>
          </div>
          <div class="flex items-center">
            <div class="w-3 h-3 rounded-full work-flyer mr-2"></div>
            <span class="text-xs text-gray-600">发传单</span>
          </div>
          <div class="flex items-center">
            <div class="w-3 h-3 rounded-full work-tutor mr-2"></div>
            <span class="text-xs text-gray-600">家教辅导</span>
          </div>
          <div class="flex items-center">
            <div class="w-3 h-3 rounded-full work-delivery mr-2"></div>
            <span class="text-xs text-gray-600">外卖配送</span>
          </div>
          <div class="flex items-center">
            <div class="w-3 h-3 rounded-full work-other mr-2"></div>
            <span class="text-xs text-gray-600">其他工作</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bottom-nav">
      <div class="flex">
        <!-- 点击跳转到首页 -->
        <div class="nav-item active">
          <i class="fas fa-calendar-alt"></i>
          <span>日历</span>
        </div>

        <!-- 点击跳转到统计页面 -->
        <div class="nav-item">
          <i class="fas fa-chart-bar"></i>
          <span>统计</span>
        </div>
        <!-- 点击跳转到我的页面 -->
        <div class="nav-item">
          <i class="fas fa-user"></i>
          <span>我的</span>
        </div>
      </div>
    </div>

    <script>
      // 日历交互功能
      document.addEventListener("DOMContentLoaded", function () {
        // 日期点击事件
        document.querySelectorAll(".calendar-cell").forEach((cell) => {
          cell.addEventListener("click", function () {
            const date = this.querySelector("span").textContent;
            if (this.classList.contains("has-work")) {
              alert(`查看 ${date} 日的工作详情`);
            } else {
              alert(`为 ${date} 日添加工作记录`);
            }
          });
        });

        // 快速添加按钮
        document
          .querySelector(".bg-blue-500")
          .addEventListener("click", function () {
            alert("打开添加工作记录页面");
          });
      });
    </script>
  </body>
</html>
