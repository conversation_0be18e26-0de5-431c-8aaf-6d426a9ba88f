<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>个人设置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      /* 自定义样式 */
      .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 24px 16px;
        color: white;
      }

      .avatar-container {
        position: relative;
        display: inline-block;
      }

      .avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        border: 4px solid rgba(255, 255, 255, 0.2);
      }

      .avatar-edit {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 24px;
        height: 24px;
        background: #3b82f6;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid white;
      }

      .setting-section {
        background: white;
        margin-bottom: 12px;
        border-radius: 12px;
        overflow: hidden;
      }

      .setting-item {
        display: flex;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #f3f4f6;
        cursor: pointer;
        transition: background-color 0.2s ease;
      }

      .setting-item:last-child {
        border-bottom: none;
      }

      .setting-item:hover {
        background: #f9fafb;
      }

      .setting-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: white;
      }

      .setting-content {
        flex: 1;
      }

      .setting-title {
        font-weight: 500;
        color: #111827;
        margin-bottom: 2px;
      }

      .setting-subtitle {
        font-size: 14px;
        color: #6b7280;
      }

      .setting-arrow {
        color: #9ca3af;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
        margin: 20px 0;
      }

      .stat-item {
        text-align: center;
      }

      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: white;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8);
      }

      .switch {
        position: relative;
        display: inline-block;
        width: 44px;
        height: 24px;
      }

      .switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: 0.4s;
        border-radius: 24px;
      }

      .slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: 0.4s;
        border-radius: 50%;
      }

      input:checked + .slider {
        background-color: #3b82f6;
      }

      input:checked + .slider:before {
        transform: translateX(20px);
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <!-- 主容器 -->
    <div class="min-h-screen pb-20">
      <!-- 个人信息头部 -->
      <div class="profile-header">
        <!-- 统计数据 -->
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">12</div>
            <div class="stat-label">本月工作天数</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">¥3,240</div>
            <div class="stat-label">本月收入</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">68.5h</div>
            <div class="stat-label">本月工时</div>
          </div>
        </div>
      </div>

      <!-- 设置选项 -->
      <div class="p-4">
        <!-- 工作管理 -->
        <div class="setting-section">
          <div class="setting-item" onclick="navigateTo('work-types')">
            <div class="setting-icon" style="background: #10b981">
              <i class="fas fa-briefcase"></i>
            </div>
            <div class="setting-content">
              <div class="setting-title">工作类型管理</div>
              <div class="setting-subtitle">管理你的工作类型和薪资标准</div>
            </div>
            <i class="fas fa-chevron-right setting-arrow"></i>
          </div>

          <div class="setting-item">
            <div class="setting-icon" style="background: #3b82f6">
              <i class="fas fa-bell"></i>
            </div>
            <div class="setting-content">
              <div class="setting-title">提醒设置</div>
              <div class="setting-subtitle">工作提醒和记录提醒</div>
            </div>
            <label class="switch">
              <input type="checkbox" checked />
              <span class="slider"></span>
            </label>
          </div>

          <div class="setting-item">
            <div class="setting-icon" style="background: #f59e0b">
              <i class="fas fa-calendar-week"></i>
            </div>
            <div class="setting-content">
              <div class="setting-title">工作模板</div>
              <div class="setting-subtitle">创建常用的工作记录模板</div>
            </div>
            <i class="fas fa-chevron-right setting-arrow"></i>
          </div>
        </div>

        <!-- 关于 -->
        <div class="setting-section">
          <div class="setting-item">
            <div class="setting-icon" style="background: #6b7280">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="setting-content">
              <div class="setting-title">关于应用</div>
              <div class="setting-subtitle">版本 1.0.0</div>
            </div>
            <i class="fas fa-chevron-right setting-arrow"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div
      class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-0 py-2"
    >
      <div class="flex">
        <!-- 点击跳转到首页 -->
        <div class="flex-1 flex flex-col items-center py-2 text-gray-400">
          <i class="fas fa-calendar-alt text-xl mb-1"></i>
          <span class="text-xs">日历</span>
        </div>
        <!-- 点击跳转到统计页面 -->
        <div class="flex-1 flex flex-col items-center py-2 text-gray-400">
          <i class="fas fa-chart-bar text-xl mb-1"></i>
          <span class="text-xs">统计</span>
        </div>
        <!-- 点击跳转到我的页面 -->
        <div class="flex-1 flex flex-col items-center py-2 text-blue-500">
          <i class="fas fa-user text-xl mb-1"></i>
          <span class="text-xs">我的</span>
        </div>
      </div>
    </div>

    <script>
      // 页面导航功能
      function navigateTo(page) {
        switch (page) {
          case "work-types":
            alert("跳转到工作类型管理页面");
            break;
          default:
            alert(`跳转到${page}页面`);
        }
      }

      // 设置项点击事件
      document.querySelectorAll(".setting-item").forEach((item) => {
        if (!item.querySelector(".switch")) {
          item.addEventListener("click", function () {
            const title = this.querySelector(".setting-title").textContent;

            // 特殊处理某些设置项
            if (title === "清空数据") {
              if (confirm("确定要清空所有数据吗？此操作不可恢复！")) {
                alert("数据已清空");
              }
            } else if (title === "数据导出") {
              alert("开始导出数据...");
            } else if (title === "意见反馈") {
              alert("打开反馈页面");
            } else if (title === "帮助中心") {
              alert("打开帮助中心");
            } else {
              alert(`打开${title}设置`);
            }
          });
        }
      });

      // 头像编辑功能
      document
        .querySelector(".avatar-edit")
        .addEventListener("click", function (e) {
          e.stopPropagation();
          alert("选择新头像");
        });

      // 退出登录
      document
        .querySelector(".bg-red-500")
        .addEventListener("click", function () {
          if (confirm("确定要退出登录吗？")) {
            alert("已退出登录");
          }
        });

      // 开关切换事件
      document.querySelectorAll(".switch input").forEach((switchInput) => {
        switchInput.addEventListener("change", function () {
          const settingTitle =
            this.closest(".setting-item").querySelector(
              ".setting-title"
            ).textContent;
          const isEnabled = this.checked;
          console.log(`${settingTitle}: ${isEnabled ? "开启" : "关闭"}`);
        });
      });
    </script>
  </body>
</html>
